<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sankuai.mdp</groupId>
        <artifactId>dzviewscene-dealshelf-home</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>dzviewscene-dealshelf-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>dzviewscene-dealshelf-service</name>
    <properties>
        <env>qa</env>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-common-filter</artifactId>
            <version>1.1.5</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>magic-member-degrade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.price.operation</groupId>
            <artifactId>price-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.deal.dealbasic</groupId>
            <artifactId>dealbasic-thrift</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpcontent.feeds</groupId>
            <artifactId>mpcontent-feeds-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-resource-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.publish</groupId>
            <artifactId>mpproduct-publish-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-meta-tag-manage-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>apollo-brand-proposal-external-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.plugin</groupId>
            <artifactId>pp-misc-trade-api</artifactId>
            <version>1.0.14</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dzviewscene-productdegrade-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-booking-process-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.joy</groupId>
                    <artifactId>joy-common-resource</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>2.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-core-thrift</artifactId>
            <version>0.3.14</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-query-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-process-thrift</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctexhibit</groupId>
            <artifactId>mpmctexhibit-common</artifactId>
            <version>0.3.10</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-ping-api</artifactId>
            <version>1.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-history-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.medicalcosmetology</groupId>
            <artifactId>offline-code-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-scene-engine-api</artifactId>
            <version>1.39</version>
        </dependency>
        <!--商场会员相关依赖-->
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-common</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-thrift</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-bizhour-common</artifactId>
            <version>1.0.18</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.poi</groupId>
                    <artifactId>poi-bizhour-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-bizhour-api</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzshop-intervention-api</artifactId>
            <version>0.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>promotion-api</artifactId>
            <version>2.0.41</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.180</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-dealtransform-api</artifactId>
            <version>1.0.16</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>bird-product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>tpfun-refund-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>tpfun-checkout-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-privilege-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>communitylife-api</artifactId>
            <version>0.9.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mobile-base-datatypes</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.maoyan</groupId>
            <artifactId>maoyan-shplatform-content-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.maoyan</groupId>
            <artifactId>maoyan-show-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-booking-api</artifactId>
            <version>1.1.21</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.user</groupId>
            <artifactId>thirdinfo-api</artifactId>
            <version>1.0.17</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-manage-api</artifactId>
            <version>2.7.55</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-stocklogic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>tpfun-product-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.tpfun</groupId>
                    <artifactId>sku-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.joynav</groupId>
            <artifactId>joynav-rb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-common</artifactId>
            <version>1.9.6</version>
        </dependency>
        <!--注意:高版本和斗斛client冲突-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.4.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-clove-api</artifactId>
            <version>1.3.1.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>home-shop-fusion-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.adp.ads.fe</groupId>
            <artifactId>fe-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.adpseadbiz.bizer</groupId>
            <artifactId>bizer-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.dataapp.ads</groupId>
            <artifactId>adsapilib</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.ads.as</groupId>
                    <artifactId>new-ads-as-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>scribe-log4j</artifactId>
                    <groupId>com.meituan.scribe</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>dztrade-order-business-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dztrade-refund-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>baby-operation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.dzopen</groupId>
            <artifactId>dzopen-gateway-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.dzopen</groupId>
                    <artifactId>dzopen-core-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-proxy-fitness-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.57</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dzviewscene-productdisplay-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.ugccontent</groupId>
                    <artifactId>ugccontent-write-module-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-category-process-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-stock-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-solution-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.joy</groupId>
                    <artifactId>joy-common-resource</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.dzopen</groupId>
            <artifactId>dzopen-aggregate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-product-nr</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>om.dianping.deal</groupId>
                    <artifactId>deal-meta-tag-manage-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.joy</groupId>
                    <artifactId>joy-stock-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.deal</groupId>
                    <artifactId>product-shelf-query-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.deal</groupId>
                    <artifactId>deal-shop-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dztheme-massagebook-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-idmapper-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.medicine</groupId>
            <artifactId>carnation-corepath-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>carnation-biz-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>mobile-oss-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-underlayer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>joycard-navigation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-activ-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-activity-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.72</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.dppoi</groupId>
                    <artifactId>dp-poi-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.user</groupId>
            <artifactId>account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dztheme-backroombook-api</artifactId>
            <version>0.0.12</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-oss-api</artifactId>
            <version>1.1.3.25</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-logger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.finance</groupId>
            <artifactId>xy-trade-installment-thrift-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>graphql-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-sdk-shopuuid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.hsr</groupId>
            <artifactId>geohash</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dp.search</groupId>
            <artifactId>mainshop-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.sf.trove4j</groupId>
                    <artifactId>trove4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gateway-framework-client3</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.vc</groupId>
                    <artifactId>vc-degrade-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gateway-framework-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>ops-remote-host</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.vc</groupId>
                    <artifactId>vc-degrade-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-common-filter</artifactId>
                    <groupId>com.dianping.merchant</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gateway-framework-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mapi-shell</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-common-filter</artifactId>
                    <groupId>com.dianping.merchant</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dzd-delivery-api</artifactId>
            <version>0.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-abtest-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-log-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.idservice</groupId>
            <artifactId>idservice-sdk</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mobile-base-datatypes</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-gis-api</artifactId>
            <version>0.4.17</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.1.45</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.campaign</groupId>
            <artifactId>proxy-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.midas</groupId>
            <artifactId>baymax-ad-bizer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.midas</groupId>
            <artifactId>baymax-mt-bizer-api-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dp.search</groupId>
            <artifactId>search-tohome-interface</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-stock-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dz-customer-api</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.ktv</groupId>
            <artifactId>ktv-shop-api</artifactId>
            <version>1.3.9</version>
        </dependency>
        <dependency>
            <groupId>com.maoyan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.stid</groupId>
            <artifactId>stid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-octo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>aqc-license-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.merchant</groupId>
            <artifactId>merchant-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.poi</groupId>
            <artifactId>sinai.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>junglepoi.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.group</groupId>
            <artifactId>groupbase</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>search-adx-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-activity-usersign-api</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-operate-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-merchant-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-logic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-marketing-nr</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.gmkt</groupId>
                    <artifactId>gmkt-activity-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-mq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-plan-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-volume-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-pagoda-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-base-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-testing-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.joynav</groupId>
            <artifactId>joynav-navigation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-bom</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-poi-nr</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <version>1.1.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.appkit</groupId>
                    <artifactId>appkit-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.appkit</groupId>
            <artifactId>appkit-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.lion</groupId>
                    <artifactId>lion-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gmm-investment-data-tool-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vc-operate-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-common-validation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>ops-remote-host</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>ops-remote-host</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.reactivex.rxjava2</groupId>
            <artifactId>rxjava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-metrics-event-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair3-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>avatar-tracker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>pigeon-octo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-consumerclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-producerclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-union-log</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ops-remote-host</artifactId>
            <version>1.1.0-IPV6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-rgc-api</artifactId>
            <version>0.1.38</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.general</groupId>
            <artifactId>martgeneral-recommend-api</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.15</version>
        </dependency>
        <!--团单商户推荐-->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-publish-category-api</artifactId>
        </dependency>
        <!--货架关键词搜索-->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-rule-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>joy-general-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.gmkt</groupId>
                    <artifactId>gmkt-activity-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>dzsku-owner-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>collectiondetail</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dzviewscene-productshelf-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.mobile</groupId>
            <artifactId>mapi-index-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-guesslike-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
        </dependency>
        <!--点评前台城市商圈接口-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gis-api</artifactId>
        </dependency>
        <!--美团前台城市商圈接口-->
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>groupgeo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--医美方案查询接口-->
        <dependency>
            <groupId>carnation-corepath-server</groupId>
            <artifactId>carnation-industry-api</artifactId>
        </dependency>
        <!--收藏-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-remote</artifactId>
        </dependency>
        <!--ugc-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>review-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-base-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-proxy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.education</groupId>
            <artifactId>education-teacher-domain-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>education-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>education-admin-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-zone-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-zone-biz-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-zone-annotation</artifactId>
        </dependency>
        <!--IM信息-->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>cliententry-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-navigation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.search.aladdin</groupId>
            <artifactId>aladdin-dsl</artifactId>
            <version>0.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-api</artifactId>
            <version>2.7.46</version>
        </dependency>
        <!--squirrel-async-client- -->
        <!--squirrel-async-client- -->
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-async-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>shop-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache</groupId>
                    <artifactId>libthrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.swan.udqs</groupId>
            <artifactId>Swan-udqs-api</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.93</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.piccentercloud</groupId>
                    <artifactId>piccenter-display-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctpoiext</groupId>
            <artifactId>mpmctpoiext-info-query-thrift</artifactId>
            <version>1.0.29</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.mpmctmvacommon</groupId>
                    <artifactId>mpmctmvacommon-resource</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.ktv</groupId>
            <artifactId>ktv-gather-api</artifactId>
            <version>0.1.30</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>dztrade-purchase-api</artifactId>
            <version>0.1.10</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.dztrade</groupId>
            <artifactId>dztrade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.trade</groupId>
            <artifactId>mpproduct-trade-api</artifactId>
            <!--上线前改为正式版本-->
            <version>1.7.0</version>
        </dependency>
        <!--资源查询-->
        <dependency>
            <groupId>com.sankuai.nibpt</groupId>
            <artifactId>nibpt-transparent-validator</artifactId>
            <version>1.0.7</version>
        </dependency>
        <!--标品评分-->
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-launch-api</artifactId>
            <version>0.0.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--美团收藏-->
        <dependency>
            <groupId>com.sankuai.user.collection</groupId>
            <artifactId>coll-client</artifactId>
            <version>1.5.8</version>
        </dependency>
        <!--点评收藏-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mapi-usercenter-api</artifactId>
            <version>0.0.20</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-framework-viewscene-client</artifactId>
            <version>0.4.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-rpc</artifactId>
            <version>0.0.40</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-definition</artifactId>
            <version>0.0.27</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-config</artifactId>
            <version>0.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>wed-business-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibmp</groupId>
            <artifactId>decorate-media-query-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>health-sc-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.takeaway</groupId>
            <artifactId>takeaway-open-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.waimai</groupId>
                    <artifactId>waimai_util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.takeaway</groupId>
                    <artifactId>takeaway-common-setrouter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.geronimo.bundles</groupId>
                    <artifactId>aspectjweaver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dzshoplist-rank-api</artifactId>
            <version>0.0.22</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!--UGC评价页-->
        <dependency>
            <groupId>com.dianping.ugccontent</groupId>
            <artifactId>ugccontent-write-module-api</artifactId>
            <version>0.9.19</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.service.inf</groupId>
                    <artifactId>kms-java-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mapi-shell</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dzviewscene-dealshelf-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-open-data-api</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mtstore</groupId>
            <artifactId>mtstore-core-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mtstore</groupId>
            <artifactId>mtstore-aggregate-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mtstore</groupId>
            <artifactId>mtstore-aggregate-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mppack.product</groupId>
            <artifactId>mppack-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.carnation</groupId>
            <artifactId>carnation-distribution-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>timescard-exposure-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-times-card-api</artifactId>
            <version>2.0.95</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.web.deal.deallistapi</groupId>
            <artifactId>deallistapi-client</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-tag-apply-api</artifactId>
            <version>1.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-fitness-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>beautycontent.creator.api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-count-thrift</artifactId>
            <version>0.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-process-thrift</artifactId>
            <version>0.1.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.hotel.biz</groupId>
                    <artifactId>hotel-resource-name-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>wm_experiment_platform_sdk2</artifactId>
            <version>1.0.4-marketing</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai-thrift-tools</artifactId>
            <version>1.4.12.1.30</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.order.route</groupId>
            <artifactId>stenv-routing</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibscp.common</groupId>
            <artifactId>flow-identify-sdk</artifactId>
            <version>1.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-fulfill-api</artifactId>
            <version>0.0.21</version>
        </dependency>
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mppack.product</groupId>
            <artifactId>mppack-product-client</artifactId>
            <version>1.1.34</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzviewscene-intention-api</artifactId>
            <version>0.0.5</version>
        </dependency>

        <!-- 手艺人信息查询 -->
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-common-api</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
            <version>0.3.9</version>
        </dependency>
        <!-- 权益计算服务 -->
        <dependency>
            <groupId>com.sankuai.interest</groupId>
            <artifactId>interest-core-thrift</artifactId>
            <version>0.0.23</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fbi</groupId>
            <artifactId>faas-wed-api</artifactId>
            <version>1.0.8</version>
        </dependency>
        <!-- 保洁自营门店  -->
        <dependency>
            <groupId>com.sankuai.lifeevent</groupId>
            <artifactId>reserve-rpc-api</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.feitianplus.data.onedata</groupId>
            <artifactId>data-onedata-api</artifactId>
            <version>1.0.37</version>
        </dependency>

        <!-- 团单类目ID转换 -->
        <dependency>
            <groupId>com.meituan.nibscp.domain</groupId>
            <artifactId>scp-accessory-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.nibscp.common</groupId>
                    <artifactId>scp-common-utils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>dzviewscene-unified-shelf-operator-api</artifactId>
            <groupId>com.sankuai.dzviewscene</groupId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-star-apply-api</artifactId>
            <version>0.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fbi</groupId>
            <artifactId>merchant-miniprogram-registration-api</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fincreditpay</groupId>
            <artifactId>bnpl-client</artifactId>
            <version>1.0.24</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
            <version>0.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide</groupId>
            <artifactId>product-insight-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-${env}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration/>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changelog-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.societegenerale.commons</groupId>
                <artifactId>arch-unit-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.0.7.RELEASE</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <layoutFactory implementation="com.sankuai.athena.boot.loader.AthenaLayoutFactory"/>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>athena-home</groupId>
                                <artifactId>athena-boot-loader</artifactId>
                                <version>1.0.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
