package com.sankuai.common.utils;

import static org.junit.Assert.*;
import com.sankuai.dztheme.deal.res.standardservice.*;
import com.sankuai.dztheme.deal.res.structured.*;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for DealAttrUtils.getAttrFromStandardServiceProject method
 */
@RunWith(MockitoJUnitRunner.class)
public class DealAttrUtilsTest {

    /**
     * Helper method to create a group with specified attribute name and value
     */
    private DealStandardServiceProjectGroupDTO createGroupWithValue(String attrName, String attrValue) {
        DealStandardServiceProjectGroupDTO group = new DealStandardServiceProjectGroupDTO();
        DealStandardServiceProjectItemDTO item = new DealStandardServiceProjectItemDTO();
        DealStandardAttributeDTO attribute = new DealStandardAttributeDTO();
        DealStandardAttributeItemDTO attrItem = new DealStandardAttributeItemDTO();
        DealStandardAttributeValueDTO valueDTO = new DealStandardAttributeValueDTO();
        valueDTO.setSimpleValues(Arrays.asList(attrValue));
        attrItem.setAttrName(attrName);
        attrItem.setAttrValues(Arrays.asList(valueDTO));
        attribute.setAttrs(Arrays.asList(attrItem));
        item.setStandardAttribute(attribute);
        group.setServiceProjectItems(Arrays.asList(item));
        return group;
    }

    /**
     * Test when standardServiceProjectDTO is null
     */
    @Test
    public void testGetAttrFromStandardServiceProject_NullDTO() {
        // arrange
        DealStandardServiceProjectDTO dto = null;
        String attrName = "testAttr";
        // act
        String result = DealAttrUtils.getAttrFromStandardServiceProject(dto, attrName);
        // assert
        assertNull(result);
    }

    /**
     * Test when mustGroups is empty and optionalGroups has value
     */
    @Test
    public void testGetAttrFromStandardServiceProject_EmptyMustGroupsValidOptional() {
        // arrange
        DealStandardServiceProjectDTO dto = new DealStandardServiceProjectDTO();
        dto.setMustGroups(Collections.emptyList());
        DealStandardServiceProjectGroupDTO optionalGroup = createGroupWithValue("testAttr", "testValue");
        dto.setOptionalGroups(Collections.singletonList(optionalGroup));
        // act
        String result = DealAttrUtils.getAttrFromStandardServiceProject(dto, "testAttr");
        // assert
        assertEquals("testValue", result);
    }

    /**
     * Test when mustGroups has value that matches
     */
    @Test
    public void testGetAttrFromStandardServiceProject_ValidMustGroups() {
        // arrange
        DealStandardServiceProjectDTO dto = new DealStandardServiceProjectDTO();
        DealStandardServiceProjectGroupDTO mustGroup = createGroupWithValue("testAttr", "mustValue");
        dto.setMustGroups(Collections.singletonList(mustGroup));
        DealStandardServiceProjectGroupDTO optionalGroup = createGroupWithValue("testAttr", "optionalValue");
        dto.setOptionalGroups(Collections.singletonList(optionalGroup));
        // act
        String result = DealAttrUtils.getAttrFromStandardServiceProject(dto, "testAttr");
        // assert
        assertEquals("mustValue", result);
    }

    /**
     * Test when mustGroups returns blank and optionalGroups has value
     */
    @Test
    public void testGetAttrFromStandardServiceProject_BlankMustGroupsValidOptional() {
        // arrange
        DealStandardServiceProjectDTO dto = new DealStandardServiceProjectDTO();
        DealStandardServiceProjectGroupDTO mustGroup = createGroupWithValue("differentAttr", "mustValue");
        dto.setMustGroups(Collections.singletonList(mustGroup));
        DealStandardServiceProjectGroupDTO optionalGroup = createGroupWithValue("testAttr", "optionalValue");
        dto.setOptionalGroups(Collections.singletonList(optionalGroup));
        // act
        String result = DealAttrUtils.getAttrFromStandardServiceProject(dto, "testAttr");
        // assert
        assertEquals("optionalValue", result);
    }

    /**
     * Test scenario: Initialize result as null and process valid mustGroups
     */
    @Test
    public void testGetAttrFromDealDetailStructured_WithValidMustGroups() {
        // arrange
        DealDetailStructuredDTO dto = new DealDetailStructuredDTO();
        DealMustSkuItemsGroupDTO mustGroup = new DealMustSkuItemsGroupDTO();
        DealSkuItemDTO skuItem = new DealSkuItemDTO();
        DealSkuAttrItemDTO attrItem = new DealSkuAttrItemDTO();
        attrItem.setAttrName("testAttr");
        attrItem.setAttrValue("testValue");
        skuItem.setAttrItems(Arrays.asList(attrItem));
        mustGroup.setSkuItems(Arrays.asList(skuItem));
        dto.setMustGroups(Arrays.asList(mustGroup));
        // act
        String result = DealAttrUtils.getAttrFromDealDetailStructured(dto, "testAttr");
        // assert
        assertEquals("testValue", result);
    }

    /**
     * Test scenario: Process empty mustGroups and check optionalGroups
     */
    @Test
    public void testGetAttrFromDealDetailStructured_EmptyMustGroups() {
        // arrange
        DealDetailStructuredDTO dto = new DealDetailStructuredDTO();
        dto.setMustGroups(Collections.emptyList());
        dto.setOptionalGroups(Collections.emptyList());
        // act
        String result = DealAttrUtils.getAttrFromDealDetailStructured(dto, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test scenario: Test null group and empty skuItems in mustGroups
     */
    @Test
    public void testGetAttrFromDealDetailStructured_NullGroupAndEmptySkuItems() {
        // arrange
        DealDetailStructuredDTO dto = new DealDetailStructuredDTO();
        DealMustSkuItemsGroupDTO mustGroup = new DealMustSkuItemsGroupDTO();
        mustGroup.setSkuItems(Collections.emptyList());
        dto.setMustGroups(Arrays.asList(null, mustGroup));
        // act
        String result = DealAttrUtils.getAttrFromDealDetailStructured(dto, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test scenario: Test attribute name matching in stream operations
     */
    @Test
    public void testGetAttrFromDealDetailStructured_AttributeNameMatching() {
        // arrange
        DealDetailStructuredDTO dto = new DealDetailStructuredDTO();
        DealMustSkuItemsGroupDTO mustGroup = new DealMustSkuItemsGroupDTO();
        DealSkuItemDTO skuItem = new DealSkuItemDTO();
        DealSkuAttrItemDTO attrItem1 = new DealSkuAttrItemDTO();
        DealSkuAttrItemDTO attrItem2 = new DealSkuAttrItemDTO();
        attrItem1.setAttrName("wrongAttr");
        attrItem1.setAttrValue("wrongValue");
        attrItem2.setAttrName("testAttr");
        attrItem2.setAttrValue("correctValue");
        skuItem.setAttrItems(Arrays.asList(attrItem1, attrItem2));
        mustGroup.setSkuItems(Arrays.asList(skuItem));
        dto.setMustGroups(Arrays.asList(mustGroup));
        // act
        String result = DealAttrUtils.getAttrFromDealDetailStructured(dto, "testAttr");
        // assert
        assertEquals("correctValue", result);
    }
}
