package com.sankuai.dzviewscene.productdetail.edu;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.business.detail.edu.context.UgcContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2020/10/29 10:15 上午
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.business.detail.edu.context","com.sankuai.dzviewscene.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class UgcContextIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(UgcContextIntegTest.class);

    @Resource
    private UgcContext context;

    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_01() {
        ActivityContext activityContext = buildContext();
        Map<String, String> result = context.contextExt(activityContext).join();
        logger.info("结果为：" + JsonCodec.encodeWithUTF8(result));
    }

    private ActivityContext buildContext() {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.platform, 2);
        activityContext.addParam(ShelfActivityConstants.Params.dpUserId, 757581398L);
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, 1591904784);
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiId, 1639651);
        activityContext.addParam(ShelfActivityConstants.Params.shopUuid, "H2ONJvMN5C8lnBco");
        return activityContext;
    }

}
