package com.sankuai.dzviewscene.productshelf.filterlist.productlist;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.BarSimilarShelfPathFilterAcsOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.testng.collections.Lists;

import java.math.BigDecimal;
import java.util.List;

public class SimilarProductFilterAscTest {

    BarSimilarShelfPathFilterAcsOpt opt;

    BarSimilarShelfPathFilterAcsOpt.Config config;

    private static final String SERVICE_TYPE = "service_type";

    private static final String PACKAGE_NUMBER_OF_PKG = "number_of_pkg";
    @BeforeEach
    void setUp() {
        opt = new BarSimilarShelfPathFilterAcsOpt();
        config = new BarSimilarShelfPathFilterAcsOpt.Config();
    }

    @Test
    @DisplayName("相似团购召回和排序")
    void testAcsLimit() {

        ActivityCxt context=new ActivityCxt();
        context.addParam("pageSource","shelf");
        context.addParam("entityId",3);


        List<ProductM> productMS= Lists.newArrayList();


        ProductM productM2 = new ProductM();
        productM2.setProductId(2);
        productM2.setAttr(SERVICE_TYPE,"酒水");
        productM2.setBasePrice(new BigDecimal(60));
        productM2.setAttr(PACKAGE_NUMBER_OF_PKG,"2-3人");
        productMS.add(productM2);

        ProductM productM3 = new ProductM();
        productM3.setProductId(3);
        productM3.setAttr(SERVICE_TYPE,"酒水小食套餐");
        productM3.setAttr(PACKAGE_NUMBER_OF_PKG,"8人");
        productM3.setBasePrice(new BigDecimal(60));
        productMS.add(productM3);

        ProductM productM4 = new ProductM();
        productM4.setProductId(4);
        productM4.setAttr(SERVICE_TYPE,"餐食");
        productM4.setBasePrice(new BigDecimal(63));
        productMS.add(productM4);

        ProductM productM5 = new ProductM();
        productM5.setProductId(5);
        productM5.setAttr(SERVICE_TYPE,"门票入场券");
        productM5.setBasePrice(new BigDecimal(60));
        productMS.add(productM5);

        ProductM productM6 = new ProductM();
        productM6.setProductId(6);
        productM6.setAttr(SERVICE_TYPE,"酒水小食套餐");
        productM6.setAttr(PACKAGE_NUMBER_OF_PKG,"2-3人");
        productM6.setBasePrice(new BigDecimal(67));
        productMS.add(productM6);

        ProductM productM7 = new ProductM();
        productM7.setProductId(7);
        productM7.setAttr(SERVICE_TYPE,"酒水小食套餐");
        productM7.setAttr(PACKAGE_NUMBER_OF_PKG,"6人以上");
        productM7.setBasePrice(new BigDecimal(66));
        productMS.add(productM7);

        ProductM productM1 = new ProductM();
        productM1.setProductId(1);
        productM1.setAttr(SERVICE_TYPE,"酒水小食套餐");
        productM1.setAttr(PACKAGE_NUMBER_OF_PKG,"2-3人");
        productM1.setBasePrice(new BigDecimal(68));
        productMS.add(productM1);

        List<ProductM> result = opt.compute(context, ProductListVP.Param.builder().productMS(productMS).build(), config);
        //排序结果是1，6，2，7，4
        //3,2,4,7,6
        //1,6，7,4,2,3
        //1,6,2,7,4,3
        //3，2，4，7，6，1
        String encode = JsonCodec.encode(result);
        Assert.assertNotNull(encode);
        System.out.println(encode);
    }
}
