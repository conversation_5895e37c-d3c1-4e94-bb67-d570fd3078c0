package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.carouselmsg;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.CarouselBuilderContext;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemCarouselMsgVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * CommonAggregateItemCarouselMsgOpt的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class CommonAggregateItemCarouselMsgOptTest {

    @InjectMocks
    private CommonAggregateItemCarouselMsgOpt carouselMsgOpt;

    @Mock
    private ButtonCarouselMsgStrategyFactory carouselMsgStrategyFactory;

    @Mock
    private ButtonCarouselMsgStrategy carouselMsgStrategy;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateItemCarouselMsgVP.Param param;
    private CommonAggregateItemCarouselMsgOpt.Config config;
    private ProductM parentProduct;
    private ProductM childProduct;
    private ProductHierarchyNodeM parentNode;
    private ProductHierarchyNodeM childNode;
    private Map<String, ProductM> productMMap;
    private CarouselMsg carouselMsg;

    @Before
    public void setUp() {
        // 初始化配置
        config = new CommonAggregateItemCarouselMsgOpt.Config();
        config.setCarouselStrategy(Lists.newArrayList("AggregateSaleMsgStrategy"));

        // 初始化产品和节点数据
        parentProduct = new ProductM();
        childProduct = new ProductM();
        parentNode = new ProductHierarchyNodeM();
        childNode = new ProductHierarchyNodeM();
        
        // 设置节点ID
        parentNode.setProductId(1);
        parentNode.setProductType(ProductTypeEnum.DEAL.getType());
        childNode.setProductId(2);
        childNode.setProductType(ProductTypeEnum.SKU.getType());
        
        // 构建节点树结构
        parentNode.setChildren(Lists.newArrayList(childNode));
        
        // 构建产品Map
        productMMap = Maps.newHashMap();
        productMMap.put("1_1", parentProduct);
        productMMap.put("7_2", childProduct);

        // 初始化参数
        param = AggregateItemCarouselMsgVP.Param.builder().build();
        param.setNodeM(parentNode);
        param.setProductMMap(productMMap);

        // 初始化轮播消息
        carouselMsg = new CarouselMsg();
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText("测试轮播消息");
        carouselMsg.setText(styleTextModel);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testComputeNormalCase() {
        // arrange
        when(carouselMsgStrategyFactory.getStrategy("AggregateSaleMsgStrategy")).thenReturn(carouselMsgStrategy);
        when(carouselMsgStrategy.unifiedBuild(any(CarouselBuilderContext.class))).thenReturn(carouselMsg);

        // act
        List<CarouselMsg> result = carouselMsgOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试轮播消息", result.get(0).getText().getText());
    }

    /**
     * 测试param为null的场景
     */
    @Test
    public void testComputeWithNullParam() {
        // act
        List<CarouselMsg> result = carouselMsgOpt.compute(activityCxt, null, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试carouselStrategy为空的场景
     */
    @Test
    public void testComputeWithEmptyStrategy() {
        // arrange
        config.setCarouselStrategy(Lists.newArrayList());

        // act
        List<CarouselMsg> result = carouselMsgOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试策略名称无效的场景
     */
    @Test
    public void testComputeWithInvalidStrategy() {
        // arrange
        config.setCarouselStrategy(Lists.newArrayList("InvalidStrategy"));
        when(carouselMsgStrategyFactory.getStrategy("InvalidStrategy")).thenReturn(null);

        // act
        List<CarouselMsg> result = carouselMsgOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
