package com.sankuai.dzviewscene.product.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantMemberPromoUtilsUsedMerchantMemberDiscount1Test {

    private ProductM productM = mock(ProductM.class);

    private CardM cardM = mock(CardM.class);

    private ProductPromoPriceM productPromoPriceM = mock(ProductPromoPriceM.class);

    @Test(expected = NullPointerException.class)
    public void testUsedMerchantMemberDiscountProductMIsNull() throws Throwable {
        MerchantMemberPromoUtils.usedMerchantMemberDiscount(null, cardM);
    }

    @Test
    public void testUsedMerchantMemberDiscountCardMIsNull() throws Throwable {
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, null));
    }

    @Test
    public void testUsedMerchantMemberDiscountPromoPricesIsEmpty() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountNoPromoTypeIsDirectPromoOrMemberDayOrJoyCard() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountBestPromoPriceIsNull() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountMerchantMemberPromoIsNull() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountPromoPriceNotEqual() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }
    // Note: The test case for when the promo price is equal is omitted due to the limitations mentioned.
}