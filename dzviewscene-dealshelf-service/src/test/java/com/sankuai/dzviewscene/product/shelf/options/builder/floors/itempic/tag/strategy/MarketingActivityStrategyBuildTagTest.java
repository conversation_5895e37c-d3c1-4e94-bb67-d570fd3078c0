package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.dianping.gmkt.activ.api.enums.ExposurePromotionTypeEnum;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import java.util.*;
import org.junit.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MarketingActivityStrategyBuildTagTest {

    @InjectMocks
    private MarketingActivityStrategy marketingActivityStrategy;

    @Mock
    private FloatTagBuildReq param;

    @Mock
    private FloatTagBuildCfg config;

    @Mock
    private ProductM productM;

    @Mock
    private ActivityCxt activityCxt;

    private void setupCommonMocks() {
        List<DouHuM> emptyDouHuList = Collections.emptyList();
        when(param.getContext()).thenReturn(activityCxt);
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(emptyDouHuList);
    }

    /**
     * 测试当productActivityMS为空时返回null
     */
    @Test
    public void testBuildTagWhenProductActivitiesIsEmpty() throws Throwable {
        // arrange
        setupCommonMocks();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.emptyList());
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试当activityM为null时返回null
     */
    @Test
    public void testBuildTagWhenNoValidActivity() throws Throwable {
        // arrange
        setupCommonMocks();
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        // Explicitly set to null
        when(activityM.getActivityPicUrlMap()).thenReturn(null);
        activities.add(activityM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(activities);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试当activityM的activityPicUrlMap为空时返回null
     */
    @Test
    public void testBuildTagWhenActivityPicUrlMapIsEmpty() throws Throwable {
        // arrange
        setupCommonMocks();
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        when(activityM.getActivityPicUrlMap()).thenReturn(new HashMap<>());
        activities.add(activityM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(activities);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试当ShelfShowType为null时返回null
     */
    @Test
    public void testBuildTagWhenShelfShowTypeIsNull() throws Throwable {
        // arrange
        setupCommonMocks();
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        activityPicUrlMap.put("key", new ActivityPicUrlDTO());
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(activities);
        // Invalid shelf show type
        when(param.getShelfShowType()).thenReturn(999);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试当activityPicUrlDTO为null时返回null
     */
    @Test
    public void testBuildTagWhenActivityPicUrlDTOIsNull() throws Throwable {
        // arrange
        setupCommonMocks();
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(activities);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试双列大图货架场景
     */
    @Test
    public void testBuildTagForDoubleBigPicShelf() throws Throwable {
        // arrange
        setupCommonMocks();
        try (MockedStatic<PictureURLBuilders> mockedPictureURLBuilders = Mockito.mockStatic(PictureURLBuilders.class)) {
            mockedPictureURLBuilders.when(() -> PictureURLBuilders.toHttpsUrl(anyString(), anyInt(), anyInt())).thenReturn("https://example.com/image.jpg");
            List<ProductActivityM> activities = new ArrayList<>();
            ProductActivityM activityM = mock(ProductActivityM.class);
            Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
            ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
            activityPicUrlDTO.setUrl("https://example.com/image.jpg");
            activityPicUrlDTO.setHeight(100);
            activityPicUrlDTO.setWidth(200);
            activityPicUrlDTO.setUrlAspectRadio(2.0);
            activityPicUrlMap.put(ExposurePicUrlKeyEnum.DOUBLE_ROW_SHELF_ICON.getKey(), activityPicUrlDTO);
            when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
            activities.add(activityM);
            when(param.getProductM()).thenReturn(productM);
            when(productM.getActivities()).thenReturn(activities);
            when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF.getType());
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNotNull(result);
            assertEquals(FloatTagPositionEnums.LEFT_BOTTOM.getPosition(), result.getPosition());
            assertNotNull(result.getIcon());
            assertEquals("https://example.com/image.jpg", result.getIcon().getPicUrl());
        }
    }

    /**
     * 测试双列小图卡片货架场景
     */
    @Test
    public void testBuildTagForDoubleSmallPicCardShelf() throws Throwable {
        // arrange
        setupCommonMocks();
        try (MockedStatic<PictureURLBuilders> mockedPictureURLBuilders = Mockito.mockStatic(PictureURLBuilders.class)) {
            mockedPictureURLBuilders.when(() -> PictureURLBuilders.toHttpsUrl(anyString(), anyInt(), anyInt())).thenReturn("https://example.com/image.jpg");
            List<ProductActivityM> activities = new ArrayList<>();
            ProductActivityM activityM = mock(ProductActivityM.class);
            Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
            ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
            activityPicUrlDTO.setUrl("https://example.com/image.jpg");
            activityPicUrlDTO.setHeight(20);
            activityPicUrlDTO.setWidth(100);
            activityPicUrlDTO.setUrlAspectRadio(5.0);
            activityPicUrlMap.put(ExposurePicUrlKeyEnum.SHELF_CARD_ICON.getKey(), activityPicUrlDTO);
            when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
            activities.add(activityM);
            when(param.getProductM()).thenReturn(productM);
            when(productM.getActivities()).thenReturn(activities);
            when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.DOUBLE_SMALL_PIC_CARD_SHELF.getType());
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNotNull(result);
            assertEquals(FloatTagPositionEnums.RIGHT_TOP.getPosition(), result.getPosition());
            assertNotNull(result.getIcon());
            assertEquals("https://example.com/image.jpg", result.getIcon().getPicUrl());
        }
    }

    /**
     * 测试当所有条件都不满足时返回null
     */
    @Test
    public void testBuildTagWhenNoConditionMatched() throws Throwable {
        // arrange
        setupCommonMocks();
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
        activityPicUrlDTO.setUrl("https://example.com/image.jpg");
        // 不设置height，导致条件不满足
        activityPicUrlMap.put(ExposurePicUrlKeyEnum.DOUBLE_ROW_SHELF_ICON.getKey(), activityPicUrlDTO);
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF.getType());
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    /**
     * 测试当命中massageTagExpSks时返回null
     */
    @Test
    public void testBuildTagWhenHitMassageTagExpSks() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        massageTagExpSks.add("test_sk");
        // Set massageTagExpSks field using reflection
        Field field = MarketingActivityStrategy.class.getDeclaredField("massageTagExpSks");
        field.setAccessible(true);
        List<String> originalValue = (List<String>) field.get(marketingActivityStrategy);
        field.set(marketingActivityStrategy, massageTagExpSks);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            List<DouHuM> douHuMList = new ArrayList<>();
            DouHuM douHuM = new DouHuM();
            douHuM.setSk("test_sk");
            douHuMList.add(douHuM);
            when(param.getContext()).thenReturn(activityCxt);
            when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(any(List.class), any(List.class))).thenReturn(true);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull(result);
        } finally {
            // Reset the field to its original value
            field.set(marketingActivityStrategy, originalValue);
        }
    }

    private void setMassageTagExpSks(List<String> massageTagExpSks) throws NoSuchFieldException, IllegalAccessException {
        Field field = MarketingActivityStrategy.class.getDeclaredField("massageTagExpSks");
        field.setAccessible(true);
        field.set(marketingActivityStrategy, massageTagExpSks);
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull_WithNullValue() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Adding the key with null value
        activityPicUrlMap.put(ExposurePicUrlKeyEnum.BIG_ICON.getKey(), null);
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF.getType());
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull_ForDoubleBigPicShelf() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Not adding the expected key for DOUBLE_BIG_PIC_SHELF
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull_ForDoubleSmallPicCardShelf() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Not adding the expected key for DOUBLE_SMALL_PIC_CARD_SHELF
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull_ForSingleNewBigPicShelf() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Not adding the expected key for SINGLE_NEW_BIG_PIC_SHELF
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull_ForDoubleNewBigPicShelf() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Not adding the expected key for DOUBLE_NEW_BIG_PIC_SHELF
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }

    @Test
    public void testBuildTag_WhenActivityPicUrlDTOIsNull() throws Throwable {
        // arrange
        List<String> massageTagExpSks = new ArrayList<>();
        setMassageTagExpSks(massageTagExpSks);
        List<DouHuM> douHuMList = new ArrayList<>();
        when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(param.getContext()).thenReturn(activityCxt);
        List<ProductActivityM> activities = new ArrayList<>();
        ProductActivityM activityM = mock(ProductActivityM.class);
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        // Not adding the expected key to the map, so lookup will return null
        when(activityM.getActivityPicUrlMap()).thenReturn(activityPicUrlMap);
        activities.add(activityM);
        when(productM.getActivities()).thenReturn(activities);
        when(param.getProductM()).thenReturn(productM);
        try (MockedStatic<DouHuUtils> mockedDouHuUtils = Mockito.mockStatic(DouHuUtils.class)) {
            mockedDouHuUtils.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            // act
            FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
            // assert
            assertNull("Result should be null when activityPicUrlDTO is null", result);
        }
    }
}