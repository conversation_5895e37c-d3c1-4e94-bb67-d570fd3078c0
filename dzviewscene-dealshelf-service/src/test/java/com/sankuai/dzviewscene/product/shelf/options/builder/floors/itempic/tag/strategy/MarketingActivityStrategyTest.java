package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.dianping.gmkt.activ.api.enums.ExposurePromotionTypeEnum;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MarketingActivityStrategyTest {

    @InjectMocks
    private MarketingActivityStrategy marketingActivityStrategy;

    @Mock
    private FloatTagBuildReq param;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private FloatTagBuildCfg config;

    @Mock
    private ProductM productM;

    @Mock
    private ProductActivityM productActivityM;

    @Mock
    private ActivityPicUrlDTO activityPicUrlDTO;

    private MockedStatic<PromoSimplifyUtils> mockedStatic;

    @Before
    public void setUp() {
        mockedStatic = Mockito.mockStatic(PromoSimplifyUtils.class);
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }

    @Test
    public void testBuildTagWhenProductActivitiesIsEmpty() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(new ArrayList<>());

        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);

        // assert
        assertNull(result);

    }

    // 其他测试用例...@Test         Z

    @Test
    public void testBuildTagWhenProductActivitiesIsNull() {
        // arrange

        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(null);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);

    }

    @Test
    public void testBuildTagWhenActivityPicUrlMapIsEmpty() {
        // arrange

        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(Collections.emptyMap());
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);

    }

    @Test
    public void testBuildTagWhenActivityPicUrlMapIsNull() {
        // arrange

        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(null);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);

    }

    @Test
    public void testBuildTagWhenShelfShowTypeEnumIsNull() {
        // arrange

        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(Collections.singletonMap(ExposurePicUrlKeyEnum.BIG_ICON.getKey(), activityPicUrlDTO));
        when(param.getShelfShowType()).thenReturn(0);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);

    }

    @Test
    public void testBuildTagWhenActivityPicUrlDTOIsNull() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(Collections.singletonMap(ExposurePicUrlKeyEnum.BIG_ICON.getKey(), null));
        when(param.getShelfShowType()).thenReturn(2);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildTagWhenActivityPicUrlDTO() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(Collections.singletonMap(ExposurePicUrlKeyEnum.BIG_ICON.getKey(), activityPicUrlDTO));
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getHeight()).thenReturn(300);
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getWidth()).thenReturn(300);
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getUrl()).thenReturn("http://p0.meituan.com");

        when(param.getShelfShowType()).thenReturn(2);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testBuildNewActivityPicUrl() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(param.getContext()).thenReturn(activityCxt);
        when(productM.getActivities()).thenReturn(Collections.singletonList(productActivityM));
        when(productActivityM.getActivityPicUrlMap()).thenReturn(Collections.singletonMap(ExposurePicUrlKeyEnum.BIG_ICON.getKey(), activityPicUrlDTO));
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getHeight()).thenReturn(300);
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getWidth()).thenReturn(300);
        when(productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.BIG_ICON.getKey()).getUrl()).thenReturn("http://p0.meituan.com");
        when(productActivityM.getExposurePromotionType()).thenReturn(ExposurePromotionTypeEnum.PLATFORM_PROMOTION.getType());

        when(param.getShelfShowType()).thenReturn(2);
        mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(true);
        // act
        FloatTagVO result = marketingActivityStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
    }

}
