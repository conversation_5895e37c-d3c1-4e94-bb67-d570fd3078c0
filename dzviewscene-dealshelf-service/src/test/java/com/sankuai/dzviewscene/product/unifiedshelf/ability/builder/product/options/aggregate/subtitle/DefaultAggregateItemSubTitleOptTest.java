package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.subtitle;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.springframework.test.util.AssertionErrors.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DefaultAggregateItemSubTitleOptTest {

    @InjectMocks
    private DefaultAggregateItemSubTitleOpt subTitleOpt;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateItemSubTitleVP.Param param;
    private DefaultAggregateItemSubTitleOpt.Config config;
    private ProductHierarchyNodeM nodeM;
    private Map<String, ProductM> productMMap;

    @Before
    public void setUp() {
        // 初始化配置
        config = new DefaultAggregateItemSubTitleOpt.Config();

        // 初始化参数
        param = AggregateItemSubTitleVP.Param.builder().build();
        nodeM = new ProductHierarchyNodeM();
        productMMap = Maps.newHashMap();

        param.setNodeM(nodeM);
        param.setProductMMap(productMMap);
    }

    /**
     * 测试SPU节点的副标题生成
     */
    @Test
    public void testComputeForSpuNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.SPT_SPU.getType());
        nodeM.setChildren(Lists.newArrayList(new ProductHierarchyNodeM(), new ProductHierarchyNodeM())); // 假设有两个子节点

        // act
        ItemSubTitleVO result = subTitleOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("equal","可选2个团购", result.getTags().get(0).getText()); // 根据实际内容进行调整
    }

    /**
     * 测试普通节点的副标题生成
     */
    @Test
    public void testComputeForNormalNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        nodeM.setChildren(Lists.newArrayList(new ProductHierarchyNodeM(), new ProductHierarchyNodeM())); // 假设有两个子节点

        // act
        ItemSubTitleVO result = subTitleOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("equal","可选2个套餐", result.getTags().get(0).getText()); // 根据实际内容进行调整
    }
}