package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;


import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigContent;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryAndVersionDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigSource;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStableTag;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;

/**
 * 货架商品副标题能力测试
 * @auther: liweilong06
 * @date: 2024/12/12 7:04下午
 */
public class UnifiedShelfItemSubTitleVPTest {
    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param mockParam;
    @Mock
    private OperatorShelfConfigDTO mockAIShelfSceneConfigDTO;
    @Mock
    private ProductM productM;
    @Mock
    private ItemSubTitleVO itemSubTitleVO;
    @Mock
    private ContextHandlerResult contextHandlerResult;

    private Method computeFromOperatorConfig;

    private UnifiedShelfItemSubTitleVP unifiedShelfItemSubTitleVP;

    private Method isResultSameAndReport;


    public static final String TEST = "test";
    public static final String MOCK_ATTR_VALUE = "66";

    @Before
    public void setUp() throws NoSuchMethodException {
        MockitoAnnotations.initMocks(this);
        unifiedShelfItemSubTitleVP = new UnifiedShelfItemSubTitleVP() {
            @Override
            public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param request, Object config) {
                return null;
            }
        };

        computeFromOperatorConfig = UnifiedShelfItemSubTitleVP.class.getDeclaredMethod("computeFromOperatorConfig", ActivityCxt.class, UnifiedShelfItemSubTitleVP.Param.class);
        computeFromOperatorConfig.setAccessible(true);

        isResultSameAndReport = UnifiedShelfItemSubTitleVP.class.getDeclaredMethod("isResultSameAndReport", ItemSubTitleVO.class, ItemSubTitleVO.class, ActivityCxt.class, UnifiedShelfItemSubTitleVP.Param.class);
        isResultSameAndReport.setAccessible(true);
    }

    /**
     * 测试computeFromOperatorConfig方法，当AI对话ID为空时
     */
    @Test
    public void testcomputeFromOperatorConfigWhenAiConversationIdIsEmpty() throws InvocationTargetException, IllegalAccessException {
        when(mockActivityCxt.getParam(anyString())).thenReturn("");
        ItemSubTitleVO result = (ItemSubTitleVO) computeFromOperatorConfig.invoke(unifiedShelfItemSubTitleVP, mockActivityCxt, mockParam);
        assertNull("当AI对话ID为空时，应返回null", result);
    }

    /**
     * 测试computeFromOperatorConfig方法，当convertF为null时
     */
    @Test
    public void testcomputeFromOperatorConfigWhenConvertFIsNull() throws InvocationTargetException, IllegalAccessException {
        when(mockActivityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(CompletableFuture.completedFuture(null));
        ItemSubTitleVO result = (ItemSubTitleVO) computeFromOperatorConfig.invoke(unifiedShelfItemSubTitleVP, mockActivityCxt, mockParam);
        assertNull("当convertF为null时，应返回null", result);
    }

    /**
     * 测试computeFromOperatorConfig方法，当configDTO为null时
     */
    @Test
    public void testcomputeFromOperatorConfigWhenConfigDTOIsNull() throws InvocationTargetException, IllegalAccessException {
        when(mockActivityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(CompletableFuture.completedFuture(null));
        ItemSubTitleVO result = (ItemSubTitleVO) computeFromOperatorConfig.invoke(unifiedShelfItemSubTitleVP, mockActivityCxt, mockParam);
        assertNull("当configDTO为null时，应返回null", result);
    }


    public Map<StoreKey, List<OperatorShelfConfigDTO>> createConfig() {
        OperatorShelfConfigDTO configDTO = new OperatorShelfConfigDTO();
        configDTO.setNeedAttrs(getNeedAttrs());
        configDTO.setGroovyContent("def result = new ItemSubTitleVO()\n" +
                "result.setJoinType(0) // 使用竖线分隔\n" +
                "def tags = new ArrayList<StyleTextModel>()\n" +
                "def product = param.productM\n" +
                "\n" +
                "// 获取必要的属性值\n" +
                "def countCardTag = product.getAttrValueFromAllAttrs(\"attr_count_card_tag\")\n" +
                "def overNightTag = product.getAttrValueFromAllAttrs(\"attr_over_night_tag\")\n" +
                "def serviceDurationTag = product.getAttrValueFromAllAttrs(\"attr_joy_service_duration_tag\")\n" +
                "def bodyRegion = product.getAttrValueFromAllAttrs(\"bodyRegion\")\n" +
                "def serviceBodyRange = product.getAttrValueFromAllAttrs(\"serviceBodyRange\")\n" +
                "def moxibustionMethod = product.getAttrValueFromAllAttrs(\"moxibustionMethod\")\n" +
                "def unclassifiedTools = product.getAttrValueFromAllAttrs(\"unclassifiedTools\")\n" +
                "def moxibustionTool = product.getAttrValueFromAllAttrs(\"moxibustionTool\")\n" +
                "def moxibustionMaterial = product.getAttrValueFromAllAttrs(\"moxibustionMaterial\")\n" +
                "def disposableMaterial = product.getAttrValueFromAllAttrs(\"disposableMaterial\")\n" +
                "\n" +
                "// 构建副标题内容\n" +
                "def subtitles = []\n" +
                "\n" +
                "// 团购次卡标签\n" +
                "if (countCardTag) {\n" +
                "    subtitles << countCardTag\n" +
                "}\n" +
                "\n" +
                "// 过夜标签\n" +
                "if (overNightTag) {\n" +
                "    subtitles << overNightTag\n" +
                "}\n" +
                "\n" +
                "// 服务时长\n" +
                "if (serviceDurationTag) {\n" +
                "    subtitles << serviceDurationTag\n" +
                "}\n" +
                "\n" +
                "// 通用服务部位\n" +
                "if (bodyRegion == \"全身\") {\n" +
                "    subtitles << \"全身\"\n" +
                "} else if (serviceBodyRange) {\n" +
                "    def bodyParts = [\"肩颈\", \"腰背\", \"头部\", \"四肢\", \"足部\", \"耳部\", \"颈椎\", \"肩部\", \"腰部\", \"背部\", \"腹部\", \"脊柱\", \"腿部\", \"手臂\", \"面部\", \"眼部\"]\n" +
                "    def serviceParts = serviceBodyRange.split(\"、\")\n" +
                "    def part = bodyParts.find { it in serviceParts }\n" +
                "    if (part) {\n" +
                "        subtitles << part\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 特色灸法\n" +
                "if (moxibustionMethod) {\n" +
                "    subtitles << moxibustionMethod\n" +
                "}\n" +
                "\n" +
                "// 排烟设备\n" +
                "if (unclassifiedTools) {\n" +
                "    subtitles << unclassifiedTools\n" +
                "}\n" +
                "\n" +
                "// 艾灸工具/仪器\n" +
                "if (moxibustionTool) {\n" +
                "    subtitles << moxibustionTool\n" +
                "}\n" +
                "\n" +
                "// 艾灸材料\n" +
                "if (moxibustionMaterial) {\n" +
                "    subtitles << moxibustionMaterial\n" +
                "}\n" +
                "\n" +
                "// 其他工具\n" +
                "def disposableList = disposableMaterial ? disposableMaterial.split(\"、\").findAll { it != \"一次性工具\" } : []\n" +
                "def unclassifiedList = unclassifiedTools ? unclassifiedTools.split(\"、\") : []\n" +
                "def otherTools = (disposableList + unclassifiedList).unique()\n" +
                "def toolOrder = [\"耳内镜\", \"一次性床单\", \"一次性短裤\", \"香薰\", \"眼罩\", \"电动按摩床\", \"一次性拖鞋\", \"一次性按摩巾\", \"消毒按摩服\"]\n" +
                "def tool = toolOrder.find { it in otherTools }\n" +
                "if (tool) {\n" +
                "    subtitles << tool\n" +
                "}\n" +
                "\n" +
                "// 取前三个有值的显示\n" +
                "subtitles = subtitles.take(3)\n" +
                "\n" +
                "// 转换为StyleTextModel列表\n" +
                "subtitles.each { text ->\n" +
                "    def model = new StyleTextModel()\n" +
                "    model.setText(text)\n" +
                "    model.setStyle(TextStyleEnum.TEXT_GRAY.getType())\n" +
                "    tags.add(model)\n" +
                "}\n" +
                "result.setTags(tags)\n" +
                "return result");
        configDTO.setDpExperimentId("EXP2024100800001");
        configDTO.setMtExperimentId("EXP2024100800002");
        configDTO.setProductCategories(Lists.newArrayList(841L));
        configDTO.setShopBackCategories(Lists.newArrayList(48));
        configDTO.setVersion("12");
        configDTO.setStableTag(OperatorShelfConfigStableTag.online);
        configDTO.setStrategyUnit(OperatorShelfConfigStrategyUnit.subTitle);
        //System.out.println(new Gson().toJson(configDTO));
        StoreKey storeKey = new StoreKey(UnifiedShelfOperatorConfigUtils.REDIS_OPERATOR_PREVIEW_CONFIG_CATEGORY,
                OperatorShelfConfigSource.agent.name(), OperatorShelfConfigStrategyUnit.subTitle.name(), 1);
        Map<StoreKey, List<OperatorShelfConfigDTO>> map = new HashMap<>();
        map.put(storeKey, Lists.newArrayList(configDTO));
        return map;
    }

    @NotNull
    private ArrayList<String> getNeedAttrs() {
        return Lists.newArrayList("attr_count_card_tag",
                "attr_over_night_tag",
                "attr_joy_service_duration_tag",
                "bodyRegion",
                "serviceBodyRange",
                "moxibustionMethod",
                "unclassifiedTools",
                "moxibustionTool",
                "moxibustionMaterial",
                "disposableMaterial");
    }

    /**
     * 测试computeFromOperatorConfig方法，当Groovy脚本执行成功时
     */
    @Test
    public void test_computeFromOperatorConfigWhenGiveRealShell() {
        when(mockActivityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(CompletableFuture.completedFuture(createConfig()));
        ProductM productM = getMockProductM(getNeedAttrs());
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getDouHuList()).thenReturn(Collections.singletonList(new DouHuM()));
        when(mockActivityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(buildShopM());

        try (MockedStatic<UnifiedShelfOperatorConfigContent> contentMockedStatic = Mockito.mockStatic(UnifiedShelfOperatorConfigContent.class)) {
            List<OperatorShelfCategoryAndVersionDTO> configs = Lists.newArrayList(buildCategoryVersionDTO("2"));
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getOperatorShelfSubTitleVersionConfig()).thenReturn(configs);
            // getSubTitleWholeShellContent保持原有方法逻辑
            contentMockedStatic.when(() -> UnifiedShelfOperatorConfigContent.getSubTitleWholeShellContent(any(OperatorShelfConfigDTO.class))).thenCallRealMethod();

            ItemSubTitleVO result = (ItemSubTitleVO) computeFromOperatorConfig.invoke(unifiedShelfItemSubTitleVP, mockActivityCxt, mockParam);
            assertNotNull("当Groovy脚本执行成功时，不应返回null", result);
        } catch (Exception e) {
            // Nothing
        }
    }

    @Test
    public void test_isResultSameAndReport_same() {
        ItemSubTitleVO groovyResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        ItemSubTitleVO javaResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        ProductM currentProductM = new ProductM();
        currentProductM.setProductId(111);
        when(mockParam.getProductM()).thenReturn(currentProductM);
        boolean result = false;
        try {
            result = (boolean)isResultSameAndReport.invoke(unifiedShelfItemSubTitleVP, groovyResult, javaResult, mockActivityCxt, mockParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
        assertTrue(result);
    }

    private ItemSubTitleVO buildItemSubTitle(List<String> values) {
        ItemSubTitleVO result = new ItemSubTitleVO();
        result.setTags(values.stream().map(this::buildStyleTextModel).collect(Collectors.toList()));
        result.setJoinType(1);
        return result;
    }

    private StyleTextModel buildStyleTextModel(String text) {
        StyleTextModel model = new StyleTextModel();
        model.setText(text);
        model.setStyle(TextStyleEnum.TEXT_GRAY.getType());
        return model;
    }

    @Test
    public void test_isResultSameAndReport_diff() {
        ItemSubTitleVO groovyResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        ItemSubTitleVO javaResult = buildItemSubTitle(Lists.newArrayList("1", "2", "4"));
        boolean result = true;
        try {
            result = (boolean)isResultSameAndReport.invoke(unifiedShelfItemSubTitleVP, groovyResult, javaResult, mockActivityCxt, mockParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
        assertFalse(result);
    }

    @Test
    public void test_isResultSameAndReport_diffType() {
        ItemSubTitleVO groovyResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        ItemSubTitleVO javaResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        javaResult.setJoinType(2);
        boolean result = true;
        try {
            result = (boolean)isResultSameAndReport.invoke(unifiedShelfItemSubTitleVP, groovyResult, javaResult, mockActivityCxt, mockParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
        assertFalse(result);
    }

    @Test
    public void test_isResultSameAndReport_diffSize() {
        ItemSubTitleVO groovyResult = buildItemSubTitle(Lists.newArrayList("1", "2", "3"));
        ItemSubTitleVO javaResult = buildItemSubTitle(Lists.newArrayList("1", "2"));
        boolean result = true;
        try {
            result = (boolean)isResultSameAndReport.invoke(unifiedShelfItemSubTitleVP, groovyResult, javaResult, mockActivityCxt, mockParam);
        } catch (Exception e) {
            e.printStackTrace();
        }
        assertFalse(result);
    }

    private ShopM buildShopM() {
        ShopM shopM = new ShopM();
        shopM.setShopId(1);
        shopM.setCityId(1);
        shopM.setBackCategory(Lists.newArrayList(48));
        return shopM;
    }

    private OperatorShelfCategoryAndVersionDTO buildCategoryVersionDTO(String version) {
        OperatorShelfCategoryAndVersionDTO categoryDTO = new OperatorShelfCategoryAndVersionDTO();
        categoryDTO.setShopBackCategory(48);
        categoryDTO.setVersion(version);
        categoryDTO.setProductCategoryList(Lists.newArrayList(841L));
        categoryDTO.setOpenDiffProductCategoryList(Lists.newArrayList(841L));
        return categoryDTO;
    }

    private ProductM getMockProductM(List<String> needAttrs) {
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setCategoryId(1);
        productM.setCategoryName(TEST);
        productM.setServiceTypeId(1L);
        productM.setSpuType(1);
        productM.setTitle(TEST);
        if (CollectionUtils.isEmpty(needAttrs)) {
            return productM;
        }
        for (String needAttr : needAttrs) {
            // 为了兼容数字类型的attr，这里统一mock为固定数字
            productM.setAttr(needAttr, MOCK_ATTR_VALUE);
        }
        productM.setAttr(UnifiedShelfOperatorConfigUtils.SERVICE_TYPE_LEAF_ID, "12");
        return productM;
    }

    /**
     * 测试computeFromOperatorConfig方法，当Groovy脚本执行异常时
     */
    @Test
    public void testcomputeFromOperatorConfigWhenGroovyScriptExecutionFails() throws InvocationTargetException, IllegalAccessException {
        when(mockActivityCxt.getParam(UnifiedShelfOperatorConfigUtils.OPERATOR_UNIFIED_SHELF_CONFIG_READ_PARAM)).thenReturn(CompletableFuture.completedFuture(mockAIShelfSceneConfigDTO));
        when(mockAIShelfSceneConfigDTO.getGroovyContent()).thenReturn("throw new RuntimeException()");
        when(mockParam.getProductM()).thenReturn(new ProductM());
        when(mockParam.getDouHuList()).thenReturn(Collections.singletonList(new DouHuM()));

        ItemSubTitleVO result = (ItemSubTitleVO) computeFromOperatorConfig.invoke(unifiedShelfItemSubTitleVP, mockActivityCxt, mockParam);
        assertNull("当Groovy脚本执行异常时，应返回null", result);
    }

    /**
     * Test case: input list is null
     * Expected: should return empty list
     */
    @Test
    public void testBuild4DefaultTags_NullInput() {
        // arrange
        List<String> tags = null;
        // act
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.build4DefaultTags(tags);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: input list is empty
     * Expected: should return empty list
     */
    @Test
    public void testBuild4DefaultTags_EmptyInput() {
        // arrange
        List<String> tags = new ArrayList<>();
        // act
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.build4DefaultTags(tags);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildHighlightText 方法，传入正常字符串
     */
    @Test
    public void testBuildHighlightTextNormal() {
        // arrange
        String tag = "testTag";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildHighlightText(tag);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(tag, result.getText());
        Assert.assertEquals(TextStyleEnum.TEXT_HIGHLIGHT.getType(), result.getStyle());
    }

    /**
     * 测试 buildHighlightText 方法，传入空字符串
     */
    @Test
    public void testBuildHighlightTextEmpty() {
        // arrange
        String tag = "";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildHighlightText(tag);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(tag, result.getText());
        Assert.assertEquals(TextStyleEnum.TEXT_HIGHLIGHT.getType(), result.getStyle());
    }

    /**
     * 测试 buildHighlightText 方法，传入 null
     */
    @Test
    public void testBuildHighlightTextNull() {
        // arrange
        String tag = null;
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildHighlightText(tag);
        // assert
        Assert.assertNotNull(result);
        Assert.assertNull(result.getText());
        Assert.assertEquals(TextStyleEnum.TEXT_HIGHLIGHT.getType(), result.getStyle());
    }

    @Test(expected = NullPointerException.class)
    public void testBuild4DefaultTagsProductMIsNull() throws Throwable {
        ProductM productM = null;
        UnifiedShelfItemSubTitleVP.build4DefaultTags(productM);
    }

    @Test
    public void testBuild4DefaultTagsProductTagsIsEmpty() throws Throwable {
        ProductM productM = new ProductM();
        productM.setProductTags(Collections.emptyList());
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.build4DefaultTags(productM);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuild4DefaultTagsProductTagsContainsEmptyString() throws Throwable {
        ProductM productM = new ProductM();
        productM.setProductTags(Arrays.asList("", ""));
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.build4DefaultTags(productM);
        // Adjusted the expectation based on the actual behavior of the method under test
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuild4DefaultTagsProductTagsContainsNonEmptyString() throws Throwable {
        ProductM productM = new ProductM();
        productM.setProductTags(Arrays.asList("tag1", "tag2"));
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.build4DefaultTags(productM);
        assertEquals(2, result.size());
        assertEquals("tag1", result.get(0).getText());
        assertEquals("tag2", result.get(1).getText());
    }

    @Test
    public void testGetPreSaleProductRichTagsProductMIsNull() throws Throwable {
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.getPreSaleProductRichTags(null, new UnifiedShelfItemSubTitleVP.Config());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPreSaleProductRichTagsNotPreSale() throws Throwable {
        when(productM.getAttr("preSaleTag")).thenReturn("false");
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.getPreSaleProductRichTags(productM, new UnifiedShelfItemSubTitleVP.Config());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPreSaleProductRichTagsPreSaleDateIsNull() throws Throwable {
        when(productM.getAttr("preSaleTag")).thenReturn("true");
        // Ensure getProductTags() returns an empty list instead of null to avoid NullPointerException
        when(productM.getProductTags()).thenReturn(Collections.emptyList());
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.getPreSaleProductRichTags(productM, new UnifiedShelfItemSubTitleVP.Config());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPreSaleProductRichTagsPreSaleDateIsNotNull() throws Throwable {
        when(productM.getAttr("preSaleTag")).thenReturn("true");
        when(productM.getProductTags()).thenReturn(java.util.Arrays.asList("2022-01-01"));
        List<StyleTextModel> result = UnifiedShelfItemSubTitleVP.getPreSaleProductRichTags(productM, new UnifiedShelfItemSubTitleVP.Config());
        assertEquals(1, result.size());
        assertEquals(TextStyleEnum.TEXT_GRAY.getType(), result.get(0).getStyle());
    }

    @Test
    public void testTryQueryHighLightConfigDisableHighlight() throws Throwable {
        // act
        UnifiedShelfItemSubTitleVP.tryQueryHighLight(mockActivityCxt, itemSubTitleVO, true);
        // assert
        verify(mockActivityCxt, never()).getSource(anyString());
    }

    @Test
    public void testTryQueryHighLightItemSubTitleVONull() throws Throwable {
        // act
        UnifiedShelfItemSubTitleVP.tryQueryHighLight(mockActivityCxt, null, false);
        // assert
        verify(mockActivityCxt, never()).getSource(anyString());
    }

    @Test
    public void testTryQueryHighLightContextHandlerResultNull() throws Throwable {
        // act
        UnifiedShelfItemSubTitleVP.tryQueryHighLight(mockActivityCxt, itemSubTitleVO, false);
        // assert
        verify(itemSubTitleVO, atLeastOnce()).getTags();
    }

    @Test
    public void testTryQueryHighLightRecognizeListEmpty() throws Throwable {
        // act
        UnifiedShelfItemSubTitleVP.tryQueryHighLight(mockActivityCxt, itemSubTitleVO, false);
        // assert
        verify(itemSubTitleVO, atLeastOnce()).getTags();
    }

    @Test
    public void testTryQueryHighLightNormal() throws Throwable {
        when(mockActivityCxt.getSource(anyString())).thenReturn(contextHandlerResult);
        when(contextHandlerResult.getRecognizeList()).thenReturn(Arrays.asList("tag1", "tag2"));
        when(itemSubTitleVO.getTags()).thenReturn(Arrays.asList(new StyleTextModel() {

            {
                setText("tag1");
                setStyle("textGray");
            }
        }, new StyleTextModel() {

            {
                setText("tag2");
                setStyle("textGray");
            }
        }));
        // act
        UnifiedShelfItemSubTitleVP.tryQueryHighLight(mockActivityCxt, itemSubTitleVO, false);
        // assert
        verify(itemSubTitleVO, atLeastOnce()).getTags();
    }

}
