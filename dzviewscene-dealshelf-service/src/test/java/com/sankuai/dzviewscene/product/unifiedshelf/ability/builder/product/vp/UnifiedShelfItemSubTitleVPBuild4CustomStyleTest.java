package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Unit tests for UnifiedShelfItemSubTitleVP.build4CustomStyle method
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemSubTitleVPBuild4CustomStyleTest {

    /**
     * Test scenario: When tags parameter is null
     * Expected: Should return null
     */
    @Test
    public void testBuild4CustomStyle_WhenTagsNull() throws Throwable {
        // arrange
        List<String> tags = null;
        List<String> styles = Arrays.asList("style1");
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNull("Result should be null when tags is null", result);
    }

    /**
     * Test scenario: When tags parameter is empty list
     * Expected: Should return null
     */
    @Test
    public void testBuild4CustomStyle_WhenTagsEmpty() throws Throwable {
        // arrange
        List<String> tags = Collections.emptyList();
        List<String> styles = Arrays.asList("style1");
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNull("Result should be null when tags is empty", result);
    }

    /**
     * Test scenario: When both tags and styles are valid with same size
     * Expected: Should return ItemSubTitleVO with correctly mapped tags and styles
     */
    @Test
    public void testBuild4CustomStyle_WithValidInput() throws Throwable {
        // arrange
        List<String> tags = Arrays.asList("tag1", "tag2");
        List<String> styles = Arrays.asList("style1", "style2");
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("JoinType should match input", joinType, result.getJoinType());
        List<StyleTextModel> resultTags = result.getTags();
        assertNotNull("Tags list should not be null", resultTags);
        assertEquals("Should have correct number of tags", 2, resultTags.size());
        // Verify first tag
        assertEquals("First tag text should match", "tag1", resultTags.get(0).getText());
        assertEquals("First tag style should match", "style1", resultTags.get(0).getStyle());
        // Verify second tag
        assertEquals("Second tag text should match", "tag2", resultTags.get(1).getText());
        assertEquals("Second tag style should match", "style2", resultTags.get(1).getStyle());
    }

    /**
     * Test scenario: When tags and styles have different sizes
     * Expected: Should return ItemSubTitleVO with empty tags list
     */
    @Test
    public void testBuild4CustomStyle_WithDifferentSizes() throws Throwable {
        // arrange
        List<String> tags = Arrays.asList("tag1", "tag2");
        List<String> styles = Collections.singletonList("style1");
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("JoinType should match input", joinType, result.getJoinType());
        assertNotNull("Tags list should not be null", result.getTags());
        assertTrue("Tags list should be empty when sizes don't match", result.getTags().isEmpty());
    }

    /**
     * Test scenario: When tags contain null or empty values
     * Expected: Should only include valid tag-style pairs
     */
    @Test
    public void testBuild4CustomStyle_WithInvalidValues() throws Throwable {
        // arrange
        List<String> tags = Arrays.asList("tag1", "", null, "tag2");
        List<String> styles = Arrays.asList("style1", "style2", "style3", "style4");
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("JoinType should match input", joinType, result.getJoinType());
        List<StyleTextModel> resultTags = result.getTags();
        assertNotNull("Tags list should not be null", resultTags);
        assertEquals("Should only include valid tag-style pairs", 2, resultTags.size());
        assertEquals("Valid tag text should be included", "tag1", resultTags.get(0).getText());
        assertEquals("Valid tag style should be included", "style1", resultTags.get(0).getStyle());
        assertEquals("Valid tag text should be included", "tag2", resultTags.get(1).getText());
        assertEquals("Valid tag style should be included", "style4", resultTags.get(1).getStyle());
    }

    /**
     * Test scenario: When styles list is null but tags are valid
     * Expected: Should return ItemSubTitleVO with empty tags list
     */
    @Test
    public void testBuild4CustomStyle_WhenStylesNull() throws Throwable {
        // arrange
        List<String> tags = Arrays.asList("tag1", "tag2");
        List<String> styles = null;
        int joinType = 1;
        // act
        ItemSubTitleVO result = UnifiedShelfItemSubTitleVP.build4CustomStyle(tags, styles, joinType);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("JoinType should match input", joinType, result.getJoinType());
        assertNotNull("Tags list should not be null", result.getTags());
        assertTrue("Tags list should be empty when styles is null", result.getTags().isEmpty());
    }

    @Test
    public void testBuildRedBackground_NormalTag() throws Throwable {
        // arrange
        String tag = "Hot";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildRedBackground(tag);
        // assert
        assertNotNull(result);
        assertEquals("Hot", result.getText());
        assertEquals(TextStyleEnum.TEXT_RED_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildRedBackground_EmptyTag() throws Throwable {
        // arrange
        String tag = "";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildRedBackground(tag);
        // assert
        assertNotNull(result);
        assertEquals("", result.getText());
        assertEquals(TextStyleEnum.TEXT_RED_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildRedBackground_NullTag() throws Throwable {
        // arrange
        String tag = null;
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildRedBackground(tag);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
        assertEquals(TextStyleEnum.TEXT_RED_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildRedBackground_WhitespaceTag() throws Throwable {
        // arrange
        String tag = "   ";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildRedBackground(tag);
        // assert
        assertNotNull(result);
        assertEquals("   ", result.getText());
        assertEquals(TextStyleEnum.TEXT_RED_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildRedBackground_SpecialCharactersTag() throws Throwable {
        // arrange
        String tag = "!@#$%^&*()_+";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildRedBackground(tag);
        // assert
        assertNotNull(result);
        assertEquals("!@#$%^&*()_+", result.getText());
        assertEquals(TextStyleEnum.TEXT_RED_BACKGROUND.getType(), result.getStyle());
    }

    public static StyleTextModel buildGreenBackground(String tag) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(tag);
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN_BACKGROUND.getType());
        return styleTextModel;
    }

    @Test
    public void testBuildGreenBackgroundWithNormalTag() {
        // arrange
        String tag = "Test Tag";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(tag);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Text should match the input tag", tag, result.getText());
        assertEquals("Style should be TEXT_GREEN_BACKGROUND type", TextStyleEnum.TEXT_GREEN_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildGreenBackgroundWithEmptyTag() {
        // arrange
        String tag = "";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(tag);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Text should be empty string", "", result.getText());
        assertEquals("Style should be TEXT_GREEN_BACKGROUND type", TextStyleEnum.TEXT_GREEN_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildGreenBackgroundWithNullTag() {
        // arrange
        String tag = null;
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(tag);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Text should be null", null, result.getText());
        assertEquals("Style should be TEXT_GREEN_BACKGROUND type", TextStyleEnum.TEXT_GREEN_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildGreenBackgroundWithSpecialCharacters() {
        // arrange
        String tag = "!@#$%^&*()_+";
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(tag);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Text should match the special characters", tag, result.getText());
        assertEquals("Style should be TEXT_GREEN_BACKGROUND type", TextStyleEnum.TEXT_GREEN_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildGreenBackgroundWithLongTag() {
        // arrange
        StringBuilder longTagBuilder = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longTagBuilder.append("a");
        }
        String longTag = longTagBuilder.toString();
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(longTag);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Text should match the long tag", longTag, result.getText());
        assertEquals("Style should be TEXT_GREEN_BACKGROUND type", TextStyleEnum.TEXT_GREEN_BACKGROUND.getType(), result.getStyle());
    }

    @Test
    public void testBuildGreenBackgroundUsesCorrectEnumValue() {
        // arrange
        String tag = "Test";
        String expectedStyle = TextStyleEnum.TEXT_GREEN_BACKGROUND.getType();
        // act
        StyleTextModel result = UnifiedShelfItemSubTitleVP.buildGreenBackground(tag);
        // assert
        assertEquals("Style should match the TEXT_GREEN_BACKGROUND enum type value", expectedStyle, result.getStyle());
    }
}