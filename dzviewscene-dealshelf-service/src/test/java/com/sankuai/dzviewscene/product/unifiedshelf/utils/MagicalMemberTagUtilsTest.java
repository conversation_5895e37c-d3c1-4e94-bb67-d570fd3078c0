package com.sankuai.dzviewscene.product.unifiedshelf.utils;
import com.dianping.tpfun.product.api.sku.common.utils.GsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MagicalMemberTagUtilsTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private Map<String, String> extendDisplayInfo;

    @Before
    public void setUp() {
        extendDisplayInfo = new HashMap<>();
        MagicalMemberTagUtils.magicalMemberConfig = new MagicalMemberTagUtils.MagicalMemberConfig();
    }

    /**
     * 测试场景：extendDisplayInfo为空
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_ExtendDisplayInfoIsEmpty() {
        // arrange
        extendDisplayInfo.clear();

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：magicalMemberConfig为空
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_MagicalMemberConfigIsNull() {
        // arrange
        MagicalMemberTagUtils.magicalMemberConfig = null;
        extendDisplayInfo.put(MagicalMemberTagUtils.MAGICAL_MEMBER_COUPON_LABEL, "{}");

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：未命中实验
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_NotHitExp() {
        // arrange
        extendDisplayInfo.put(MagicalMemberTagUtils.MAGICAL_MEMBER_COUPON_LABEL, "{}");

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：命中实验，但extendDisplayInfo中MAGICAL_MEMBER_COUPON_LABEL对应的值无法解析为MagicalMemberTagTextDTO
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_InvalidMagicalMemberCouponLabel() {
        // arrange
        extendDisplayInfo.put(MagicalMemberTagUtils.MAGICAL_MEMBER_COUPON_LABEL, "invalid_json");

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：命中实验，extendDisplayInfo中MAGICAL_MEMBER_COUPON_LABEL对应的值可以解析为MagicalMemberTagTextDTO，但showType不匹配
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_ShowTypeNotMatch() {
        // arrange
        MagicalMemberTagTextDTO dto = new MagicalMemberTagTextDTO();
        dto.setShowType("other_type");
        extendDisplayInfo.put(MagicalMemberTagUtils.MAGICAL_MEMBER_COUPON_LABEL, GsonUtils.toJson(dto));

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：命中实验，extendDisplayInfo中MAGICAL_MEMBER_COUPON_LABEL对应的值可以解析为MagicalMemberTagTextDTO，且showType匹配
     */
    @Test
    public void testGetCanInflateAndCanInflateMore_ShowTypeMatch() {
        // arrange
        MagicalMemberTagTextDTO dto = new MagicalMemberTagTextDTO();
        dto.setShowType(MagicalMemberTagShowTypeEnum.INFLATED_IS_BETTER_MMC.getValue());
        extendDisplayInfo.put(MagicalMemberTagUtils.MAGICAL_MEMBER_COUPON_LABEL, GsonUtils.toJson(dto));
        List<DouHuM> douHuMList = Lists.newArrayList();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("1");
        douHuMList.add(douHuM);
        when(mockActivityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        MagicalMemberTagUtils.magicalMemberConfig.setExpSkWhitelist(Lists.newArrayList("1"));
        MagicalMemberTagUtils.magicalMemberConfig.setClientType(Lists.newArrayList(0));

        // act
        boolean result = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(mockActivityCxt, extendDisplayInfo);

        // assert
        assertTrue(result);
    }
}