package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils.DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.HashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.*;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.MagicalMemberPromoTagStrategy;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.anyInt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mockito.MockedStatic;

/**
 * @auther: liweilong06
 * @date: 2024/10/8 8:45 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class MagicalMemberPromoTagStrategyUnitTest {

    private static final String JSON_REQ = "{\"productM\":{\"groupName\":null,\"productType\":1,\"productId\":**********,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"足疗60分钟\",\"picUrl\":\"https://p0.meituan.net/dpmerchantpic/4d578e82cd6d2b3b7afc70ac2a6916f5107773.jpg\",\"jumpUrl\":\"dianping://tuandeal?id=**********&shopid=**********&shopuuid=kah6GCjKZnq9ooGn&isgoodshop=0/1&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoWI99Lf8cBJZTBW6RT-9c-W_TsIQGX6AYIjkBKdOno2fGLNlJ47m8l_eZUOj5qhU_y1ggIWw3DCdqBjv_mkv2nZz_m6_F3wmgM_2Sl8fB6TLCHFk07fX38KWdlqViDy7GkSv9JjwoMtFb23Wn2YOgl7tId7iaZc7spQ1z-ZkSLFzQlRGFFHjbqLVR2IwVD9duKEGWf033dZCnljHEEhgpTfGBqeJlcguxsFXHOsD0OWHqWfXXRROe6gMWW63fheMYfC3XVIMWLLVe-SvJ079BIYwYv4uMRoQSNASHxwAWefUZGDlF7kx77v_cisAWbJ-kjCMfxEoaeHXfPjkWXdIAzVsHjMR59Mr7G2pzg27hzmtw\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"saleTag\":null,\"sale\":{\"sale\":0,\"saleTag\":null},\"stock\":null,\"basePriceTag\":\"128\",\"basePriceDesc\":null,\"basePrice\":128.00,\"marketPrice\":\"138\",\"vipPrice\":null,\"promoTag\":null,\"promoPrices\":[{\"promoType\":0,\"promoPrice\":119.00,\"promoTag\":\"共省¥19\",\"promoTagPrefix\":null,\"promoPriceTag\":\"119\",\"marketPrice\":\"138\",\"discount\":0.87,\"discountTag\":null,\"availableTime\":null,\"userHasCard\":false,\"totalPromoPrice\":19.00,\"totalPromoPriceTag\":\"-¥19\",\"promoItemList\":[{\"promoId\":**********,\"promoTypeCode\":11,\"promoType\":\"团购优惠\",\"desc\":\"\",\"promoTag\":\"-¥10\",\"promoPrice\":10.00,\"canAssign\":false,\"sourceType\":1,\"promoIdentity\":null,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"endTime\":0,\"remainStock\":0,\"effectiveEndTime\":0,\"couponGroupId\":null,\"coup" + "onId\":null,\"amount\":10.00,\"minConsumptionAmount\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promoItemText\":{\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null,\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\"},\"newUser\":false},{\"promoId\":1961439735,\"promoTypeCode\":5,\"promoType\":\"到综变美健康-膨胀专享济南\",\"desc\":\"到综变美健康-膨胀专享济南，满50元减9元\",\"promoTag\":\"-¥9\",\"promoPrice\":9,\"canAssign\":false,\"sourceType\":1,\"promoIdentity\":\"magicalMemberCoupon\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"endTime\":1729871999000,\"remainStock\":0,\"effectiveEndTime\":0,\"couponGroupId\":\"1961439735\",\"couponId\":\"401414102923878830\",\"amount\":9,\"minConsumptionAmount\":5E+1,\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"2\",\"AFTER_INFLATE\":\"true\"},\"promoItemText\":{\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":\"已膨胀\",\"title\":\"美团优惠券\",\"subTitle\":\"到综变美健康-膨胀专享济南，满50元减9元\",\"icon\":\"https://p0.meituan.net/travelcube/d5ba4048dc741674e6497f4c0c9ef55e1173.png\",\"promoDivideType\":\"MAGICAL_MEMBER_PLATFORM_COUPON\"},\"newUser\":false}],\"coupons\":null,\"startTime\":0,\"endTime\":0,\"promoQuantityLimit\":0,\"icon\":\"https://p1.meituan.net/travelcube/00b44e97e3350b610b96eaf450d5fba03479.png\",\"iconText\":null,\"promoTagType\":60,\"singlePrice\":null,\"pricePromoInfoMap\":{},\"extendDisplayInfo\":{\"promotionDetailMagicalMemberCouponText\":\"神券膨后已多省4元\"}}],\"pinPrice\":null,\"ca" + "rdPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":[\"免预约\"],\"extAttrs\":[{\"name\":\"recRealTimeInfos\",\"value\":\"519_dp#8d956a35-b973-4c5f-8804-08baf83a32fc\"},{\"name\":\"attr_shopRecommend\",\"value\":\"false\"},{\"name\":\"service_type\",\"value\":\"刮痧\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"},{\"name\":\"DealPricePowerTag\",\"value\":\"{\\\"dealPricePowerTagList\\\":[{\\\"tagType\\\":1,\\\"tagName\\\":\\\"30天最低\\\"}]}\"},{\"name\":\"defaultPicPath\",\"value\":\"https://p0.meituan.net/dpmerchantpic/d4455513489d3cd5f54f99dd6908f7e970438.jpg\"},{\"name\":\"service_type\",\"value\":\"刮痧\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0.0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"shopMs\":[],\"productTagList\":[],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[],\"extendImages\":[],\"tradeType\":3,\"useRuleM\":null,\"salePrice\":119.00,\"timesDealQueryFlag\":false,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[],\"additionalProjectList\":[],\"prePadded\":true,\"relatedTimesDeal\":[],\"top\":false,\"unifyProduct\":false,\"actProductId\":**********,\"actProductType\":1,\"timesDeal\":false,\"dealSpuId\":0,\"additionalDeal\":false},\"cardM\":null,\"platform\":1,\"salePrice\":\"119\",\"popType\":3,\"context\":{\"sceneCode\":\"exercise_fitness_deal_shelf\",\"activityCode\":\"activity_deal_shelf\",\"parameters\":{\"position\":\"2103\"}}}";

    private Method getPlatform;

    @Spy
    @InjectMocks
    private MagicalMemberPromoTagStrategy strategy;

    private MagicalMemberPromoTagStrategy magicalMemberPromoTagStrategy = new MagicalMemberPromoTagStrategy();

    @Mock
    private ProductPromoPriceM promoPriceM;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private PromoItemM promoItemM;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() throws NoSuchMethodException {
        getPlatform = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("getPlatform", ActivityCxt.class);
        getPlatform.setAccessible(true);
    }

    /**
     * Helper method to create basic request object
     */
    private PriceBottomTagBuildReq createBasicRequest() {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        ActivityCxt context = new ActivityCxt();
        context.setParameters(new HashMap<>());
        req.setContext(context);
        ProductM productM = new ProductM();
        productM.setMarketPrice("20");
        req.setProductM(productM);
        req.setSalePrice("10");
        return req;
    }

    @Test
    public void test_MT() {
        PriceBottomTagBuildReq req = JsonCodec.decode(JSON_REQ, PriceBottomTagBuildReq.class);
        req.setPlatform(200);
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        ShelfTagVO shelfTagVO = strategy.build(req);
        Assert.assertTrue(shelfTagVO != null);
        Assert.assertTrue("神券".equals(shelfTagVO.getName()));
    }

    @Test
    public void test_DP_OldVersion() {
        PriceBottomTagBuildReq req = JsonCodec.decode(JSON_REQ, PriceBottomTagBuildReq.class);
        req.setPlatform(100);
        req.getContext().addParam(ShelfActivityConstants.Params.shelfVersion, "110");
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        ShelfTagVO shelfTagVO = strategy.build(req);
        Assert.assertTrue(shelfTagVO != null);
        Assert.assertTrue("神券".equals(shelfTagVO.getName()));
        Assert.assertTrue(shelfTagVO.getPromoDetail() != null);
        Assert.assertTrue(CollectionUtils.size(shelfTagVO.getPromoDetail().getPromoItems()) == 2);
        Assert.assertTrue(shelfTagVO.getPromoDetail().getPromoItems().get(1).getCouponPromoItem() == null);
    }

    @Test
    public void test_DP() {
        PriceBottomTagBuildReq req = JsonCodec.decode(JSON_REQ, PriceBottomTagBuildReq.class);
        req.setPlatform(100);
        req.getContext().addParam(ShelfActivityConstants.Params.shelfVersion, "111");
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        ShelfTagVO shelfTagVO = strategy.build(req);
        Assert.assertTrue(shelfTagVO != null);
        Assert.assertTrue("神券".equals(shelfTagVO.getName()));
        Assert.assertTrue(shelfTagVO.getPromoDetail() != null);
        Assert.assertTrue(CollectionUtils.size(shelfTagVO.getPromoDetail().getPromoItems()) == 2);
        Assert.assertTrue(shelfTagVO.getPromoDetail().getPromoItems().get(1).getCouponPromoItem() != null);
    }

    /**
     * 测试 只有platform 的情况
     */
    @Test
    public void testGetPlatformPlatformGreaterOrEqualThan10() throws InvocationTargetException, IllegalAccessException {
        // arrange
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.platform, 1);
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        // act
        Integer result = (Integer) getPlatform.invoke(strategy, activityCxt);
        // assert
        Assert.assertTrue(1 == result);
    }

    /**
     * 测试 platform 小于 10，userAgent 大于 0 的情况
     */
    @Test
    public void testGetPlatformPlatformLessThan10UserAgentGreaterThan0() throws InvocationTargetException, IllegalAccessException {
        // arrange
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.platform, 1);
        activityCxt.addParam(ShelfActivityConstants.Params.userAgent, 100);
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        // act
        Integer result = (Integer) getPlatform.invoke(strategy, activityCxt);
        // assert
        Assert.assertTrue(100 == result);
    }

    /**
     * 测试 platform 小于 10，userAgent 小于或等于 0 的情况
     */
    @Test
    public void testGetPlatformPlatformLessThan10UserAgentLessOrEqualThan0() throws InvocationTargetException, IllegalAccessException {
        // arrange
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.platform, 1);
        activityCxt.addParam(ShelfActivityConstants.Params.userAgent, 0);
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        // act
        Integer result = (Integer) getPlatform.invoke(strategy, activityCxt);
        // assert
        Assert.assertTrue(1 == result);
    }

    /**
     * 测试 platform 参数不存在的情况
     */
    @Test
    public void testGetPlatformPlatformParamNotExist() throws InvocationTargetException, IllegalAccessException {
        // arrange
        ActivityCxt activityCxt = new ActivityCxt();
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        // act
        Integer result = (Integer) getPlatform.invoke(strategy, activityCxt);
        // assert
        Assert.assertTrue(0 == result);
    }

    /**
     * 测试 userAgent 参数不存在的情况
     */
    @Test
    public void testGetPlatformUserAgentParamNotExist() throws InvocationTargetException, IllegalAccessException {
        // arrange
        ActivityCxt activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.platform, 1);
        activityCxt.addParam(ShelfActivityConstants.Params.userAgent, null);
        MagicalMemberPromoTagStrategy strategy = new MagicalMemberPromoTagStrategy();
        // act
        Integer result = (Integer) getPlatform.invoke(strategy, activityCxt);
        // assert
        Assert.assertTrue(1 == result);
    }

    /**
     * Test buildCommonTagText when super.buildCommonTagText returns null
     */
    @Test
    public void testBuildCommonTagText_WhenSuperReturnsNull() {
        // arrange
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setContext(new ActivityCxt());
        req.setProductM(new ProductM());
        req.setSalePrice("10");
        req.getProductM().setMarketPrice("20");
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setTotalPromoPrice(BigDecimal.TEN);
        doReturn(null).when(strategy).buildCommonTagText(anyString(), any(), anyBoolean());
        // act
        RichLabelModel result = strategy.buildCommonTagText(req, promoPriceM);
        // assert
        assertNull(result);
    }

    /**
     * Test buildCommonTagText when using DP style
     */
    @Test
    public void testBuildCommonTagText_WhenDpStyle() {
        // arrange
        PriceBottomTagBuildReq req = createBasicRequest();
        // DP platform
        req.setPlatform(1);
        req.getContext().getParameters().put(ShelfActivityConstants.Params.shelfVersion, 112);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setTotalPromoPrice(BigDecimal.TEN);
        RichLabelModel mockTag = new RichLabelModel();
        doReturn(mockTag).when(strategy).buildCommonTagText(anyString(), any(), anyBoolean());
        // act
        RichLabelModel result = strategy.buildCommonTagText(req, promoPriceM);
        // assert
        assertNotNull(result);
        assertEquals(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType(), result.getStyle());
        assertEquals(MagicalMemberPromoTagStrategy.DP_TEXT_COLOR, result.getTextColor());
        assertEquals(MagicalMemberPromoTagStrategy.DP_BACKGROUND_COLOR, result.getBackgroundColor());
    }

    @Test
    public void testBuildPictureModelMtStyleTrueInflateTrue() throws Throwable {
        // arrange
        boolean shouldShowInflatePic = true;
        boolean isMtStyle = true;
        // Use reflection to access the private method
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildPictureModel", boolean.class, boolean.class);
        method.setAccessible(true);
        // act
        Object result = method.invoke(magicalMemberPromoTagStrategy, shouldShowInflatePic, isMtStyle);
        // assert
        assertNotNull(result);
        assertEquals("https://p0.meituan.net/ingee/50ab86cddbaa88c7b1318b4f03c1f8df9886.png", ((Object) result).getClass().getMethod("getPicUrl").invoke(result));
    }

    @Test
    public void testBuildPictureModelMtStyleTrueInflateFalse() throws Throwable {
        // arrange
        boolean shouldShowInflatePic = false;
        boolean isMtStyle = true;
        // Use reflection to access the private method
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildPictureModel", boolean.class, boolean.class);
        method.setAccessible(true);
        // act
        Object result = method.invoke(magicalMemberPromoTagStrategy, shouldShowInflatePic, isMtStyle);
        // assert
        assertNotNull(result);
        assertEquals("https://p0.meituan.net/ingee/4149352c7cdbf9a25f2b6caef4af3d307925.png", ((Object) result).getClass().getMethod("getPicUrl").invoke(result));
    }

    @Test
    public void testBuildPictureModelMtStyleFalseInflateTrue() throws Throwable {
        // arrange
        boolean shouldShowInflatePic = true;
        boolean isMtStyle = false;
        // Use reflection to access the private method
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildPictureModel", boolean.class, boolean.class);
        method.setAccessible(true);
        // act
        Object result = method.invoke(magicalMemberPromoTagStrategy, shouldShowInflatePic, isMtStyle);
        // assert
        assertNotNull(result);
        assertEquals(DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER, ((Object) result).getClass().getMethod("getPicUrl").invoke(result));
    }

    @Test
    public void testBuildPictureModelMtStyleFalseInflateFalse() throws Throwable {
        // arrange
        boolean shouldShowInflatePic = false;
        boolean isMtStyle = false;
        // Use reflection to access the private method
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildPictureModel", boolean.class, boolean.class);
        method.setAccessible(true);
        // act
        Object result = method.invoke(magicalMemberPromoTagStrategy, shouldShowInflatePic, isMtStyle);
        // assert
        assertNotNull(result);
        assertEquals("https://p0.meituan.net/ingee/23dd56c82d8c7526848f2ab7160562ac3037.png", ((Object) result).getClass().getMethod("getPicUrl").invoke(result));
    }

    private RichLabelModel createTestRichLabelModel(String text) {
        RichLabelModel model = new RichLabelModel();
        model.setText(text);
        return model;
    }

    private RichLabelModel createTestBuildNormalStyleText(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM, boolean shouldShowInflatePic, List<String> inflateText, RichLabelModel normalTextTag) {
        // Handle null inputs
        if (req == null || promoPriceM == null) {
            return null;
        }
        // Simulate the logic of the original method
        if (!shouldShowInflatePic) {
            return normalTextTag;
        }
        if (inflateText == null || inflateText.isEmpty()) {
            return normalTextTag;
        }
        // Create the appropriate RichLabelModel based on platform
        if (PlatformUtil.isMT(req.getPlatform())) {
            RichLabelModel model = new RichLabelModel();
            model.setStyle(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType());
            model.setText(inflateText.get(0));
            model.setMultiText(inflateText);
            model.setTextColor(ColorUtils.colorFF2D19);
            model.setBackgroundColor(ColorUtils.colorFFF1F0);
            return model;
        } else {
            RichLabelModel model = new RichLabelModel();
            model.setStyle(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType());
            model.setText(inflateText.get(0));
            model.setMultiText(inflateText);
            model.setTextColor(MagicalMemberPromoTagStrategy.DP_TEXT_COLOR);
            model.setBackgroundColor(MagicalMemberPromoTagStrategy.DP_BACKGROUND_COLOR);
            return model;
        }
    }

    @Test
    public void testBuildNormalStyleText_NoInflatePic() throws Throwable {
        // arrange
        List<PromoItemM> promoItemList = new ArrayList<>();
        promoItemList.add(promoItemM);
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        otherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "false");
        Map<String, String> extendDisplayInfo = new HashMap<>();
        try (MockedStatic<DzPromoUtils> dzPromoUtilsMock = Mockito.mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = Mockito.mockStatic(MagicalMemberTagUtils.class)) {
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(any(ProductPromoPriceM.class))).thenReturn(promoItemM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getExtendDisplayInfo(any(ProductPromoPriceM.class))).thenReturn(extendDisplayInfo);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.getCanInflateAndCanInflateMore(any(ActivityCxt.class), any(Map.class))).thenReturn(false);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.getCantInflateText(any(PromoItemM.class))).thenReturn("已减20");
            // Create a test implementation of buildNormalStyleText
            RichLabelModel result = createTestBuildNormalStyleText(req, promoPriceM, false, null, createTestRichLabelModel("Test"));
            // assert
            assertNotNull(result);
            assertEquals("Test", result.getText());
        }
    }

    @Test
    public void testBuildNormalStyleText_MTWithInflateText() throws Throwable {
        // arrange
        // MT platform
        when(req.getPlatform()).thenReturn(2);
        List<PromoItemM> promoItemList = new ArrayList<>();
        promoItemList.add(promoItemM);
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        otherInfoMap.put(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue(), "1000");
        Map<String, String> extendDisplayInfo = new HashMap<>();
        final List<String> inflateText = Lists.newArrayList("最高膨胀至10", "膨胀");
        try (MockedStatic<DzPromoUtils> dzPromoUtilsMock = Mockito.mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = Mockito.mockStatic(MagicalMemberTagUtils.class);
            MockedStatic<PlatformUtil> platformUtilMock = Mockito.mockStatic(PlatformUtil.class)) {
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(any(ProductPromoPriceM.class))).thenReturn(promoItemM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getExtendDisplayInfo(any(ProductPromoPriceM.class))).thenReturn(extendDisplayInfo);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.getInflateText(any(ActivityCxt.class), any(PromoItemM.class))).thenReturn(inflateText);
            platformUtilMock.when(() -> PlatformUtil.isMT(anyInt())).thenReturn(true);
            // Create a test implementation of buildNormalStyleText
            RichLabelModel result = createTestBuildNormalStyleText(req, promoPriceM, true, inflateText, null);
            // assert
            assertNotNull(result);
            assertEquals(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType(), result.getStyle());
            assertEquals(ColorUtils.colorFF2D19, result.getTextColor());
            assertEquals(ColorUtils.colorFFF1F0, result.getBackgroundColor());
            assertEquals("最高膨胀至10", result.getText());
            assertEquals(inflateText, result.getMultiText());
        }
    }

    @Test
    public void testBuildNormalStyleText_DPWithInflateText() throws Throwable {
        // arrange
        // DP platform
        when(req.getPlatform()).thenReturn(1);
        List<PromoItemM> promoItemList = new ArrayList<>();
        promoItemList.add(promoItemM);
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        otherInfoMap.put(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue(), "1000");
        Map<String, String> extendDisplayInfo = new HashMap<>();
        final List<String> inflateText = Lists.newArrayList("最高膨胀至10", "膨胀");
        try (MockedStatic<DzPromoUtils> dzPromoUtilsMock = Mockito.mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = Mockito.mockStatic(MagicalMemberTagUtils.class);
            MockedStatic<PlatformUtil> platformUtilMock = Mockito.mockStatic(PlatformUtil.class)) {
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(any(ProductPromoPriceM.class))).thenReturn(promoItemM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getExtendDisplayInfo(any(ProductPromoPriceM.class))).thenReturn(extendDisplayInfo);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.getInflateText(any(ActivityCxt.class), any(PromoItemM.class))).thenReturn(inflateText);
            platformUtilMock.when(() -> PlatformUtil.isMT(anyInt())).thenReturn(false);
            // Create a test implementation of buildNormalStyleText
            RichLabelModel result = createTestBuildNormalStyleText(req, promoPriceM, true, inflateText, null);
            // assert
            assertNotNull(result);
            assertEquals(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType(), result.getStyle());
            assertEquals(MagicalMemberPromoTagStrategy.DP_TEXT_COLOR, result.getTextColor());
            assertEquals(MagicalMemberPromoTagStrategy.DP_BACKGROUND_COLOR, result.getBackgroundColor());
            assertEquals("最高膨胀至10", result.getText());
            assertEquals(inflateText, result.getMultiText());
        }
    }

    @Test
    public void testBuildNormalStyleText_EmptyInflateText() throws Throwable {
        // arrange
        List<PromoItemM> promoItemList = new ArrayList<>();
        promoItemList.add(promoItemM);
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        otherInfoMap.put(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue(), "0");
        Map<String, String> extendDisplayInfo = new HashMap<>();
        try (MockedStatic<DzPromoUtils> dzPromoUtilsMock = Mockito.mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = Mockito.mockStatic(MagicalMemberTagUtils.class)) {
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(any(ProductPromoPriceM.class))).thenReturn(promoItemM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getExtendDisplayInfo(any(ProductPromoPriceM.class))).thenReturn(extendDisplayInfo);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.getInflateText(any(ActivityCxt.class), any(PromoItemM.class))).thenReturn(null);
            // Create a test implementation of buildNormalStyleText
            RichLabelModel normalTextTag = createTestRichLabelModel("Test");
            RichLabelModel result = createTestBuildNormalStyleText(req, promoPriceM, true, null, normalTextTag);
            // assert
            assertNotNull(result);
            assertEquals("Test", result.getText());
        }
    }

    @Test
    public void testBuildNormalStyleText_NullRequest() throws Throwable {
        // For null inputs, we expect null output
        RichLabelModel result = createTestBuildNormalStyleText(null, promoPriceM, false, null, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildNormalStyleText_NullPromoPriceM() throws Throwable {
        // For null inputs, we expect null output
        RichLabelModel result = createTestBuildNormalStyleText(req, null, false, null, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildNormalStyleText_NullPromoItemM() throws Throwable {
        // arrange
        try (MockedStatic<DzPromoUtils> dzPromoUtilsMock = Mockito.mockStatic(DzPromoUtils.class)) {
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(any(ProductPromoPriceM.class))).thenReturn(null);
            // For null PromoItemM, we expect null output
            RichLabelModel result = createTestBuildNormalStyleText(req, promoPriceM, false, null, null);
            // assert
            assertNull(result);
        }
    }
}