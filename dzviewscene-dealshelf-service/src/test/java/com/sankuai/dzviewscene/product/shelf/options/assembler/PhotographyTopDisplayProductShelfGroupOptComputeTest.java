package com.sankuai.dzviewscene.product.shelf.options.assembler;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.util.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PhotographyTopDisplayProductShelfGroupOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PhotographyTopDisplayProductShelfGroupOpt.Param param;

    @Mock
    private PhotographyTopDisplayProductShelfGroupOpt.Config config;

    @Test
    public void testComputeWhenFilterSelectedNotInFilterNames() throws Throwable {
        String groupName = "testGroup";
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Collections.singletonList(new ProductM()));
        productGroupMs.put(groupName, productGroupM);
        Map<String, FilterM> filterMs = new HashMap<>();
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setTitle("notInList");
        filterBtnM.setSelected(true);
        filterM.setFilters(Collections.singletonList(filterBtnM));
        filterMs.put(groupName, filterM);
        when(param.getProductGroupMs()).thenReturn(productGroupMs);
        when(param.getFilterMs()).thenReturn(filterMs);
        when(config.getGroupName()).thenReturn(groupName);
        when(config.getFilterNames()).thenReturn(Arrays.asList("filter1", "filter2"));
        PhotographyTopDisplayProductShelfGroupOpt opt = new PhotographyTopDisplayProductShelfGroupOpt();
        Map<String, ProductGroupM> result = opt.compute(context, param, config);
        assertEquals(productGroupMs, result);
    }

    @Test
    public void testComputeWhenProductIsTopDisplay() throws Throwable {
        String groupName = "testGroup";
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        ProductM productM = new ProductM();
        productM.setExtAttrs(new ArrayList<>());
        productM.setAttr("hot_sell_product", "true");
        productGroupM.setProducts(Collections.singletonList(productM));
        productGroupMs.put(groupName, productGroupM);
        when(param.getProductGroupMs()).thenReturn(productGroupMs);
        when(config.getGroupName()).thenReturn(groupName);
        when(config.getTopFlags()).thenReturn(Collections.singletonList("topFlag"));
        PhotographyTopDisplayProductShelfGroupOpt opt = new PhotographyTopDisplayProductShelfGroupOpt();
        opt.compute(context, param, config);
        assertEquals("true", productM.getAttr("hot_sell_product"));
    }

    @Test
    public void testComputeWhenProductIsNotTopDisplay() throws Throwable {
        String groupName = "testGroup";
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        ProductM productM = new ProductM();
        productGroupM.setProducts(Collections.singletonList(productM));
        productGroupMs.put(groupName, productGroupM);
        when(param.getProductGroupMs()).thenReturn(productGroupMs);
        when(config.getGroupName()).thenReturn(groupName);
        when(config.getTopFlags()).thenReturn(Collections.singletonList("topFlag"));
        PhotographyTopDisplayProductShelfGroupOpt opt = new PhotographyTopDisplayProductShelfGroupOpt();
        Map<String, ProductGroupM> result = opt.compute(context, param, config);
        assertEquals(productGroupMs, result);
    }

    @Test
    public void testComputeWhenTopDisplayGroupListEmpty() throws Throwable {
        String groupName = "testGroup";
        Map<String, ProductGroupM> productGroupMs = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        ProductM productM = new ProductM();
        productGroupM.setProducts(Collections.singletonList(productM));
        productGroupMs.put(groupName, productGroupM);
        when(param.getProductGroupMs()).thenReturn(productGroupMs);
        when(config.getGroupName()).thenReturn(groupName);
        when(config.getTopFlags()).thenReturn(Collections.emptyList());
        PhotographyTopDisplayProductShelfGroupOpt opt = new PhotographyTopDisplayProductShelfGroupOpt();
        Map<String, ProductGroupM> result = opt.compute(context, param, config);
        assertEquals(productGroupMs, result);
    }
}
