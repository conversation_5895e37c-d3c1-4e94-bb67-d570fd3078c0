package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.FloatTagModel;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.ActivityTagCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicTagOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicTagOpt.TagItemCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicFloatTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedSingleRowPicTagOptTest {

    @InjectMocks
    private UnifiedSingleRowPicTagOpt unifiedSingleRowPicTagOpt;

    @Mock
    private FloatTagBuildStrategyFactory tagBuildStrategyFactory;

    @Mock
    private UnifiedShelfItemPicFloatTagVP.Param mockParam;

    @Mock
    private UnifiedSingleRowPicTagOpt.Config config;

    @Mock
    private UnifiedSingleRowPicTagOpt.RuleTagGroup ruleTagGroup;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试compute方法，当productM为null时应返回null
     */
    @Test
    public void testComputeProductMIsNull() {
        ActivityCxt context = new ActivityCxt();
        mockParam.setProductM(null);
        Config config = new Config();

        List<FloatTagModel> result = unifiedSingleRowPicTagOpt.compute(context, mockParam, config);

        assertNull(result);
    }

    /**
     * 测试compute方法，当productM的picUrl为空时应返回null
     */
    @Test
    public void testComputePicUrlIsEmpty() {
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setPicUrl("");
        Config config = new Config();
        mockParam.setProductM(productM);
        List<FloatTagModel> result = unifiedSingleRowPicTagOpt.compute(context, mockParam, config);

        assertNull(result);
    }

    /**
     * 测试compute方法，当config为null时应返回null
     */
    @Test
    public void testComputeConfigIsNull() {
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setPicUrl("http://example.com/pic.jpg");
        mockParam.setProductM(productM);
        List<FloatTagModel> result = unifiedSingleRowPicTagOpt.compute(context, mockParam, null);

        assertNull(result);
    }

    /**
     * 测试compute方法，当config的ruleTagGroups为空时应返回null
     */
    @Test
    public void testComputeRuleTagGroupsIsEmpty() {
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setPicUrl("http://example.com/pic.jpg");
        mockParam.setProductM(productM);
        Config config = new Config();
        config.setRuleTagGroups(new ArrayList<>());

        List<FloatTagModel> result = unifiedSingleRowPicTagOpt.compute(context, mockParam, config);

        assertNull(result);
    }

    /**
     * 测试buildFloatTagBuildReq方法是否正确填充了filterId和context
     */
    @Test
    public void testBuildFloatTagBuildReqWithFilterIdAndContext() throws Exception {
        // 准备测试数据
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setPicUrl("http://example.com/pic.jpg");

        // 设置mockParam的值
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getFilterId()).thenReturn(123L);
        when(mockParam.getPlatform()).thenReturn(1);
        when(mockParam.getIndex()).thenReturn(2);

        // 准备ActivityTagCfg
        ActivityTagCfg activityTagCfg = new ActivityTagCfg();

        // 准备FloatTagBuildStrategy
        FloatTagBuildStrategy mockStrategy = mock(FloatTagBuildStrategy.class);
        // 准备TagItemCfg
        TagItemCfg tagItemCfg = new TagItemCfg();
        tagItemCfg.setStrategyName("testStrategy");
        tagItemCfg.setActivityTagCfg(activityTagCfg);

        // 使用反射调用私有方法buildFloatTagBuildReq
        java.lang.reflect.Method method = UnifiedSingleRowPicTagOpt.class.getDeclaredMethod(
                "buildFloatTagBuildReq",
                UnifiedShelfItemPicFloatTagVP.Param.class,
                ActivityTagCfg.class,
                int.class,
                List.class,
                ActivityCxt.class);
        method.setAccessible(true);

        // 调用方法
        FloatTagBuildReq result = (FloatTagBuildReq) method.invoke(
                unifiedSingleRowPicTagOpt,
                mockParam,
                activityTagCfg,
                1,
                new ArrayList<FloatTagModel>(),
                context);

        // 验证结果
        assertNotNull(result);
        assertEquals(123L, result.getFilterId());
        assertSame(context, result.getContext());
        assertEquals(productM, result.getProductM());
        assertEquals(1, result.getShelfShowType());
        assertEquals(activityTagCfg, result.getActivityTagCfg());
        assertEquals(1, result.getPlatform());
        assertEquals(2, result.getIndex());
    }

    /**
     * 测试calGroupFloatTagVO方法中是否正确传递了context到buildFloatTagBuildReq方法
     */
    @Test
    public void testCalGroupFloatTagVOPassesContextToBuildFloatTagBuildReq() throws Exception {
        // 准备测试数据
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        productM.setPicUrl("http://example.com/pic.jpg");

        // 设置mockParam的值
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getFilterId()).thenReturn(123L);
        when(mockParam.getPlatform()).thenReturn(1);
        when(mockParam.getIndex()).thenReturn(2);

        // 准备TagItemCfg
        TagItemCfg tagItemCfg = new TagItemCfg();
        tagItemCfg.setStrategyName("testStrategy");
        tagItemCfg.setActivityTagCfg(new ActivityTagCfg());
        List<TagItemCfg> tagItemCfgs = Collections.singletonList(tagItemCfg);

        // 准备FloatTagBuildStrategy
        FloatTagBuildStrategy mockStrategy = mock(FloatTagBuildStrategy.class);
        when(tagBuildStrategyFactory.getBuildStrategy("testStrategy")).thenReturn(mockStrategy);

        // 创建ArgumentCaptor来捕获传递给newBuild方法的FloatTagBuildReq参数
        ArgumentCaptor<FloatTagBuildReq> reqCaptor = ArgumentCaptor.forClass(FloatTagBuildReq.class);

        // 调用calGroupFloatTagVO方法
        java.lang.reflect.Method method = UnifiedSingleRowPicTagOpt.class.getDeclaredMethod(
                "calGroupFloatTagVO",
                int.class,
                UnifiedShelfItemPicFloatTagVP.Param.class,
                List.class,
                List.class,
                ActivityCxt.class);
        method.setAccessible(true);

        method.invoke(
                unifiedSingleRowPicTagOpt,
                1,
                mockParam,
                tagItemCfgs,
                new ArrayList<FloatTagModel>(),
                context);

        // 验证mockStrategy.newBuild被调用，并捕获传递的FloatTagBuildReq参数
        verify(mockStrategy).newBuild(reqCaptor.capture(), any());

        // 验证捕获的FloatTagBuildReq参数中的filterId和context是否正确
        FloatTagBuildReq capturedReq = reqCaptor.getValue();
        assertNotNull(capturedReq);
        assertEquals(123L, capturedReq.getFilterId());
        assertSame(context, capturedReq.getContext());
    }
}
