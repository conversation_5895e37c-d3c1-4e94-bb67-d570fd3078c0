package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean.UnifiedShelfItemCommonOceanLabsOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP.Param;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedShelfItemCommonOceanLabsOpt的compute方法
 */
public class UnifiedShelfItemCommonOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private Config mockConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private CardM mockCardM;
    @Mock
    private ShelfItemVO mockShelfItemVO;
    @Mock
    private ProductHierarchyNodeM nodeM;

    private UnifiedShelfItemCommonOceanLabsOpt optUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        optUnderTest = new UnifiedShelfItemCommonOceanLabsOpt();
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getCardM()).thenReturn(mockCardM);
        when(mockParam.getShelfItemVO()).thenReturn(mockShelfItemVO);
        when(mockParam.getNodeM()).thenReturn(nodeM);
    }

    /**
     * 测试compute方法，当enableTopDisplayInfoLabs和enableCardLabs都为false时
     */
    @Test
    public void testCompute_WhenTopDisplayInfoLabsAndCardLabsDisabled() {
        // arrange
        when(mockConfig.isEnableTopDisplayInfoLabs()).thenReturn(false);
        when(mockConfig.isEnableCardLabs()).thenReturn(false);

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }

    /**
     * 测试compute方法，当enableTopDisplayInfoLabs为true时
     */
    @Test
    public void testCompute_WhenTopDisplayInfoLabsEnabled() {
        // arrange
        when(mockConfig.isEnableTopDisplayInfoLabs()).thenReturn(true);
        when(mockConfig.isEnableCardLabs()).thenReturn(false);

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }

    /**
     * 测试compute方法，当enableCardLabs为true时
     */
    @Test
    public void testCompute_WhenCardLabsEnabled() {
        // arrange
        when(mockConfig.isEnableTopDisplayInfoLabs()).thenReturn(false);
        when(mockConfig.isEnableCardLabs()).thenReturn(true);

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }

    /**
     * 测试compute方法，当所有配置都启用时
     */
    @Test
    public void testCompute_WhenAllConfigsEnabled() {
        // arrange
        when(mockConfig.isEnableTopDisplayInfoLabs()).thenReturn(true);
        when(mockConfig.isEnableCardLabs()).thenReturn(true);

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }

    /**
     * 测试SPU打点
     */
    @Test
    public void testCompute_SPU() {
        // arrange
        when(nodeM.getProductType()).thenReturn(ProductTypeEnum.SPT_SPU.getType());
        ProductHierarchyNodeM parent = new ProductHierarchyNodeM();
        parent.setChildren(Lists.newArrayList(nodeM));
        when(nodeM.getParent()).thenReturn(parent);
        when(nodeM.getIdentityKey()).thenReturn("1_0");

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }

    /**
     * 测试SKU打点
     */
    @Test
    public void testCompute_SKU() {
        // arrange
        when(nodeM.getProductType()).thenReturn(ProductTypeEnum.SKU.getType());

        // act
        String result = optUnderTest.compute(mockActivityCxt, mockParam, mockConfig);

        // 断言结果不为空
        assertTrue(StringUtils.isNotEmpty(result));
    }
}
