package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.specialtags;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateSpecialTagVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class AggregateItemRecommendTagOptTest {

    @InjectMocks
    private AggregateItemRecommendTagOpt recommendTagOpt;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateSpecialTagVP.Param param;
    private AggregateItemRecommendTagOpt.Config config;
    private ProductHierarchyNodeM nodeM;
    private Map<String, ProductM> productMMap;

    @Before
    public void setUp() {
        // 初始化配置
        config = new AggregateItemRecommendTagOpt.Config();

        // 初始化参数
        param = AggregateSpecialTagVP.Param.builder().build();
        nodeM = new ProductHierarchyNodeM();
        productMMap = Maps.newHashMap();

        param.setNodeM(nodeM);
        param.setProductMMap(productMMap);
    }

    /**
     * 测试SPU节点的推荐标签
     */
    @Test
    public void testComputeForSpuNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.SPT_SPU.getType());
        ProductHierarchyNodeM childNodeM = new ProductHierarchyNodeM();
        childNodeM.setProductId(2);
        childNodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productM.setAttr("recommend_product_tags_list", "[\"标签1\"]");
        nodeM.setChildren(Lists.newArrayList(childNodeM));
        productMMap.put("1_2", productM);

        // act
        ItemSpecialTagVO result = recommendTagOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        List<ShelfTagVO> tags = result.getTags();
        assertFalse(tags.isEmpty());
        assertEquals("“标签1”", tags.get(0).getText().getText());
    }

    /**
     * 测试普通节点的推荐标签
     */
    @Test
    public void testComputeForNormalNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productM.setAttr("recommend_product_tags_list", "[\"标签1\"]");
        productMMap.put("1_1", productM);

        // act
        ItemSpecialTagVO result = recommendTagOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        List<ShelfTagVO> tags = result.getTags();
        assertFalse(tags.isEmpty());
        assertEquals("“标签1”", tags.get(0).getText().getText());
    }

    /**
     * 测试没有推荐标签的场景
     */
    @Test
    public void testComputeWithNoRecommendTags() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productMMap.put("1_1", productM);

        // act
        ItemSpecialTagVO result = recommendTagOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }
}
