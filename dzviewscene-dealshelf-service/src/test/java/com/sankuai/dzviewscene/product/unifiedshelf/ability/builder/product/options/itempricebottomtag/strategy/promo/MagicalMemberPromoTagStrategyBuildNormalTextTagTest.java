package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MagicalMemberPromoTagStrategyBuildNormalTextTagTest {

    @InjectMocks
    private MagicalMemberPromoTagStrategy strategy;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductPromoPriceM promoPriceM;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    private Method buildNormalTextTagMethod;

    @Before
    public void setUp() throws Exception {
        buildNormalTextTagMethod = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        buildNormalTextTagMethod.setAccessible(true);
    }

    private RichLabelModel invokeBuildNormalTextTag(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) throws Exception {
        return (RichLabelModel) buildNormalTextTagMethod.invoke(strategy, req, promoPriceM);
    }

    /**
     * 测试当标签文本为空时的情况
     */
    @Test
    public void testBuildNormalTextTag_EmptyTagText() throws Throwable {
        // 由于我们不能修改被测试的代码，我们需要使用一个不同的方法来测试这个场景
        // 我们将使用一个有效的配置，但确保buildCommonTagText返回null
        // 准备
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.shelfVersion, 112);
        // DP platform
        when(req.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("100");
        when(req.getSalePrice()).thenReturn("90");
        when(req.isHitSimplifyPromoTag()).thenReturn(false);
        // 使用spy来部分模拟strategy
        MagicalMemberPromoTagStrategy spyStrategy = spy(strategy);
        // 模拟buildCommonTagText返回null
        doReturn(null).when(spyStrategy).buildCommonTagText(anyString(), isNull(), anyBoolean());
        // 使用反射调用私有方法
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        RichLabelModel result = (RichLabelModel) method.invoke(spyStrategy, req, promoPriceM);
        // 断言
        assertNull("当标签文本为空时应返回null", result);
    }

    /**
     * 测试简化促销标签的情况
     */
    @Test
    public void testBuildNormalTextTag_SimplifiedPromoTag() throws Throwable {
        // 准备
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setAmount(new BigDecimal("50"));
        // MAGICAL_MEMBER_COUPON
        promoItemM.setPromotionExplanatoryTags(Collections.singletonList(3));
        when(req.isHitSimplifyPromoTag()).thenReturn(true);
        when(promoPriceM.getPromoItemList()).thenReturn(Collections.singletonList(promoItemM));
        when(req.getContext()).thenReturn(activityCxt);
        when(activityCxt.getParameters()).thenReturn(new HashMap<>());
        // 使用spy来部分模拟strategy
        MagicalMemberPromoTagStrategy spyStrategy = spy(strategy);
        // 创建一个RichLabelModel作为返回值
        RichLabelModel mockLabel = new RichLabelModel();
        mockLabel.setText("已减50");
        mockLabel.setStyle(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType());
        // 模拟buildCommonTagText返回我们创建的标签
        doReturn(mockLabel).when(spyStrategy).buildCommonTagText(anyString(), isNull(), anyBoolean());
        // 使用反射调用私有方法
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        RichLabelModel result = (RichLabelModel) method.invoke(spyStrategy, req, promoPriceM);
        // 断言
        assertNotNull("简化促销标签应返回非null结果", result);
        assertEquals("已减50", result.getText());
    }

    /**
     * 测试美团样式的情况
     */
    @Test
    public void testBuildNormalTextTag_MtStyle_WithMarketAndSalePrice() throws Throwable {
        // 准备
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.shelfVersion, 100);
        when(req.getContext()).thenReturn(activityCxt);
        when(activityCxt.getParameters()).thenReturn(params);
        // MT platform
        when(req.getPlatform()).thenReturn(2);
        when(req.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("100");
        when(req.getSalePrice()).thenReturn("80");
        when(req.isHitSimplifyPromoTag()).thenReturn(false);
        // 使用spy来部分模拟strategy
        MagicalMemberPromoTagStrategy spyStrategy = spy(strategy);
        // 创建一个RichLabelModel作为返回值
        RichLabelModel mockLabel = new RichLabelModel();
        mockLabel.setText("共省20");
        // 模拟buildCommonTagText返回我们创建的标签
        doReturn(mockLabel).when(spyStrategy).buildCommonTagText(anyString(), isNull(), anyBoolean());
        // 使用反射调用私有方法
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        RichLabelModel result = (RichLabelModel) method.invoke(spyStrategy, req, promoPriceM);
        // 断言
        assertNotNull("美团样式应返回非null结果", result);
        assertEquals(RichLabelStyleEnum.MT_MAGICAL_MEMBER.getType(), result.getStyle());
        assertEquals("共省20", result.getText());
    }

    /**
     * 测试点评样式的情况
     */
    @Test
    public void testBuildNormalTextTag_DpStyle_WithTotalPromoPrice() throws Throwable {
        // 准备
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.shelfVersion, 112);
        when(req.getContext()).thenReturn(activityCxt);
        when(activityCxt.getParameters()).thenReturn(params);
        // DP platform
        when(req.getPlatform()).thenReturn(1);
        when(req.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn(null);
        when(promoPriceM.getTotalPromoPrice()).thenReturn(new BigDecimal("30"));
        when(req.isHitSimplifyPromoTag()).thenReturn(false);
        // 使用spy来部分模拟strategy
        MagicalMemberPromoTagStrategy spyStrategy = spy(strategy);
        // 创建一个RichLabelModel作为返回值
        RichLabelModel mockLabel = new RichLabelModel();
        mockLabel.setText("共省30");
        // 模拟buildCommonTagText返回我们创建的标签
        doReturn(mockLabel).when(spyStrategy).buildCommonTagText(anyString(), isNull(), anyBoolean());
        // 使用反射调用私有方法
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        RichLabelModel result = (RichLabelModel) method.invoke(spyStrategy, req, promoPriceM);
        // 断言
        assertNotNull("点评样式应返回非null结果", result);
        assertEquals(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType(), result.getStyle());
        assertEquals("共省30", result.getText());
    }

    /**
     * 测试版本边界的情况
     */
    @Test
    public void testBuildNormalTextTag_VersionBoundary() throws Throwable {
        // 准备
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.shelfVersion, MagicalMemberPromoTagStrategy.DP_LIMIT_SHELF_VERSION);
        when(req.getContext()).thenReturn(activityCxt);
        when(activityCxt.getParameters()).thenReturn(params);
        // DP platform
        when(req.getPlatform()).thenReturn(1);
        when(req.getProductM()).thenReturn(productM);
        when(productM.getMarketPrice()).thenReturn("100");
        when(req.getSalePrice()).thenReturn("90");
        when(req.isHitSimplifyPromoTag()).thenReturn(false);
        // 使用spy来部分模拟strategy
        MagicalMemberPromoTagStrategy spyStrategy = spy(strategy);
        // 创建一个RichLabelModel作为返回值
        RichLabelModel mockLabel = new RichLabelModel();
        mockLabel.setText("共省10");
        // 模拟buildCommonTagText返回我们创建的标签
        doReturn(mockLabel).when(spyStrategy).buildCommonTagText(anyString(), isNull(), anyBoolean());
        // 使用反射调用私有方法
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalTextTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        RichLabelModel result = (RichLabelModel) method.invoke(spyStrategy, req, promoPriceM);
        // 断言
        assertNotNull("版本边界测试应返回非null结果", result);
        assertEquals(RichLabelStyleEnum.DP_MAGICAL_MEMBER.getType(), result.getStyle());
        assertEquals("共省10", result.getText());
    }
}