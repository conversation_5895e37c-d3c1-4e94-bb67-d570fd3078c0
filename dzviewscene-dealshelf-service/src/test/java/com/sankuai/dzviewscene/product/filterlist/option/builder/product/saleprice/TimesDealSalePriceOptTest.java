package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

/**
 * @author: created by hang.yu on 2024/3/6 14:50
 */
@RunWith(MockitoJUnitRunner.class)
public class TimesDealSalePriceOptTest {

    @Test
    public void testCompute() {
        ActivityCxt context = mock(ActivityCxt.class);

        TimesDealSalePriceOpt opt = new TimesDealSalePriceOpt();
        String compute = opt.compute(context, ProductSalePriceVP.Param.builder().productM(new ProductM()).build(), null);
        Assert.assertNull(compute);

        ProductM productM = new ProductM();
        productM.setSalePrice(BigDecimal.TEN);
        ProductSalePriceVP.Param param = ProductSalePriceVP.Param.builder().productM(productM).build();
        compute = opt.compute(context, param, null);
        Assert.assertNotNull(compute);
    }

}