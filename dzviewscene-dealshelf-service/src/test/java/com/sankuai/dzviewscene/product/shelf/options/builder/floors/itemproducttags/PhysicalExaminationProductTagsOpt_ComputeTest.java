package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.dianping.vc.sdk.codec.JsonCodec;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class PhysicalExaminationProductTagsOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PhysicalExaminationProductTagsOpt.Config config;

    @Test
    public void testComputeBadRequest() throws Throwable {
        PhysicalExaminationProductTagsOpt opt = new PhysicalExaminationProductTagsOpt();
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().build();
        List<String> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeBookInfoBlank() throws Throwable {
        PhysicalExaminationProductTagsOpt opt = new PhysicalExaminationProductTagsOpt();
        ProductM productM = new ProductM();
        List<TagM> productTagList = new ArrayList<>();
        TagM tagM = new TagM();
        tagM.setId("100208251");
        productTagList.add(tagM);
        productM.setProductTagList(productTagList);
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().productM(productM).build();
        List<String> result = opt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeProductTagsNotEmpty() throws Throwable {
        PhysicalExaminationProductTagsOpt opt = new PhysicalExaminationProductTagsOpt();
        ProductM productM = new ProductM();
        List<TagM> productTagList = new ArrayList<>();
        TagM tagM = new TagM();
        tagM.setId("100208251");
        productTagList.add(tagM);
        productM.setProductTagList(productTagList);
        List<AttrM> extAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dentistryPlantCheckTag");
        attrM.setValue(JsonCodec.encode(Arrays.asList("tag1", "tag2")));
        extAttrs.add(attrM);
        productM.setExtAttrs(extAttrs);
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().productM(productM).build();
        List<String> result = opt.compute(context, param, config);
        assertEquals(2, result.size());
    }

//    @Test
//    public void testComputeProductTagsEmpty() throws Throwable {
//        // This test seems redundant with testComputeBookInfoBlank and could be considered for removal or modification
//        testComputeBookInfoBlank();
//    }
}
