package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.MarketingActivityStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemCommonSubTitleOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemCommonSubTitleOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import static org.mockito.ArgumentMatchers.*;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.mockito.MockedStatic;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemCommonSubTitleOptTest {

    @InjectMocks
    private UnifiedShelfItemCommonSubTitleOpt unifiedShelfItemCommonSubTitleOpt;

    @Mock
    private ProductActivityM productActivityM;

    @Mock
    private ProductM productM;

    @Mock
    private Config config;

    @Mock
    private ProductTagStrategyFactory productTagStrategyFactory;

    /**
     * 测试getProductActivityM方法，当productActivityMS为空时，应返回null
     */
    @Test(expected = NullPointerException.class)
    public void testGetProductActivityMWhenProductActivityMSIsNull() throws Throwable {
        unifiedShelfItemCommonSubTitleOpt.getProductActivityM(null);
    }

    /**
     * 测试getProductActivityM方法，当productActivityMS不为空，但是没有任何一个ProductActivityM对象的exposurePromotionType在MarketingActivityStrategy.exposurePromotionTypes中时，应返回null
     */
    @Test
    public void testGetProductActivityMWhenNoMatchedPromotionType() throws Throwable {
        // Use a promotion type that definitely won't match (-1)
        when(productActivityM.getExposurePromotionType()).thenReturn(-1);
        assertNull(unifiedShelfItemCommonSubTitleOpt.getProductActivityM(Arrays.asList(productActivityM)));
    }

    /**
     * 测试getProductActivityM方法，当productActivityMS不为空，且存在至少一个ProductActivityM对象的exposurePromotionType在MarketingActivityStrategy.exposurePromotionTypes中时，应返回第一个找到的ProductActivityM对象
     */
    @Test
    public void testGetProductActivityMWhenMatchedPromotionTypeExists() throws Throwable {
        // Use a promotion type that exists in MarketingActivityStrategy.exposurePromotionTypes
        when(productActivityM.getExposurePromotionType()).thenReturn(1);
        assertEquals(productActivityM, unifiedShelfItemCommonSubTitleOpt.getProductActivityM(Arrays.asList(productActivityM)));
    }

    @Test
    public void testGetRecommendTagConfigIsNull() throws Throwable {
        String result = unifiedShelfItemCommonSubTitleOpt.getRecommendTag(productM, null);
        assertNull(result);
    }

    @Test
    public void testGetRecommendTagEnableRecommendTagIsFalse() throws Throwable {
        when(config.isEnableRecommendTag()).thenReturn(false);
        String result = unifiedShelfItemCommonSubTitleOpt.getRecommendTag(productM, config);
        assertNull(result);
    }

    @Test
    public void testGetRecommendTagTradeRateTagIsNotEmpty() throws Throwable {
        when(config.isEnableRecommendTag()).thenReturn(true);
        when(productM.getAttr("tradeCountRate")).thenReturn("tradeRateTag");
        String result = unifiedShelfItemCommonSubTitleOpt.getRecommendTag(productM, config);
        assertEquals("tradeRateTag", result);
    }

    @Test
    public void testGetRecommendTagRecommendTagsIsNotEmpty() throws Throwable {
        when(config.isEnableRecommendTag()).thenReturn(true);
        when(productM.getAttr("tradeCountRate")).thenReturn("");
        when(productM.getAttr("recommend_product_tags_list")).thenReturn("[\"recommendTag\"]");
        String result = unifiedShelfItemCommonSubTitleOpt.getRecommendTag(productM, config);
        assertEquals("recommendTag", result);
    }

    @Test
    public void testGetRecommendTagAllEmpty() throws Throwable {
        when(config.isEnableRecommendTag()).thenReturn(true);
        when(productM.getAttr("tradeCountRate")).thenReturn("");
        when(productM.getAttr("recommend_product_tags_list")).thenReturn("[]");
        String result = unifiedShelfItemCommonSubTitleOpt.getRecommendTag(productM, config);
        assertNull(result);
    }

    @Test
    public void testGetActivityTag_WhenEnableActivityTagFalse_ShouldReturnNull() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(false);
        ItemSubTitleVO result = unifiedShelfItemCommonSubTitleOpt.getActivityTag(context, param, config);
        assertNull("Should return null when enableActivityTag is false", result);
    }

    @Test
    public void testGetActivityTag_WhenProductMIsNull_ShouldReturnNull() throws Throwable {
        ActivityCxt context = new ActivityCxt();
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(null);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        ItemSubTitleVO result = unifiedShelfItemCommonSubTitleOpt.getActivityTag(context, param, config);
        assertNull("Should return null when productM is null", result);
    }

    @Test
    public void testGetActivityTag_WhenDouHuMListIsEmpty_ShouldReturnNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(new ArrayList<>());
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            ItemSubTitleVO result = unifiedShelfItemCommonSubTitleOpt.getActivityTag(context, param, config);
            assertNull("Should return null when douHuMList doesn't hit any SK", result);
        }
    }

    @Test
    public void testGetActivityTag_WhenHitAnySkReturnsFalse_ShouldReturnNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(anyList(), anyList())).thenReturn(false);
            ItemSubTitleVO result = unifiedShelfItemCommonSubTitleOpt.getActivityTag(context, param, config);
            assertNull("Should return null when hitAnySk returns false", result);
        }
    }

    @Test
    public void testGetActivityTag_WhenProductActivityMSIsEmpty_ShouldReturnNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        productM.setActivities(new ArrayList<>());
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(productM.getActivities())).thenReturn(true);
            ItemSubTitleVO result = unifiedShelfItemCommonSubTitleOpt.getActivityTag(context, param, config);
            assertNull("Should return null when productActivityMS is empty", result);
        }
    }

    @Test
    public void testGetActivityTag_WhenGetProductActivityMReturnsNull_ShouldReturnNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        List<ProductActivityM> activities = Arrays.asList(new ProductActivityM());
        productM.setActivities(activities);
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        UnifiedShelfItemCommonSubTitleOpt spyOpt = spy(unifiedShelfItemCommonSubTitleOpt);
        doReturn(null).when(spyOpt).getProductActivityM(activities);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(activities)).thenReturn(false);
            ItemSubTitleVO result = spyOpt.getActivityTag(context, param, config);
            assertNull("Should return null when getProductActivityM returns null", result);
        }
    }

    @Test
    public void testGetActivityTag_WhenActivityPicUrlMapEmptyAndLabelTextEmpty_ShouldReturnNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        activityM.setActivityPicUrlMap(new HashMap<>());
        activityM.setTextMap(new HashMap<>());
        List<ProductActivityM> activities = Arrays.asList(activityM);
        productM.setActivities(activities);
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        config.setNewJoinType(1);
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        UnifiedShelfItemCommonSubTitleOpt spyOpt = spy(unifiedShelfItemCommonSubTitleOpt);
        doReturn(activityM).when(spyOpt).getProductActivityM(activities);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
            MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
            MockedStatic<org.apache.commons.collections.MapUtils> mapUtilsMock = mockStatic(org.apache.commons.collections.MapUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(activities)).thenReturn(false);
            mapUtilsMock.when(() -> org.apache.commons.collections.MapUtils.isNotEmpty(anyMap())).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isNotBlank(anyString())).thenReturn(false);
            ItemSubTitleVO result = spyOpt.getActivityTag(context, param, config);
            assertNull("Should return null when activityPicUrlMap is empty and labelText is empty", result);
        }
    }

    @Test
    public void testGetActivityTag_WhenValidActivityPicUrlDTO_ShouldReturnItemSubTitleVOWithIconTag() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
        activityPicUrlDTO.setUrl("http://example.com/icon.png");
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        activityPicUrlMap.put(ExposurePicUrlKeyEnum.ICON.getKey(), activityPicUrlDTO);
        activityM.setActivityPicUrlMap(activityPicUrlMap);
        List<ProductActivityM> activities = Arrays.asList(activityM);
        productM.setActivities(activities);
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        config.setNewJoinType(1);
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        UnifiedShelfItemCommonSubTitleOpt spyOpt = spy(unifiedShelfItemCommonSubTitleOpt);
        doReturn(activityM).when(spyOpt).getProductActivityM(activities);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
            MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
            MockedStatic<org.apache.commons.collections.MapUtils> mapUtilsMock = mockStatic(org.apache.commons.collections.MapUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(activities)).thenReturn(false);
            mapUtilsMock.when(() -> org.apache.commons.collections.MapUtils.isNotEmpty(activityPicUrlMap)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isNotBlank("http://example.com/icon.png")).thenReturn(true);
            ItemSubTitleVO result = spyOpt.getActivityTag(context, param, config);
            assertNotNull("Should return ItemSubTitleVO when valid ActivityPicUrlDTO exists", result);
            assertEquals("JoinType should be set correctly", 1, result.getJoinType());
            assertNotNull("IconTag should be set", result.getIconTag());
            assertNotNull("Tags should not be null", result.getTags());
            assertTrue("Tags should be empty", result.getTags().isEmpty());
        }
    }

    @Test
    public void testGetActivityTag_WhenLabelTextNotEmpty_ShouldReturnItemSubTitleVOWithStyleTextModel() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        // 设置包含无效图片URL的activityPicUrlMap和有内容的textMap
        ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
        // 设置空URL
        activityPicUrlDTO.setUrl("");
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        activityPicUrlMap.put(ExposurePicUrlKeyEnum.ICON.getKey(), activityPicUrlDTO);
        activityM.setActivityPicUrlMap(activityPicUrlMap);
        Map<String, String> textMap = new HashMap<>();
        textMap.put("labelText", "促销标签");
        activityM.setTextMap(textMap);
        List<ProductActivityM> activities = Collections.singletonList(activityM);
        productM.setActivities(activities);
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        config.setNewJoinType(2);
        List<DouHuM> douHuMList = Collections.singletonList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        UnifiedShelfItemCommonSubTitleOpt spyOpt = spy(unifiedShelfItemCommonSubTitleOpt);
        doReturn(activityM).when(spyOpt).getProductActivityM(activities);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
            MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
            MockedStatic<org.apache.commons.collections.MapUtils> mapUtilsMock = mockStatic(org.apache.commons.collections.MapUtils.class)) {
            // 模拟静态方法调用
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(activities)).thenReturn(false);
            // 确保activityPicUrlMap被认为是非空的，但URL是空的
            mapUtilsMock.when(() -> org.apache.commons.collections.MapUtils.isNotEmpty(activityPicUrlMap)).thenReturn(true);
            // 模拟StringUtils.isNotBlank的具体调用
            // 空URL返回false
            // 空URL返回false
            stringUtilsMock.when(() -> StringUtils.isNotBlank("")).thenReturn(false);
            // labelText返回true
            // labelText返回true
            stringUtilsMock.when(() -> StringUtils.isNotBlank("促销标签")).thenReturn(true);
            ItemSubTitleVO result = spyOpt.getActivityTag(context, param, config);
            assertNotNull("Should return ItemSubTitleVO when labelText is not empty", result);
            assertEquals("JoinType should be set correctly", 2, result.getJoinType());
            assertNotNull("Tags should not be null", result.getTags());
            assertEquals("Should have one tag", 1, result.getTags().size());
            StyleTextModel styleTextModel = result.getTags().get(0);
            assertNotNull("StyleTextModel should not be null", styleTextModel);
            assertEquals("Tag text should match labelText", "促销标签", styleTextModel.getText());
            assertEquals("Tag style should be red background", TextStyleEnum.TEXT_RED_BACKGROUND.getType(), styleTextModel.getStyle());
        }
    }

    @Test
    public void testGetActivityTag_WhenActivityPicUrlDTOUrlIsEmpty_ShouldCheckLabelText() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        ProductM productM = new ProductM();
        ProductActivityM activityM = new ProductActivityM();
        ActivityPicUrlDTO activityPicUrlDTO = new ActivityPicUrlDTO();
        activityPicUrlDTO.setUrl("");
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = new HashMap<>();
        activityPicUrlMap.put(ExposurePicUrlKeyEnum.ICON.getKey(), activityPicUrlDTO);
        activityM.setActivityPicUrlMap(activityPicUrlMap);
        Map<String, String> textMap = new HashMap<>();
        textMap.put("labelText", "备用标签");
        activityM.setTextMap(textMap);
        List<ProductActivityM> activities = Arrays.asList(activityM);
        productM.setActivities(activities);
        UnifiedShelfItemSubTitleVP.Param param = new UnifiedShelfItemSubTitleVP.Param();
        param.setProductM(productM);
        UnifiedShelfItemCommonSubTitleOpt.Config config = new UnifiedShelfItemCommonSubTitleOpt.Config();
        config.setEnableActivityTag(true);
        config.setMassageTagExpSks(Arrays.asList("sk1", "sk2"));
        config.setNewJoinType(3);
        List<DouHuM> douHuMList = Arrays.asList(new DouHuM());
        when(context.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        UnifiedShelfItemCommonSubTitleOpt spyOpt = spy(unifiedShelfItemCommonSubTitleOpt);
        doReturn(activityM).when(spyOpt).getProductActivityM(activities);
        try (MockedStatic<DouHuUtils> douHuUtilsMock = mockStatic(DouHuUtils.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
            MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
            MockedStatic<org.apache.commons.collections.MapUtils> mapUtilsMock = mockStatic(org.apache.commons.collections.MapUtils.class)) {
            douHuUtilsMock.when(() -> DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(activities)).thenReturn(false);
            mapUtilsMock.when(() -> org.apache.commons.collections.MapUtils.isNotEmpty(activityPicUrlMap)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isNotBlank("")).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isNotBlank("备用标签")).thenReturn(true);
            ItemSubTitleVO result = spyOpt.getActivityTag(context, param, config);
            assertNotNull("Should return ItemSubTitleVO when labelText is available as fallback", result);
            assertEquals("JoinType should be set correctly", 3, result.getJoinType());
            assertNotNull("Tags should not be null", result.getTags());
            assertEquals("Should have one tag", 1, result.getTags().size());
            StyleTextModel styleTextModel = result.getTags().get(0);
            assertEquals("Tag text should match labelText", "备用标签", styleTextModel.getText());
            assertEquals("Tag style should be red background", TextStyleEnum.TEXT_RED_BACKGROUND.getType(), styleTextModel.getStyle());
        }
    }
}