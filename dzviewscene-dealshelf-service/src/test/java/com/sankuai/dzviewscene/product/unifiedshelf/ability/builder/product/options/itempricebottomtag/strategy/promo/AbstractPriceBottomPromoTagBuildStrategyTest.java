package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.AbstractPriceBottomTagBuildStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.SimplifyPricePromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class AbstractPriceBottomPromoTagBuildStrategyTest {

    private TestPriceBottomPromoTagBuildStrategy strategy = new TestPriceBottomPromoTagBuildStrategy();

    // Minimal concrete subclass for testing
    private static class TestPriceBottomPromoTagBuildStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

        @Override
        public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
            ShelfTagVO vo = new ShelfTagVO();
            vo.setName("test");
            return vo;
        }

        @Override
        public String getName() {
            return "test";
        }

        @Override
        public String getStrategyDesc() {
            return "desc";
        }

        @Override
        public ShelfTagVO build(PriceBottomTagBuildReq req) {
            return super.build(req);
        }
    }

    @Test
    public void testBuild_NullReq() throws Throwable {
        ShelfTagVO result = strategy.build(null);
        assertNull(result);
    }

    @Test
    public void testBuild_NullProductM() throws Throwable {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(null);
        ShelfTagVO result = strategy.build(req);
        assertNull(result);
    }

    @Test
    public void testBuild_EmptyPromoPrices() throws Throwable {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        ProductM productM = new ProductM();
        productM.setPromoPrices(null);
        req.setProductM(productM);
        ShelfTagVO result1 = strategy.build(req);
        assertNull(result1);
        productM.setPromoPrices(Collections.emptyList());
        ShelfTagVO result2 = strategy.build(req);
        assertNull(result2);
    }

    @Test
    public void testBuild_HitSimplifyPromoTagAndNotRetainedStrategy() throws Throwable {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        ProductM productM = new ProductM();
        productM.setPromoPrices(Collections.singletonList(new ProductPromoPriceM()));
        req.setProductM(productM);
        req.setHitSimplifyPromoTag(true);
        req.setStrategyName("notRetained");
        try (MockedStatic<SimplifyPricePromoUtils> mocked = Mockito.mockStatic(SimplifyPricePromoUtils.class)) {
            mocked.when(() -> SimplifyPricePromoUtils.isRetainedStrategy("notRetained")).thenReturn(false);
            ShelfTagVO result = strategy.build(req);
            assertNull(result);
        }
    }
}