package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemRecommendTagOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private UnifiedShelfItemRecommendTagOpt.Param mockParam;

    @Mock
    private UnifiedShelfItemRecommendTagOpt.Config mockConfig;

    @Mock
    private com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO mockShelfItemVO;

    @Mock
    private com.sankuai.dzviewscene.shelf.platform.common.model.ProductM mockProductM;

    @InjectMocks
    private UnifiedShelfItemRecommendTagOpt unifiedShelfItemRecommendTagOpt;

    @Before
    public void setUp() {
        when(mockParam.getShelfItemVO()).thenReturn(mockShelfItemVO);
        when(mockParam.getProductM()).thenReturn(mockProductM);
    }

    /**
     * Test when hit any massage tag exp sk, should return null
     */
    @Test
    public void testCompute_WithHitDouHu() throws Throwable {
        // arrange
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(null);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("testSk");
        douHuMList.add(douHuM);
        when(mockActivityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
        when(mockConfig.getMassageTagExpSks()).thenReturn(Collections.singletonList("testSk"));
        // act
        ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        assertNull(result);
    }

    /**
     * Test when recommend tags is empty, should return null
     */
    @Test
    public void testCompute_WithEmptyRecommendTags() throws Throwable {
        // arrange
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(null);
        when(mockActivityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(null);
        // PriceAboveTagsUtils.getTradeRateTag() will return null by default
        // PriceAboveTagsUtils.getRecommendTag() will return empty list by default
        // act
        ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        assertNull(result);
    }

    /**
     * Test normal case when all conditions are met
     */
    @Test
    public void testCompute_Normal() throws Throwable {
        // arrange
        when(mockShelfItemVO.getPriceBottomTags()).thenReturn(null);
        when(mockActivityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(null);
        // Mock trade rate tag to return a value
        when(mockProductM.getAttr("tradeCountRate")).thenReturn("90%好评");
        // act
        ItemSpecialTagVO result = unifiedShelfItemRecommendTagOpt.compute(mockActivityCxt, mockParam, mockConfig);
        // assert
        assertNotNull(result);
        assertNotNull(result.getTags());
        assertFalse(result.getTags().isEmpty());
        // Verify the tag was created with expected colors
        ShelfTagVO tag = result.getTags().get(0);
        assertEquals(ColorUtils.colorFFF1EC, tag.getText().getBackgroundColor());
        assertEquals(ColorUtils.colorFF4B10, tag.getText().getTextColor());
    }
}