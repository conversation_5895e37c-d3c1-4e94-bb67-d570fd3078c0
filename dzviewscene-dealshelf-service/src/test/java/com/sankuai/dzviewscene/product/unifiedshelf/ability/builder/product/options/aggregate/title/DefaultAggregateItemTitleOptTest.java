package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.title;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultAggregateItemTitleOptTest {

    @InjectMocks
    private DefaultAggregateItemTitleOpt titleOpt;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateItemTitleVP.Param param;
    private DefaultAggregateItemTitleOpt.Config config;
    private ProductHierarchyNodeM nodeM;
    private Map<String, ProductM> productMMap;

    @Before
    public void setUp() {
        // 初始化配置
        config = new DefaultAggregateItemTitleOpt.Config();

        // 初始化参数
        param = AggregateItemTitleVP.Param.builder().build();
        nodeM = new ProductHierarchyNodeM();
        productMMap = Maps.newHashMap();

        param.setNodeM(nodeM);
        param.setProductMMap(productMMap);
    }

    /**
     * 测试SPU节点的标题生成
     */
    @Test
    public void testComputeForSpuNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.SPT_SPU.getType());
        ProductHierarchyNodeM child1 = new ProductHierarchyNodeM();
        child1.setProductId(2);
        child1.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM child1ProductM = new ProductM();
        child1ProductM.setProductId(2);
        child1ProductM.setProductType(ProductTypeEnum.DEAL.getType());
        DealProductSpuDTO dealProductSpuDTO = new DealProductSpuDTO();
        dealProductSpuDTO.setSpuId(1);
        dealProductSpuDTO.setSpuName("SPU商品标题");
        child1ProductM.setSpuMList(Lists.newArrayList(dealProductSpuDTO));
        productMMap.put("1_2", child1ProductM);
        nodeM.setChildren(Lists.newArrayList(child1));

        // act
        List<StyleTextModel> result = titleOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("SPU商品标题", result.get(0).getText());
    }

    /**
     * 测试普通节点的标题生成
     */
    @Test
    public void testComputeForNormalNode() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productM.setTitle("普通商品标题");
        productMMap.put("1_1", productM);

        // act
        List<StyleTextModel> result = titleOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("普通商品标题", result.get(0).getText());
    }

    /**
     * 测试商品标题为空的场景
     */
    @Test
    public void testComputeWithEmptyTitle() {
        // arrange
        nodeM.setProductId(1);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productM.setTitle(null); // 商品标题为空
        productMMap.put("1_1", productM);

        // act
        List<StyleTextModel> result = titleOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }
}