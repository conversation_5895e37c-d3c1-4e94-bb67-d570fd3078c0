package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.headpic;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfPicUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemPicVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicOpt.SCALE_PIC_HEIGHT;
import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicOpt.SCALE_PIC_WIDTH;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;

/**
 * DefaultAggregateItemPicOpt的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class DefaultAggregateItemPicOptTest {

    @InjectMocks
    private DefaultAggregateItemPicOpt picOpt;

    @Mock
    private ActivityCxt activityCxt;

    private AggregateItemPicVP.Param param;
    private DefaultAggregateItemPicOpt.Config config;
    private ProductHierarchyNodeM nodeM;
    private Map<String, ProductM> productMMap;

    @Before
    public void setUp() {
        // 初始化配置
        config = new DefaultAggregateItemPicOpt.Config();
        
        // 初始化参数
        param = AggregateItemPicVP.Param.builder().build();
        nodeM = new ProductHierarchyNodeM();
        productMMap = Maps.newHashMap();
        
        param.setNodeM(nodeM);
        param.setProductMMap(productMMap);
    }

    /**
     * 测试普通节点的图片处理
     */
    @Test
    public void testComputeForNormalNode() {
        // arrange
        String picUrl = "http://example.com/pic.jpg";
        String httpsUrl = "https://example.com/pic.jpg";
        nodeM.setProductId(1);
        nodeM.setProductType(1);
        ProductM productM = new ProductM();
        productM.setPicUrl(picUrl);
        productMMap.put("1_1", productM);

        try (MockedStatic<UnifiedShelfPicUtils> utilsMockedStatic = mockStatic(UnifiedShelfPicUtils.class)) {
            utilsMockedStatic.when(() -> UnifiedShelfPicUtils.toHttpsUrl(
                    eq(picUrl),
                    eq(SCALE_PIC_WIDTH),
                    eq(SCALE_PIC_HEIGHT),
                    eq(PictureURLBuilders.ScaleType.Cut)
            )).thenReturn(httpsUrl);

            // act
            PictureModel result = picOpt.compute(activityCxt, param, config);

            // assert
            assertNotNull(result);
            assertEquals(httpsUrl, result.getPicUrl());
            assertEquals(1, result.getAspectRadio(), 0.001);
        }
    }

    /**
     * 测试SPU节点的图片处理（选择销量最高的商品图片）
     */
    @Test
    public void testComputeForSpuNode() {
        // arrange
        String picUrl1 = "http://example.com/pic1.jpg";
        String picUrl2 = "http://example.com/pic2.jpg";
        String httpsUrl = "https://example.com/pic2.jpg";
        
        // 设置为SPU节点
        nodeM.setProductId(2);
        nodeM.setProductType(ProductTypeEnum.SPT_SPU.getType());

        // 创建两个子节点
        ProductHierarchyNodeM child1 = new ProductHierarchyNodeM();
        child1.setProductId(3);
        child1.setProductType(ProductTypeEnum.DEAL.getType());
        ProductHierarchyNodeM child2 = new ProductHierarchyNodeM();
        child2.setProductId(4);
        child2.setProductType(ProductTypeEnum.DEAL.getType());
        nodeM.setChildren(Lists.newArrayList(child1, child2));
        
        // 创建两个商品，sales不同
        ProductM product1 = new ProductM();
        product1.setPicUrl(picUrl1);
        ProductSaleM sale1 = new ProductSaleM();
        sale1.setSale(100);
        product1.setSale(sale1);
        
        ProductM product2 = new ProductM();
        product2.setPicUrl(picUrl2);
        ProductSaleM sale2 = new ProductSaleM();
        sale2.setSale(200);
        product2.setSale(sale2);
        
        productMMap.put("1_3", product1);
        productMMap.put("1_4", product2);

        try (MockedStatic<UnifiedShelfPicUtils> utilsMockedStatic = mockStatic(UnifiedShelfPicUtils.class)) {
            utilsMockedStatic.when(() -> UnifiedShelfPicUtils.toHttpsUrl(
                    eq(picUrl2),
                    eq(SCALE_PIC_WIDTH),
                    eq(SCALE_PIC_HEIGHT),
                    eq(PictureURLBuilders.ScaleType.Cut)
            )).thenReturn(httpsUrl);

            // act
            PictureModel result = picOpt.compute(activityCxt, param, config);

            // assert
            assertNotNull(result);
            assertEquals(httpsUrl, result.getPicUrl());
            assertEquals(1, result.getAspectRadio(), 0.001);
        }
    }

    /**
     * 测试商品图片为空的场景
     */
    @Test
    public void testComputeWithNullPicUrl() {
        // arrange
        nodeM.setProductId(6);
        nodeM.setProductType(ProductTypeEnum.DEAL.getType());
        ProductM productM = new ProductM();
        productM.setPicUrl(null);
        productMMap.put("1_6", productM);

        // act
        PictureModel result = picOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试商品为空的场景
     */
    @Test
    public void testComputeWithNullProduct() {
        // arrange
        nodeM.setProductId(7);
        nodeM.setProductId(ProductTypeEnum.DEAL.getType());
        productMMap.put("test", null);

        // act
        PictureModel result = picOpt.compute(activityCxt, param, config);

        // assert
        assertNull(result);
    }
}
