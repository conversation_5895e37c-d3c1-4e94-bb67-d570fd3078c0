package com.sankuai.dzviewscene.product.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import java.lang.reflect.Method;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantMemberPromoUtilsUsedMerchantMemberDiscountTest {

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    @Test(expected = NullPointerException.class)
    public void testUsedMerchantMemberDiscountProductMIsNull() throws Throwable {
        // Expecting NullPointerException based on the current implementation
        MerchantMemberPromoUtils.usedMerchantMemberDiscount(null, cardM);
    }

    @Test
    public void testUsedMerchantMemberDiscountPromoPricesIsEmpty() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountNoDirectPromo() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(1);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountCardMIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, null));
    }

    @Test
    public void testUsedMerchantMemberDiscountUserCardListIsEmpty() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountHasDirectPromoButCardPromoIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testMemberDiscountHasDirectPromoAndCardPromoButProductPromoPriceIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountHasDirectPromoAndCardPromoAndProductPromoPriceIsNotNullButBestPromoPriceIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testMerchantMemberountHasPromoAndCardPromoButProductPromoPriceIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    @Test
    public void testUsedMerchantMemberDiscountHasDirectPromoAndCardPromoButProductPromoPriceIsNull() throws Throwable {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setPromoType(0);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPrice));
        assertFalse(MerchantMemberPromoUtils.usedMerchantMemberDiscount(productM, cardM));
    }

    private boolean invokeIsPaidTypeMemberPromo(MerchantMemberPromoUtils.MerchantMemberDealModel model) throws Exception {
        Method method = MerchantMemberPromoUtils.class.getDeclaredMethod("isPaidTypeMemberPromo", MerchantMemberPromoUtils.MerchantMemberDealModel.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, model);
    }

    @Test
    public void testIsPaidTypeMemberPromoWhenMerchantMemberPromoIsNull() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = null;
        // act
        boolean result = invokeIsPaidTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPaidTypeMemberPromoWhenChargeTypeIsNull() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = new MerchantMemberPromoUtils.MerchantMemberDealModel();
        merchantMemberPromo.setChargeType(null);
        // act
        boolean result = invokeIsPaidTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPaidTypeMemberPromoWhenChargeTypeIsNotPayType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = new MerchantMemberPromoUtils.MerchantMemberDealModel();
        // FREE_TYPE
        merchantMemberPromo.setChargeType(1);
        // act
        boolean result = invokeIsPaidTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsPaidTypeMemberPromoWhenChargeTypeIsPayType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = new MerchantMemberPromoUtils.MerchantMemberDealModel();
        // PAY_TYPE
        merchantMemberPromo.setChargeType(2);
        // act
        boolean result = invokeIsPaidTypeMemberPromo(merchantMemberPromo);
        // assert
        assertTrue(result);
    }

    private boolean invokeIsFreeTypeMemberPromo(MerchantMemberPromoUtils.MerchantMemberDealModel model) throws Exception {
        Method method = MerchantMemberPromoUtils.class.getDeclaredMethod("isFreeTypeMemberPromo", MerchantMemberPromoUtils.MerchantMemberDealModel.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, model);
    }

    @Test
    public void testIsFreeTypeMemberPromo_NullInput() throws Throwable {
        // act
        boolean result = invokeIsFreeTypeMemberPromo(null);
        // assert
        assertFalse("Should return false for null input", result);
    }

    @Test
    public void testIsFreeTypeMemberPromo_NullChargeType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = spy(new MerchantMemberPromoUtils.MerchantMemberDealModel());
        when(merchantMemberPromo.getChargeType()).thenReturn(null);
        // act
        boolean result = invokeIsFreeTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse("Should return false when chargeType is null", result);
        // Only called once due to short-circuit
        verify(merchantMemberPromo, times(1)).getChargeType();
    }

    @Test
    public void testIsFreeTypeMemberPromo_FreeType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = spy(new MerchantMemberPromoUtils.MerchantMemberDealModel());
        when(merchantMemberPromo.getChargeType()).thenReturn(1);
        // act
        boolean result = invokeIsFreeTypeMemberPromo(merchantMemberPromo);
        // assert
        assertTrue("Should return true when chargeType is FREE_TYPE", result);
        // Called twice (null check + value comparison)
        verify(merchantMemberPromo, times(2)).getChargeType();
    }

    @Test
    public void testIsFreeTypeMemberPromo_NonFreeType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = spy(new MerchantMemberPromoUtils.MerchantMemberDealModel());
        when(merchantMemberPromo.getChargeType()).thenReturn(2);
        // act
        boolean result = invokeIsFreeTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse("Should return false when chargeType is not FREE_TYPE", result);
        // Called twice (null check + value comparison)
        verify(merchantMemberPromo, times(2)).getChargeType();
    }

    @Test
    public void testIsFreeTypeMemberPromo_PayType() throws Throwable {
        // arrange
        MerchantMemberPromoUtils.MerchantMemberDealModel merchantMemberPromo = spy(new MerchantMemberPromoUtils.MerchantMemberDealModel());
        when(merchantMemberPromo.getChargeType()).thenReturn(2);
        // act
        boolean result = invokeIsFreeTypeMemberPromo(merchantMemberPromo);
        // assert
        assertFalse("Should return false when chargeType is PAY_TYPE", result);
        verify(merchantMemberPromo, times(2)).getChargeType();
    }
}