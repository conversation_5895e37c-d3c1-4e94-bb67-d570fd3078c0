package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * AfterPayTagProcessHandler单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class AfterPayTagProcessHandlerTest {

    @InjectMocks
    private AfterPayTagProcessHandler afterPayTagProcessHandler;

    @Mock
    private ActivityCxt activityCxt;

    private Map<String, ProductM> productMMap;
    private ShelfItemVO shelfItemVO;
    private List<DouHuM> douHuMList;

    @Before
    public void setUp() {
        // 初始化参数
        productMMap = Maps.newHashMap();
        shelfItemVO = new ShelfItemVO();
        douHuMList = new ArrayList<>();
    }

    /**
     * 测试getName方法
     */
    @Test
    public void testGetName() {
        // act
        String name = afterPayTagProcessHandler.getName();
        
        // assert
        assertEquals("AfterPayTagProcessHandler", name);
    }

    /**
     * 测试process方法，当不是SPU节点时
     */
    @Test
    public void testProcessNotSpuNode() {
        // arrange
        shelfItemVO.setItemType(ProductTypeEnum.DEAL.getType());
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();
        
        // act
        ShelfItemVO result = afterPayTagProcessHandler.process(activityCxt, context);
        
        // assert
        assertNotNull(result);
        assertEquals(shelfItemVO, result);
    }

    /**
     * 测试process方法，当是SPU节点时
     */
    @Test
    public void testProcessSpuNode() {
        // arrange
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();
        
        try (MockedStatic<UserAfterPayPaddingHandler> userAfterPayPaddingHandlerMockedStatic = Mockito.mockStatic(UserAfterPayPaddingHandler.class);
             MockedStatic<DouHuUtils> douHuUtilsMockedStatic = Mockito.mockStatic(DouHuUtils.class)) {
            
            // 设置静态方法返回值
            when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
            userAfterPayPaddingHandlerMockedStatic.when(() -> UserAfterPayPaddingHandler.getUserExposure(activityCxt)).thenReturn(true);
            douHuUtilsMockedStatic.when(() -> DouHuUtils.hitAnySk(douHuMList)).thenReturn(true);
            
            // act
            ShelfItemVO result = afterPayTagProcessHandler.process(activityCxt, context);
            
            // assert
            assertNotNull(result);
            assertEquals(shelfItemVO, result);
        }
    }

    /**
     * 测试appendAfterPayProductTag方法，当用户曝光为false时
     */
    @Test
    public void testAppendAfterPayProductTagWhenUserExposureFalse() {
        try (MockedStatic<UserAfterPayPaddingHandler> userAfterPayPaddingHandlerMockedStatic = Mockito.mockStatic(UserAfterPayPaddingHandler.class)) {
            
            // 设置静态方法返回值
            when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
            userAfterPayPaddingHandlerMockedStatic.when(() -> UserAfterPayPaddingHandler.getUserExposure(activityCxt)).thenReturn(false);
            
            // act
            afterPayTagProcessHandler.appendAfterPayProductTag(shelfItemVO, activityCxt, productMMap);
            
            // assert - 方法应该直接返回，不做任何操作
            verify(activityCxt, times(1)).getParam(ShelfActivityConstants.Params.douHus);
        }
    }

    /**
     * 测试appendAfterPayProductTag方法，当hitAnySk为false时
     */
    @Test
    public void testAppendAfterPayProductTagWhenHitAnySkFalse() {
        try (MockedStatic<UserAfterPayPaddingHandler> userAfterPayPaddingHandlerMockedStatic = Mockito.mockStatic(UserAfterPayPaddingHandler.class);
             MockedStatic<DouHuUtils> douHuUtilsMockedStatic = Mockito.mockStatic(DouHuUtils.class)) {
            
            // 设置静态方法返回值
            when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
            userAfterPayPaddingHandlerMockedStatic.when(() -> UserAfterPayPaddingHandler.getUserExposure(activityCxt)).thenReturn(true);
            douHuUtilsMockedStatic.when(() -> DouHuUtils.hitAnySk(douHuMList)).thenReturn(false);
            
            // act
            afterPayTagProcessHandler.appendAfterPayProductTag(shelfItemVO, activityCxt, productMMap);
            
            // assert - 方法应该直接返回，不做任何操作
            verify(activityCxt, times(1)).getParam(ShelfActivityConstants.Params.douHus);
        }
    }

    /**
     * 测试appendAfterPayProductTag方法，当条件满足且有子项时
     */
    @Test
    public void testAppendAfterPayProductTagWithSubItems() {
        // arrange
        ShelfItemVO subItem = new ShelfItemVO();
        subItem.setItemId(2);
        subItem.setItemType(ProductTypeEnum.DEAL.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(subItem));
        
        ProductM productM = new ProductM();
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.TIME_CARD.getType());
        String key = ProductHierarchyNodeM.identityKey(subItem.getItemType(), subItem.getItemId());
        productMMap.put(key, productM);
        
        try (MockedStatic<UserAfterPayPaddingHandler> userAfterPayPaddingHandlerMockedStatic = Mockito.mockStatic(UserAfterPayPaddingHandler.class);
             MockedStatic<DouHuUtils> douHuUtilsMockedStatic = Mockito.mockStatic(DouHuUtils.class);
             MockedStatic<ProductMAttrUtils> productMAttrUtilsMockedStatic = Mockito.mockStatic(ProductMAttrUtils.class)) {
            
            // 设置静态方法返回值
            when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);
            userAfterPayPaddingHandlerMockedStatic.when(() -> UserAfterPayPaddingHandler.getUserExposure(activityCxt)).thenReturn(true);
            douHuUtilsMockedStatic.when(() -> DouHuUtils.hitAnySk(douHuMList,Lists.newArrayList("EXP2025032100003_c"))).thenReturn(true);
            productMAttrUtilsMockedStatic.when(() -> ProductMAttrUtils.isAfterPayProduct(productM)).thenReturn(true);
            
            // act
            afterPayTagProcessHandler.appendAfterPayProductTag(shelfItemVO, activityCxt, productMMap);
            
            // assert
            assertNotNull(shelfItemVO.getSubItems().get(0).getProductTags());
            assertEquals(1, shelfItemVO.getSubItems().get(0).getProductTags().getTags().size());
            assertEquals("先用后付", shelfItemVO.getSubItems().get(0).getProductTags().getTags().get(0).getText());
        }
    }

    /**
     * 测试appendAfterPaySpuTag方法，当productTags为null时
     */
    @Test
    public void testAppendAfterPaySpuTagWhenProductTagsNull() {
        // act
        ItemSubTitleVO result = afterPayTagProcessHandler.appendAfterPaySpuTag(null);
        
        // assert
        assertNotNull(result);
        assertEquals(0, result.getJoinType());
        assertEquals(1, result.getTags().size());
        assertEquals("先用后付", result.getTags().get(0).getText());
    }

    /**
     * 测试appendAfterPaySpuTag方法，当productTags不为null时
     */
    @Test
    public void testAppendAfterPaySpuTagWhenProductTagsNotNull() {
        // arrange
        ItemSubTitleVO productTags = new ItemSubTitleVO();
        StyleTextModel existingTag = new StyleTextModel();
        existingTag.setText("现有标签");
        productTags.setTags(Lists.newArrayList(existingTag));
        
        // act
        ItemSubTitleVO result = afterPayTagProcessHandler.appendAfterPaySpuTag(productTags);
        
        // assert
        assertNotNull(result);
        assertEquals(2, result.getTags().size());
        assertEquals("先用后付", result.getTags().get(0).getText());
        assertEquals("现有标签", result.getTags().get(1).getText());
    }

    /**
     * 测试buildPreAfterPayTag方法
     */
    @Test
    public void testBuildPreAfterPayTag() {
        // act
        StyleTextModel result = afterPayTagProcessHandler.buildPreAfterPayTag();
        
        // assert
        assertNotNull(result);
        assertEquals("先用后付", result.getText());
    }
}
