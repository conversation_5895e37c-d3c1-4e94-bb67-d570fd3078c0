package com.sankuai.dzviewscene.product.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;

@RunWith(MockitoJUnitRunner.class)
public class PriceUtilsTest {

    private MockedStatic<Lion> mockedLion;

    @Before
    public void setUp() {
        // Initialize the static mock once
        mockedLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        // Close the static mock after each test
        mockedLion.close();
    }

    /**
     * Test when Lion config is disabled - should return false
     */
    @Test
    public void testUsedNationalSubsidy_LionConfigDisabled() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Arrays.asList(createPromoPriceWithSubsidy()));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(false);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
        mockedLion.verify(() -> Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true));
    }

    /**
     * Test when ProductM is null - should return false
     */
    @Test
    public void testUsedNationalSubsidy_NullProduct() throws Throwable {
        // arrange
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(null);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has empty promoPrices - should return false
     */
    @Test
    public void testUsedNationalSubsidy_EmptyPromoPrices() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Collections.emptyList());
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has promoPrices but no matching national subsidy - should return false
     */
    @Test
    public void testUsedNationalSubsidy_NoMatchingSubsidy() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        promoPrice.setPromoPrice(BigDecimal.valueOf(90));
        productM.setPromoPrices(Arrays.asList(promoPrice));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has promoPrices with matching national subsidy - should return true
     */
    @Test
    public void testUsedNationalSubsidy_WithMatchingSubsidy() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Arrays.asList(createPromoPriceWithSubsidy()));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertTrue(result);
    }

    private ProductPromoPriceM createPromoPriceWithSubsidy() {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        BigDecimal price = BigDecimal.valueOf(100);
        promoPrice.setNationalSubsidyPrice(price);
        promoPrice.setPromoPrice(price);
        return promoPrice;
    }

    @Test
    public void testUsedNewCustomerDiscountWithNullProduct() {
        // arrange
        ProductM productM = null;
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithNullPromoPrices() {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(null);
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithEmptyPromoPrices() {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Collections.emptyList());
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithNullPromoItemList() {
        // arrange
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(null);
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithEmptyPromoItemList() {
        // arrange
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Collections.emptyList());
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithNoMatchingPromoType() {
        // arrange
        ProductM productM = new ProductM();
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromoShowType("OTHER_DISCOUNT");
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithMatchingPromoType() {
        // arrange
        ProductM productM = new ProductM();
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromoShowType("NEW_CUSTOMER_DISCOUNT");
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertTrue(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithMultiplePromoPricesOneMatching() {
        // arrange
        ProductM productM = new ProductM();
        // First promo price with non-matching promo item
        PromoItemM promoItem1 = new PromoItemM();
        promoItem1.setPromoShowType("OTHER_DISCOUNT");
        ProductPromoPriceM promoPriceM1 = new ProductPromoPriceM();
        promoPriceM1.setPromoItemList(Collections.singletonList(promoItem1));
        // Second promo price with matching promo item
        PromoItemM promoItem2 = new PromoItemM();
        promoItem2.setPromoShowType("NEW_CUSTOMER_DISCOUNT");
        ProductPromoPriceM promoPriceM2 = new ProductPromoPriceM();
        promoPriceM2.setPromoItemList(Collections.singletonList(promoItem2));
        productM.setPromoPrices(Arrays.asList(promoPriceM1, promoPriceM2));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertTrue(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithMultiplePromoItemsOneMatching() {
        // arrange
        ProductM productM = new ProductM();
        PromoItemM promoItem1 = new PromoItemM();
        promoItem1.setPromoShowType("OTHER_DISCOUNT");
        PromoItemM promoItem2 = new PromoItemM();
        promoItem2.setPromoShowType("NEW_CUSTOMER_DISCOUNT");
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Arrays.asList(promoItem1, promoItem2));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertTrue(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithMixedNullAndNonNullPromoItemLists() {
        // arrange
        ProductM productM = new ProductM();
        // First promo price with null promo item list
        ProductPromoPriceM promoPriceM1 = new ProductPromoPriceM();
        promoPriceM1.setPromoItemList(null);
        // Second promo price with empty promo item list
        ProductPromoPriceM promoPriceM2 = new ProductPromoPriceM();
        promoPriceM2.setPromoItemList(Collections.emptyList());
        // Third promo price with matching promo item
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromoShowType("NEW_CUSTOMER_DISCOUNT");
        ProductPromoPriceM promoPriceM3 = new ProductPromoPriceM();
        promoPriceM3.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Arrays.asList(promoPriceM1, promoPriceM2, promoPriceM3));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertTrue(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithNullPromoShowType() {
        // arrange
        ProductM productM = new ProductM();
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromoShowType(null);
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testUsedNewCustomerDiscountWithEmptyPromoShowType() {
        // arrange
        ProductM productM = new ProductM();
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromoShowType("");
        ProductPromoPriceM promoPriceM = new ProductPromoPriceM();
        promoPriceM.setPromoItemList(Collections.singletonList(promoItem));
        productM.setPromoPrices(Collections.singletonList(promoPriceM));
        // act
        boolean result = PriceUtils.usedNewCustomerDiscount(productM);
        // assert
        assertFalse(result);
    }
}