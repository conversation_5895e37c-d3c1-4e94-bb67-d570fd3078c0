package com.sankuai.dzviewscene.product.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PriceUtilsTest {

    private MockedStatic<Lion> mockedLion;

    @Before
    public void setUp() {
        // Initialize the static mock once
        mockedLion = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        // Close the static mock after each test
        mockedLion.close();
    }

    /**
     * Test when Lion config is disabled - should return false
     */
    @Test
    public void testUsedNationalSubsidy_LionConfigDisabled() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Arrays.asList(createPromoPriceWithSubsidy()));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(false);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
        mockedLion.verify(() -> Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true));
    }

    /**
     * Test when ProductM is null - should return false
     */
    @Test
    public void testUsedNationalSubsidy_NullProduct() throws Throwable {
        // arrange
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(null);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has empty promoPrices - should return false
     */
    @Test
    public void testUsedNationalSubsidy_EmptyPromoPrices() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Collections.emptyList());
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has promoPrices but no matching national subsidy - should return false
     */
    @Test
    public void testUsedNationalSubsidy_NoMatchingSubsidy() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        promoPrice.setNationalSubsidyPrice(BigDecimal.valueOf(100));
        promoPrice.setPromoPrice(BigDecimal.valueOf(90));
        productM.setPromoPrices(Arrays.asList(promoPrice));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductM has promoPrices with matching national subsidy - should return true
     */
    @Test
    public void testUsedNationalSubsidy_WithMatchingSubsidy() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setPromoPrices(Arrays.asList(createPromoPriceWithSubsidy()));
        when(Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.enableNationalSubsidy", true)).thenReturn(true);
        // act
        boolean result = PriceUtils.usedNationalSubsidy(productM);
        // assert
        assertTrue(result);
    }

    private ProductPromoPriceM createPromoPriceWithSubsidy() {
        ProductPromoPriceM promoPrice = new ProductPromoPriceM();
        BigDecimal price = BigDecimal.valueOf(100);
        promoPrice.setNationalSubsidyPrice(price);
        promoPrice.setPromoPrice(price);
        return promoPrice;
    }
}