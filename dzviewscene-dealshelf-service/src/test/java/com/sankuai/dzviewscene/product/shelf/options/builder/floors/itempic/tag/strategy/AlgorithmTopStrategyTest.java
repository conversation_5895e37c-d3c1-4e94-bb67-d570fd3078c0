package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

public class AlgorithmTopStrategyTest {

    private AlgorithmTopStrategy algorithmTopStrategy;
    private FloatTagBuildReq mockReq;
    private FloatTagBuildCfg mockCfg;

    @Before
    public void setUp() {
        algorithmTopStrategy = new AlgorithmTopStrategy();
        mockReq = Mockito.mock(FloatTagBuildReq.class);
        mockCfg = Mockito.mock(FloatTagBuildCfg.class);
    }


    /**
     * 测试标签构造
     */
    @Test
    public void testBuildTagDoubleBigPicShelf() {
        // arrange
        when(mockReq.getShelfShowType()).thenReturn(5);

        // act
        FloatTagVO result = algorithmTopStrategy.buildTag(mockReq, mockCfg);

        // assert
        assertTrue(result != null);
        assertEquals(FloatTagPositionEnums.LEFT_TOP.getPosition(), result.getPosition());
    }

    /**
     * 测试场景：Top1商品，算法圈选，全部tab
     */
    @Test
    public void testIsMatch_Top1AlgorithmRecommendAllTab() {
        HashMap<String, Object> context = new HashMap<>();
        FloatTagBuildReq req = new FloatTagBuildReq();
        FloatTagBuildCfg cfg = new FloatTagBuildCfg();
        ProductM productM = new ProductM();
        context.put(ShelfActivityConstants.Params.pageNo, 1);
        req.setContext(new ActivityCxt());
        req.getContext().setParameters(context);
        req.setIndex(0);
        req.setProductM(productM);
        productM.setAttr("GuessLikeRecommendProduct", "true");
        boolean match = algorithmTopStrategy.isMatch(req, cfg);
        assertTrue(match);
    }

}
