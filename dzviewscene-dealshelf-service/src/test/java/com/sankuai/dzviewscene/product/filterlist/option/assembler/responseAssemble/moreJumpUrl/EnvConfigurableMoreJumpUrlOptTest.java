package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import org.junit.Assert;
//import org.junit.jupiter.api.Test;
import org.testng.annotations.Test;
/**
 * <AUTHOR>
 * @date 2022/12/6
 */
class EnvConfigurableMoreJumpUrlOptTest {

    private static EnvConfigurableMoreJumpUrlOpt jumpUrlOpt = new EnvConfigurableMoreJumpUrlOpt();

//    @Test
    public void test_mt_no_param_cfg() {
        EnvConfigurableMoreJumpUrlOpt.Param param = DealFilterListMoreJumpUrlVP.Param.builder()
                .userAgent(200).entityId("123").platform(2).build();
        EnvConfigurableMoreJumpUrlOpt.Config cfg = new EnvConfigurableMoreJumpUrlOpt.Config();
        cfg.setDpUrl("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html");
        cfg.setMtUrl("https://i.meituan.com/csr/biz-product-list/play-brand-group-list.html");
        Assert.assertTrue(jumpUrlOpt.compute(null, param, cfg).equals("https://i.meituan.com/csr/biz-product-list/play-brand-group-list.html"));
    }

//    @Test
    public void test_dp_one_param_cfg() {
        EnvConfigurableMoreJumpUrlOpt.Param param = DealFilterListMoreJumpUrlVP.Param.builder()
                .userAgent(100).entityId("123").platform(1).build();
        EnvConfigurableMoreJumpUrlOpt.Config cfg = new EnvConfigurableMoreJumpUrlOpt.Config();
        cfg.setDpUrl("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html?platform=%s&entityid=%s");
        cfg.setMtUrl("https://i.meituan.com/csr/biz-product-list/play-brand-group-list.html?platform=%s&entityid=%s");
        cfg.setDynamicValueEnvKeys(Lists.newArrayList("userAgent","entityId"));
        Assert.assertTrue(jumpUrlOpt.compute(null, param, cfg).equals("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html?platform=100&entityid=123"));
    }


//    @Test
    public void test_dp_env_no_param() {
        EnvConfigurableMoreJumpUrlOpt.Param param = DealFilterListMoreJumpUrlVP.Param.builder()
                .userAgent(100).platform(1).build();
        EnvConfigurableMoreJumpUrlOpt.Config cfg = new EnvConfigurableMoreJumpUrlOpt.Config();
        cfg.setDpUrl("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html?entityid=%s");
        cfg.setMtUrl("https://i.meituan.com/csr/biz-product-list/play-brand-group-list.html?entityid=%s");
        cfg.setDynamicValueEnvKeys(Lists.newArrayList("entityId"));
        Assert.assertTrue(jumpUrlOpt.compute(null, param, cfg).equals("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html?entityid="));
    }
}