package com.sankuai.dzviewscene.product.unifiedshelf.enums;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class JumpUrlPatternEnumTest {

    private Map<String, String> invokeCreatePatternMap(String... keyPatternPairs) throws Exception {
        Method method = JumpUrlPatternEnum.class.getDeclaredMethod("createPatternMap", String[].class);
        method.setAccessible(true);
        try {
            return (Map<String, String>) method.invoke(null, (Object) keyPatternPairs);
        } catch (InvocationTargetException e) {
            // Unwrap the actual exception thrown by the method
            throw (Exception) e.getCause();
        }
    }

    /**
     * Test createPatternMap with valid even number of arguments
     */
    @Test
    public void testCreatePatternMapValidInput() throws Throwable {
        // arrange
        String key1 = "key1";
        String value1 = "value1";
        String key2 = "key2";
        String value2 = "value2";
        // act
        Map<String, String> result = invokeCreatePatternMap(key1, value1, key2, value2);
        // assert
        assertEquals(2, result.size());
        assertEquals(value1, result.get(key1));
        assertEquals(value2, result.get(key2));
    }

    /**
     * Test createPatternMap with odd number of arguments should throw IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreatePatternMapInvalidInput() throws Throwable {
        // arrange
        String key = "key";
        // act
        invokeCreatePatternMap(key);
        // The test expects an IllegalArgumentException to be thrown
    }

    /**
     * Test createPatternMap with null key
     */
    @Test
    public void testCreatePatternMapNullKey() throws Throwable {
        // arrange
        String key = null;
        String value = "value";
        // act
        Map<String, String> result = invokeCreatePatternMap(key, value);
        // assert
        assertEquals(1, result.size());
        assertEquals(value, result.get(key));
    }

    /**
     * Test createPatternMap with null value
     */
    @Test
    public void testCreatePatternMapNullValue() throws Throwable {
        // arrange
        String key = "key";
        String value = null;
        // act
        Map<String, String> result = invokeCreatePatternMap(key, value);
        // assert
        assertEquals(1, result.size());
        assertNull(result.get(key));
    }

    /**
     * Test createPatternMap with empty arguments
     */
    @Test
    public void testCreatePatternMapEmptyArguments() throws Throwable {
        // arrange
        // act
        Map<String, String> result = invokeCreatePatternMap();
        // assert
        assertTrue(result.isEmpty());
    }
}