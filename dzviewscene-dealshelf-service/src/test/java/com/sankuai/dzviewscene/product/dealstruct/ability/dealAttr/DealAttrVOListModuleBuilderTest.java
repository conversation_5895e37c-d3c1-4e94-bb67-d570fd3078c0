package com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrVOListModuleBuilderTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assembleParam;

    // Expecting NullPointerException
    @Test(expected = NullPointerException.class)
    public void testBuildModelVONullActivityCxt() throws Throwable {
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        builder.buildModelVO(null, assembleParam, "config");
    }

    @Test
    public void testBuildModelVONullSource() throws Throwable {
        when(activityCxt.getSource(DealAttrVOListModuleBuilder.CODE)).thenReturn(null);
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        assertNull(builder.buildModelVO(activityCxt, assembleParam, "config"));
    }

    @Test
    public void testBuildModelVOEmptySource() throws Throwable {
        when(activityCxt.getSource(DealAttrVOListModuleBuilder.CODE)).thenReturn(Collections.emptyList());
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        assertNull(builder.buildModelVO(activityCxt, assembleParam, "config"));
    }

    @Test
    public void testBuildModelVONullConfig() throws Throwable {
        when(activityCxt.getSource(DealAttrVOListModuleBuilder.CODE)).thenReturn(Collections.singletonList(new DealDetailStructAttrModuleGroupModel()));
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        assertNull(builder.buildModelVO(activityCxt, assembleParam, null));
    }

    @Test
    public void testBuildModelVONoMatchConfig() throws Throwable {
        DealDetailStructAttrModuleGroupModel groupModel = new DealDetailStructAttrModuleGroupModel();
        groupModel.setGroupName("otherConfig");
        when(activityCxt.getSource(DealAttrVOListModuleBuilder.CODE)).thenReturn(Collections.singletonList(groupModel));
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        assertNull(builder.buildModelVO(activityCxt, assembleParam, "config"));
    }

    @Test
    public void testBuildModelVOMatchConfig() throws Throwable {
        DealDetailStructAttrModuleGroupModel groupModel = new DealDetailStructAttrModuleGroupModel();
        groupModel.setGroupName("config");
        // Ensure the group model has a non-empty list of DealDetailStructAttrModuleVOs
        DealDetailStructAttrModuleVO moduleVO = new DealDetailStructAttrModuleVO();
        List<DealDetailStructAttrModuleVO> moduleVOList = Collections.singletonList(moduleVO);
        groupModel.setDealDetailStructAttrModuleVOS(moduleVOList);
        when(activityCxt.getSource(DealAttrVOListModuleBuilder.CODE)).thenReturn(Collections.singletonList(groupModel));
        DealAttrVOListModuleBuilder builder = new DealAttrVOListModuleBuilder();
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // Ensure the result is not null when conditions are met
        assertNotNull(result);
    }
}
