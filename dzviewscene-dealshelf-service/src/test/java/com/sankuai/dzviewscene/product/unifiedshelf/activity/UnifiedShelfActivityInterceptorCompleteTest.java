package com.sankuai.dzviewscene.product.unifiedshelf.activity;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.pmf.monitor.error.ExecuteError;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfModelUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfActivityInterceptorCompleteTest {

    private UnifiedShelfActivityInterceptor interceptor;

    @Mock
    private InterceptorContext<UnifiedShelfResponse> interceptorContext;

    private MockedStatic<Cat> catMock;

    private MockedStatic<ParamsUtil> paramsUtilMock;

    private MockedStatic<PlatformUtil> platformUtilMock;

    private MockedStatic<UnifiedShelfModelUtils> unifiedShelfModelUtilsMock;

    private Map<String, Object> parameters;

    @Mock
    private UnifiedShelfResponse unifiedShelfResponse;

    @Before
    public void setUp() {
        interceptor = new UnifiedShelfActivityInterceptor();
        parameters = new HashMap<>();
        when(interceptorContext.getParameters()).thenReturn(parameters);
        when(interceptorContext.getActivityCode()).thenReturn("testActivity");
        when(interceptorContext.getSceneCode()).thenReturn("testScene");
        catMock = mockStatic(Cat.class);
        paramsUtilMock = mockStatic(ParamsUtil.class);
        platformUtilMock = mockStatic(PlatformUtil.class);
        unifiedShelfModelUtilsMock = mockStatic(UnifiedShelfModelUtils.class);
    }

    @After
    public void tearDown() {
        catMock.close();
        paramsUtilMock.close();
        platformUtilMock.close();
        unifiedShelfModelUtilsMock.close();
    }

    /**
     * Test the complete method when an exception occurs
     */
    @Test
    public void testCompleteWithException() throws Throwable {
        // arrange
        doThrow(new RuntimeException("Test exception")).when(interceptorContext).getDefaultResult();
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logError(any(Throwable.class)), times(1));
    }

    /**
     * Test the complete method with catWithCategory when startTime is valid and shopM is not null
     */
    @Test
    public void testCompleteWithCatWithCategoryValidParams() throws Throwable {
        // arrange
        ShopM shopM = mock(ShopM.class);
        when(shopM.getShopType()).thenReturn(1);
        parameters.put(ShelfActivityConstants.Ctx.ctxShop, shopM);
        paramsUtilMock.when(() -> ParamsUtil.getStringSafely(any(Map.class), eq(ShelfActivityConstants.Params.requestType))).thenReturn(RequestTypeEnum.API_UNIFIED_SHELF.getType());
        paramsUtilMock.when(() -> ParamsUtil.getLongSafely(any(Map.class), eq(ShelfActivityConstants.Params.startTime))).thenReturn(System.currentTimeMillis() - 1000);
        paramsUtilMock.when(() -> ParamsUtil.getIntSafely(any(Map.class), eq(PmfConstants.Params.userAgent))).thenReturn(VCClientTypeEnum.DP_APP.getCode());
        platformUtilMock.when(() -> PlatformUtil.isApp(anyInt())).thenReturn(true);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.newCompletedTransactionWithDuration(eq("UNIFIED_SHELF_FIRST_LOAD_APP"), eq("1"), anyLong()), times(1));
    }

    /**
     * Test the complete method with catWithCategory when startTime is invalid
     */
    @Test
    public void testCompleteWithCatWithCategoryInvalidStartTime() throws Throwable {
        // arrange
        ShopM shopM = mock(ShopM.class);
        parameters.put(ShelfActivityConstants.Ctx.ctxShop, shopM);
        paramsUtilMock.when(() -> ParamsUtil.getLongSafely(any(Map.class), eq(ShelfActivityConstants.Params.startTime))).thenReturn(0L);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.newCompletedTransactionWithDuration(eq("UNIFIED_SHELF_FIRST_LOAD_APP"), anyString(), anyLong()), never());
    }

    /**
     * Test the complete method with catWithCategory when shopM is null
     */
    @Test
    public void testCompleteWithCatWithCategoryNullShopM() throws Throwable {
        // arrange
        parameters.put(ShelfActivityConstants.Ctx.ctxShop, null);
        paramsUtilMock.when(() -> ParamsUtil.getLongSafely(any(Map.class), eq(ShelfActivityConstants.Params.startTime))).thenReturn(System.currentTimeMillis() - 1000);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.newCompletedTransactionWithDuration(eq("UNIFIED_SHELF_FIRST_LOAD_APP"), anyString(), anyLong()), never());
    }

    /**
     * Test the complete method with catWithCategory when not app platform
     */
    @Test
    public void testCompleteWithCatWithCategoryNotAppPlatform() throws Throwable {
        // arrange
        ShopM shopM = mock(ShopM.class);
        parameters.put(ShelfActivityConstants.Ctx.ctxShop, shopM);
        paramsUtilMock.when(() -> ParamsUtil.getStringSafely(any(Map.class), eq(ShelfActivityConstants.Params.requestType))).thenReturn(RequestTypeEnum.API_UNIFIED_SHELF.getType());
        paramsUtilMock.when(() -> ParamsUtil.getLongSafely(any(Map.class), eq(ShelfActivityConstants.Params.startTime))).thenReturn(System.currentTimeMillis() - 1000);
        paramsUtilMock.when(() -> ParamsUtil.getIntSafely(any(Map.class), eq(PmfConstants.Params.userAgent))).thenReturn(VCClientTypeEnum.DP_M.getCode());
        platformUtilMock.when(() -> PlatformUtil.isApp(anyInt())).thenReturn(false);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.newCompletedTransactionWithDuration(eq("UNIFIED_SHELF_FIRST_LOAD_APP"), anyString(), anyLong()), never());
    }

    /**
     * Test the complete method with logMetricForRefreshCount when refreshTag is not blank
     */
    @Test
    public void testCompleteWithLogMetricForRefreshCountValidTag() throws Throwable {
        // arrange
        String refreshTag = "testRefreshTag";
        paramsUtilMock.when(() -> ParamsUtil.getStringSafely(any(Map.class), eq(ShelfActivityConstants.Params.refreshTag))).thenReturn(refreshTag);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logMetricForCount(eq("refresh.testActivity"), anyMap()), times(1));
    }

    /**
     * Test the complete method with logMetricForRefreshCount when refreshTag is blank
     */
    @Test
    public void testCompleteWithLogMetricForRefreshCountBlankTag() throws Throwable {
        // arrange
        paramsUtilMock.when(() -> ParamsUtil.getStringSafely(any(Map.class), eq(ShelfActivityConstants.Params.refreshTag))).thenReturn("");
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logMetricForCount(startsWith("refresh."), anyMap()), never());
    }

    /**
     * Test the complete method with buildMetricTags when defaultResult is not null
     */
    @Test
    public void testCompleteWithBuildMetricTagsDefaultResultNotNull() throws Throwable {
        // arrange
        UnifiedShelfResponse defaultResult = new UnifiedShelfResponse();
        when(interceptorContext.getDefaultResult()).thenReturn(defaultResult);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasFilters(eq(defaultResult))).thenReturn(true);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasProducts(eq(defaultResult))).thenReturn(true);
        paramsUtilMock.when(() -> ParamsUtil.getIntSafely(any(Map.class), eq(PmfConstants.Params.userAgent))).thenReturn(VCClientTypeEnum.DP_APP.getCode());
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logMetricForCount(eq("testActivity"), argThat(map -> map.containsKey("hasFilters") && map.get("hasFilters").equals("true") && map.containsKey("hasProducts") && map.get("hasProducts").equals("true") && map.containsKey("clientType") && map.get("clientType").equals("dpapp"))), times(1));
    }

    /**
     * Test the complete method with buildMetricTags when defaultResult is null
     */
    @Test
    public void testCompleteWithBuildMetricTagsDefaultResultNull() throws Throwable {
        // arrange
        when(interceptorContext.getDefaultResult()).thenReturn(null);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasFilters(eq(unifiedShelfResponse))).thenReturn(false);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasProducts(eq(unifiedShelfResponse))).thenReturn(false);
        paramsUtilMock.when(() -> ParamsUtil.getIntSafely(any(Map.class), eq(PmfConstants.Params.userAgent))).thenReturn(VCClientTypeEnum.MT_APP.getCode());
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logMetricForCount(eq("testActivity"), argThat(map -> map.containsKey("hasFilters") && map.get("hasFilters").equals("false") && map.containsKey("hasProducts") && map.get("hasProducts").equals("false") && map.containsKey("clientType") && map.get("clientType").equals("mtapp"))), times(1));
    }

    /**
     * Test the complete method with buildMetricTags when clientType is invalid
     */
    @Test
    public void testCompleteWithBuildMetricTagsInvalidClientType() throws Throwable {
        // arrange
        when(interceptorContext.getDefaultResult()).thenReturn(null);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasFilters(any())).thenReturn(true);
        unifiedShelfModelUtilsMock.when(() -> UnifiedShelfModelUtils.hasProducts(any())).thenReturn(true);
        paramsUtilMock.when(() -> ParamsUtil.getIntSafely(any(Map.class), eq(PmfConstants.Params.userAgent))).thenReturn(999);
        // act
        interceptor.complete(interceptorContext, unifiedShelfResponse);
        // assert
        catMock.verify(() -> Cat.logMetricForCount(eq("testActivity"), argThat(map -> map.containsKey("hasFilters") && map.get("hasFilters").equals("true") && map.containsKey("hasProducts") && map.get("hasProducts").equals("true") && !map.containsKey("clientType"))), times(1));
    }
}