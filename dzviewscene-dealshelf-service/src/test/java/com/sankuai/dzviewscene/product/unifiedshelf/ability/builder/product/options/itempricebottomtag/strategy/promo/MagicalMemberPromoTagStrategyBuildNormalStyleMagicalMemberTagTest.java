package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CouponPromoItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MagicalMemberPromoTagStrategyBuildNormalStyleMagicalMemberTagTest {

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductPromoPriceM promoPriceM;

    @Mock
    private ActivityCxt context;

    @Mock
    private PromoItemM promoItemM;

    @Mock
    private ProductM productM;

    @InjectMocks
    private MagicalMemberPromoTagStrategy strategy;

    @Before
    public void setUp() {
        when(req.getContext()).thenReturn(context);
        when(context.getParameters()).thenReturn(Collections.singletonMap(ShelfActivityConstants.Params.shelfVersion, "1"));
        // Setup common mock behaviors
        when(req.getProductM()).thenReturn(productM);
        when(req.getSalePrice()).thenReturn("100");
        when(productM.getMarketPrice()).thenReturn("120");
        when(promoPriceM.getProductId()).thenReturn(123L);
        when(promoPriceM.getPromoItemList()).thenReturn(Collections.singletonList(promoItemM));
        when(promoPriceM.getTotalPromoPrice()).thenReturn(new BigDecimal("20"));
        // Setup default promotion info map
        Map<String, String> promotionInfo = new HashMap<>();
        promotionInfo.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        promotionInfo.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "false");
        when(promoItemM.getPromotionOtherInfoMap()).thenReturn(promotionInfo);
    }

    private ShelfTagVO invokePrivateMethod(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) throws Exception {
        Method method = MagicalMemberPromoTagStrategy.class.getDeclaredMethod("buildNormalStyleMagicalMemberTag", PriceBottomTagBuildReq.class, ProductPromoPriceM.class);
        method.setAccessible(true);
        return (ShelfTagVO) method.invoke(strategy, req, promoPriceM);
    }

    /**
     * Test normal case for buildNormalStyleMagicalMemberTag method
     */
    @Test
    public void testBuildNormalStyleMagicalMemberTagNormalCase() throws Throwable {
        try (MockedStatic<DzPromoUtils> mockedDzPromoUtils = mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> mockedMagicalMemberTagUtils = mockStatic(MagicalMemberTagUtils.class)) {
            // Arrange
            mockedDzPromoUtils.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(promoItemM);
            mockedDzPromoUtils.when(() -> DzPromoUtils.getExtendDisplayInfo(promoPriceM)).thenReturn(new HashMap<>());
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCantInflateText(promoItemM)).thenReturn("省10元");
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCanInflateAndCanInflateMore(any(), any())).thenReturn(false);
            // DP platform
            when(req.getPlatform()).thenReturn(1);
            when(req.isHitSimplifyPromoTag()).thenReturn(false);
            // Act
            ShelfTagVO result = invokePrivateMethod(req, promoPriceM);
            // Assert
            assertNotNull(result);
            assertEquals(UnifiedShelfPromoUtils.MAGICAL_MEMBER_TAG_NAME, result.getName());
            assertNotNull(result.getLabs());
        }
    }

    /**
     * Test case when hitSimplifyPromoTag is true
     */
    @Test
    public void testBuildNormalStyleMagicalMemberTagHitSimplifyPromoTag() throws Throwable {
        try (MockedStatic<DzPromoUtils> mockedDzPromoUtils = mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> mockedMagicalMemberTagUtils = mockStatic(MagicalMemberTagUtils.class);
            MockedStatic<UnifiedShelfPromoUtils> mockedUnifiedShelfPromoUtils = mockStatic(UnifiedShelfPromoUtils.class)) {
            // Arrange
            CouponPromoItemVO couponPromoItemVO = new CouponPromoItemVO();
            couponPromoItemVO.setCouponTag("神会员专享");
            mockedDzPromoUtils.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(promoItemM);
            mockedDzPromoUtils.when(() -> DzPromoUtils.getExtendDisplayInfo(promoPriceM)).thenReturn(new HashMap<>());
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCantInflateText(promoItemM)).thenReturn("省10元");
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCanInflateAndCanInflateMore(any(), any())).thenReturn(false);
            mockedUnifiedShelfPromoUtils.when(() -> UnifiedShelfPromoUtils.buildMagicMemberCoupon(anyInt(), anyInt(), any(PromoItemM.class), anyLong())).thenReturn(couponPromoItemVO);
            // DP platform
            when(req.getPlatform()).thenReturn(1);
            when(req.isHitSimplifyPromoTag()).thenReturn(true);
            // Act
            ShelfTagVO result = invokePrivateMethod(req, promoPriceM);
            // Assert
            assertNotNull(result);
            assertEquals(UnifiedShelfPromoUtils.MAGICAL_MEMBER_TAG_NAME, result.getName());
            assertNotNull(result.getCouponPromoItemVO());
            assertEquals("神会员专享", result.getCouponPromoItemVO().getCouponTag());
        }
    }

    /**
     * Test case for MT platform
     */
    @Test
    public void testBuildNormalStyleMagicalMemberTagMTPlatform() throws Throwable {
        try (MockedStatic<DzPromoUtils> mockedDzPromoUtils = mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> mockedMagicalMemberTagUtils = mockStatic(MagicalMemberTagUtils.class)) {
            // Arrange
            mockedDzPromoUtils.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(promoItemM);
            mockedDzPromoUtils.when(() -> DzPromoUtils.getExtendDisplayInfo(promoPriceM)).thenReturn(new HashMap<>());
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCantInflateText(promoItemM)).thenReturn("省10元");
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCanInflateAndCanInflateMore(any(), any())).thenReturn(false);
            // MT platform
            when(req.getPlatform()).thenReturn(2);
            when(req.isHitSimplifyPromoTag()).thenReturn(false);
            // Act
            ShelfTagVO result = invokePrivateMethod(req, promoPriceM);
            // Assert
            assertNotNull(result);
            assertEquals(UnifiedShelfPromoUtils.MAGICAL_MEMBER_TAG_NAME, result.getName());
            assertNotNull(result.getLabs());
        }
    }

    /**
     * Test case when should show inflate pic
     */
    @Test
    public void testBuildNormalStyleMagicalMemberTagWithInflatePic() throws Throwable {
        try (MockedStatic<DzPromoUtils> mockedDzPromoUtils = mockStatic(DzPromoUtils.class);
            MockedStatic<MagicalMemberTagUtils> mockedMagicalMemberTagUtils = mockStatic(MagicalMemberTagUtils.class)) {
            // Arrange
            mockedDzPromoUtils.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(promoItemM);
            mockedDzPromoUtils.when(() -> DzPromoUtils.getExtendDisplayInfo(promoPriceM)).thenReturn(new HashMap<>());
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCantInflateText(promoItemM)).thenReturn("省10元");
            mockedMagicalMemberTagUtils.when(() -> MagicalMemberTagUtils.getCanInflateAndCanInflateMore(any(), any())).thenReturn(true);
            // DP platform
            when(req.getPlatform()).thenReturn(1);
            when(req.isHitSimplifyPromoTag()).thenReturn(false);
            // Setup inflate conditions
            Map<String, String> promotionInfo = new HashMap<>();
            promotionInfo.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
            promotionInfo.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
            when(promoItemM.getPromotionOtherInfoMap()).thenReturn(promotionInfo);
            // Act
            ShelfTagVO result = invokePrivateMethod(req, promoPriceM);
            // Assert
            assertNotNull(result);
            assertEquals(UnifiedShelfPromoUtils.MAGICAL_MEMBER_TAG_NAME, result.getName());
            assertNotNull(result.getPrePic());
        }
    }
}