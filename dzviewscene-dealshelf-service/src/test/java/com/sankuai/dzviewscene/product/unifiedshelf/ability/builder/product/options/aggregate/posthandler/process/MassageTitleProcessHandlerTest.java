package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MassageTitleProcessHandlerTest {

    @InjectMocks
    private MassageTitleProcessHandler massageTitleProcessHandler;

    @Mock
    private ActivityCxt activityCxt;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private Map<String, ProductM> productMMap;
    private ShelfItemVO shelfItemVO;

    @Before
    public void setUp() {
        // 初始化参数
        productMMap = Maps.newHashMap();
        shelfItemVO = new ShelfItemVO();
    }

    /**
     * 测试处理SPU节点的标题替换
     */
    @Test
    public void testProcessForSpuNode() {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setTitle("足疗按摩｜60分钟足疗");
        productMMap.put("1_2", productM);
        ShelfItemVO shelfItemVO1 = new ShelfItemVO();
        shelfItemVO1.setItemId(2);
        shelfItemVO1.setItemType(ProductTypeEnum.DEAL.getType());
        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(shelfItemVO1));

        AggregateHandlerContext context = AggregateHandlerContext.builder().productMMap(productMMap).shelfItemVO(shelfItemVO).build();
        // act
        ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

        // assert
        assertNotNull(result);
        assertEquals("足疗按摩", result.getSubItems().get(0).getTitle().get(0).getText());
    }

    /**
     * 测试处理多个子项的情况
     */
    @Test
    public void testProcessForMultipleSubItems() {
        // arrange
        ProductM productM1 = new ProductM();
        productM1.setProductId(2);
        productM1.setProductType(ProductTypeEnum.DEAL.getType());
        productM1.setTitle("足疗按摩1｜60分钟足疗");
        productMMap.put("1_2", productM1);

        ProductM productM2 = new ProductM();
        productM2.setProductId(3);
        productM2.setProductType(ProductTypeEnum.DEAL.getType());
        productM2.setTitle("足疗按摩2｜90分钟足疗");
        productMMap.put("1_3", productM2);

        ShelfItemVO shelfItemVO1 = new ShelfItemVO();
        shelfItemVO1.setItemId(2);
        shelfItemVO1.setItemType(ProductTypeEnum.DEAL.getType());

        ShelfItemVO shelfItemVO2 = new ShelfItemVO();
        shelfItemVO2.setItemId(3);
        shelfItemVO2.setItemType(ProductTypeEnum.DEAL.getType());

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(shelfItemVO1, shelfItemVO2));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();

        // act
        ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

        // assert
        assertNotNull(result);
        assertEquals("足疗按摩1", result.getSubItems().get(0).getTitle().get(0).getText());
        assertEquals("足疗按摩2", result.getSubItems().get(1).getTitle().get(0).getText());
    }

    /**
     * 测试命中黑名单商户的情况
     */
    @Test
    public void testHitBlackShop() {
        // arrange
        // 保存原始黑名单配置
        List<Long> originalBlackShopIds = MassageTitleProcessHandler.BLACK_SHOP_IDS;

        try {
            // 设置测试黑名单
            MassageTitleProcessHandler.BLACK_SHOP_IDS = Lists.newArrayList(123L);

            ProductM productM = new ProductM();
            productM.setProductId(2);
            productM.setProductType(ProductTypeEnum.DEAL.getType());
            productM.setTitle("足疗按摩｜60分钟足疗");
            productMMap.put("1_2", productM);

            ShelfItemVO shelfItemVO1 = new ShelfItemVO();
            shelfItemVO1.setItemId(2);
            shelfItemVO1.setItemType(ProductTypeEnum.DEAL.getType());
            shelfItemVO1.setTitle(Lists.newArrayList()); // 设置一个空标题列表

            shelfItemVO.setItemId(1);
            shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
            shelfItemVO.setSubItems(Lists.newArrayList(shelfItemVO1));

            // 模拟商户ID为黑名单中的ID
            when(ParamsUtil.getLongSafely(activityCxt, ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(123L);

            AggregateHandlerContext context = AggregateHandlerContext.builder()
                    .productMMap(productMMap)
                    .shelfItemVO(shelfItemVO)
                    .build();

            // act
            ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

            // assert
            assertNotNull(result);
            // 由于命中黑名单，标题不会被修改，应该是空列表
            assertEquals(0, result.getSubItems().get(0).getTitle().size());
        } finally {
            // 恢复原始黑名单配置
            MassageTitleProcessHandler.BLACK_SHOP_IDS = originalBlackShopIds;
        }
    }

    /**
     * 测试非SPU节点的情况
     */
    @Test
    public void testNonSpuNode() {
        // arrange
        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.DEAL.getType()); // 设置为非SPU节点

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();

        // act
        ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

        // assert
        assertNotNull(result);
        // 非SPU节点应该直接返回原始对象，不做处理
        assertEquals(shelfItemVO, result);
    }

    /**
     * 测试标题格式不匹配的情况
     */
    @Test
    public void testTitleFormatNotMatch() {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setTitle("不匹配格式的标题"); // 不符合正则表达式的标题
        productMMap.put("1_2", productM);

        ShelfItemVO shelfItemVO1 = new ShelfItemVO();
        shelfItemVO1.setItemId(2);
        shelfItemVO1.setItemType(ProductTypeEnum.DEAL.getType());

        StyleTextModel originalTitle = new StyleTextModel();
        originalTitle.setText("原始标题");
        shelfItemVO1.setTitle(Lists.newArrayList(originalTitle));

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(shelfItemVO1));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();

        // act
        ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

        // assert
        assertNotNull(result);
        // 由于标题格式不匹配，应该保留原始标题
        assertEquals("原始标题", result.getSubItems().get(0).getTitle().get(0).getText());
    }

    /**
     * 测试标题为空的情况
     */
    @Test
    public void testEmptyTitle() {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.DEAL.getType());
        productM.setTitle(""); // 空标题
        productMMap.put("1_2", productM);

        ShelfItemVO shelfItemVO1 = new ShelfItemVO();
        shelfItemVO1.setItemId(2);
        shelfItemVO1.setItemType(ProductTypeEnum.DEAL.getType());

        StyleTextModel originalTitle = new StyleTextModel();
        originalTitle.setText("原始标题");
        shelfItemVO1.setTitle(Lists.newArrayList(originalTitle));

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(shelfItemVO1));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();

        // act
        ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

        // assert
        assertNotNull(result);
        // 由于标题为空，应该保留原始标题
        assertEquals("原始标题", result.getSubItems().get(0).getTitle().get(0).getText());
    }

    /**
     * 测试命中斗槲实验的情况
     */
    @Test
    public void testHitDouhuExperiment() throws Exception {
        // 准备测试数据
        // 1. 设置斗槲实验白名单
        massageTitleProcessHandler.douhuExpWhitelist = "EXP2025050800008_c,EXP2025050800009_c";

        // 2. 模拟斗槲实验数据
        DouHuM douHuM = new DouHuM();
        douHuM.setExpId("EXP2025050800008_c");
        List<DouHuM> douHuMList = Lists.newArrayList(douHuM);

        // 3. 模拟 ActivityCxt 返回斗槲实验数据
        Mockito.when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);

        // 4. 准备商品数据
        ProductM productM = new ProductM();
        productM.setTitle("豪华按摩套餐｜60分钟足疗");
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性
        String productNameGroupJson = "{\"productName_value\":\"自定义标题\"}";
        productM.setAttr("productName_group", productNameGroupJson);

        productMMap.put("1_2", productM);

        // 5. 准备 ShelfItemVO 数据
        ShelfItemVO childItem = new ShelfItemVO();
        childItem.setItemId(2);
        childItem.setItemType(ProductTypeEnum.DEAL.getType());

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(childItem));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();
        try(MockedStatic<DouHuUtils> mockedStatic = Mockito.mockStatic(DouHuUtils.class)){
            mockedStatic.when(() -> DouHuUtils.hitAnySk(Mockito.anyList(), Mockito.anyList())).thenReturn(true);
            // 6. 模拟 DouHuUtils.hitAnySk 方法

            // 执行测试
            ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getSubItems());
            assertEquals(1, result.getSubItems().size());
            assertNotNull(result.getSubItems().get(0).getTitle());
            System.out.println(result.getSubItems().get(0).getTitle().get(0).getText());
            assertEquals("豪华按摩套餐", result.getSubItems().get(0).getTitle().get(0).getText());
        }
    }

    /**
     * 测试单deal SPU处理旧标准化拼接字段后缀团单标题
     */
    @Test
    public void testSingleDealWithOldStandardSuffixTitle() throws Exception {
        // 准备测试数据
        // 1. 设置斗槲实验白名单
        massageTitleProcessHandler.douhuExpWhitelist = "EXP2025050800008_c";
        // 2. 模拟斗槲实验数据
        DouHuM douHuM = new DouHuM();
        douHuM.setExpId("EXP2025050800008_c");
        List<DouHuM> douHuMList = Lists.newArrayList(douHuM);

        // 3. 模拟 ActivityCxt 返回斗槲实验数据
        Mockito.when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);

        // 4. 准备商品数据
        ProductM productM = new ProductM();
        productM.setTitle("按摩足疗");
        productM.setProductId(2);
        productM.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性 - 旧标准化拼接字段后缀团单
        String productNameGroupJson = "{\"productName_value\":\"按摩\",\"productName_suffix\":\"足疗\"}";
        productM.setAttr("productName_group", productNameGroupJson);

        productMMap.put("1_2", productM);

        // 5. 准备 ShelfItemVO 数据
        ShelfItemVO childItem = new ShelfItemVO();
        childItem.setItemId(2);
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText("按摩足疗");
        childItem.setTitle(Lists.newArrayList(styleTextModel));
        childItem.setItemType(ProductTypeEnum.DEAL.getType());

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(childItem));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();
        try(MockedStatic<DouHuUtils> mockedStatic = Mockito.mockStatic(DouHuUtils.class)){
            mockedStatic.when(() -> DouHuUtils.hitAnySk(Mockito.anyList(), Mockito.anyList())).thenReturn(true);
            // 6. 模拟 DouHuUtils.hitAnySk 方法

            // 执行测试
            ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

            // 验证结果 - 应该展示完整团单标题（含拼接+自定义部分）
            assertNotNull(result);
            assertNotNull(result.getSubItems());
            assertEquals(1, result.getSubItems().size());
            assertNotNull(result.getSubItems().get(0).getTitle());
            assertEquals("按摩足疗", result.getSubItems().get(0).getTitle().get(0).getText());
        }
    }

    /**
     * 测试多deal SPU处理新标准化拼接字段前缀团单标题，且前缀与SPU标题重叠
     */
    @Test
    public void testMultiDealWithNewStandardPrefixTitleOverlap() throws Exception {
        // 准备测试数据
        // 1. 设置斗槲实验白名单
        massageTitleProcessHandler.douhuExpWhitelist = "EXP2025050800008_c";
        // 2. 模拟斗槲实验数据
        DouHuM douHuM = new DouHuM();
        douHuM.setExpId("EXP2025050800008_c");
        List<DouHuM> douHuMList = Lists.newArrayList(douHuM);

        // 3. 模拟 ActivityCxt 返回斗槲实验数据
        Mockito.when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);

        // 4. 准备商品数据
        ProductM productM1 = new ProductM();
        productM1.setProductId(2);
        productM1.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性 - 新标准化拼接字段前缀团单
        String productNameGroupJson1 = "{\"productName_value\":\"按摩\",\"productName_prefix\":\"足疗会所\"}";
        productM1.setAttr("productName_group", productNameGroupJson1);

        ProductM productM2 = new ProductM();
        productM2.setProductId(3);
        productM2.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性 - 新标准化拼接字段前缀团单
        String productNameGroupJson2 = "{\"productName_value\":\"推拿\",\"productName_prefix\":\"足疗会所\"}";
        productM2.setAttr("productName_group", productNameGroupJson2);

        productMMap.put("1_2", productM1);
        productMMap.put("1_3", productM2);

        // 5. 准备 ShelfItemVO 数据
        ShelfItemVO childItem1 = new ShelfItemVO();
        childItem1.setItemId(2);
        childItem1.setItemType(ProductTypeEnum.DEAL.getType());

        ShelfItemVO childItem2 = new ShelfItemVO();
        childItem2.setItemId(3);
        childItem2.setItemType(ProductTypeEnum.DEAL.getType());

        // 设置 SPU 标题
        StyleTextModel spuTitle = new StyleTextModel();
        spuTitle.setText("足疗会所");

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(childItem1, childItem2));
        shelfItemVO.setTitle(Lists.newArrayList(spuTitle));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();
        try(MockedStatic<DouHuUtils> mockedStatic = Mockito.mockStatic(DouHuUtils.class)){
            mockedStatic.when(() -> DouHuUtils.hitAnySk(Mockito.anyList(), Mockito.anyList())).thenReturn(true);
            // 6. 模拟 DouHuUtils.hitAnySk 方法

            // 执行测试
            ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

            // 验证结果 - 应该只展示自定义部分，不展示与SPU标题重叠的前缀
            assertNotNull(result);
            assertNotNull(result.getSubItems());
            assertEquals(2, result.getSubItems().size());
            assertNotNull(result.getSubItems().get(0).getTitle());
            assertNotNull(result.getSubItems().get(1).getTitle());
            assertEquals("按摩", result.getSubItems().get(0).getTitle().get(0).getText());
            assertEquals("推拿", result.getSubItems().get(1).getTitle().get(0).getText());
        }
    }

    /**
     * 测试多deal SPU处理旧标准化拼接字段后缀团单标题
     */
    @Test
    public void testMultiDealWithOldStandardSuffixTitle() throws Exception {
        // 准备测试数据
        // 1. 设置斗槲实验白名单
        massageTitleProcessHandler.douhuExpWhitelist = "EXP2025050800008_c";
        // 2. 模拟斗槲实验数据
        DouHuM douHuM = new DouHuM();
        douHuM.setExpId("EXP2025050800008_c");
        List<DouHuM> douHuMList = Lists.newArrayList(douHuM);

        // 3. 模拟 ActivityCxt 返回斗槲实验数据
        Mockito.when(activityCxt.getParam(ShelfActivityConstants.Params.douHus)).thenReturn(douHuMList);

        // 4. 准备商品数据
        ProductM productM1 = new ProductM();
        productM1.setProductId(2);
        productM1.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性 - 旧标准化拼接字段后缀团单
        String productNameGroupJson1 = "{\"productName_value\":\"按摩\",\"productName_suffix\":\"足疗\"}";
        productM1.setAttr("productName_group", productNameGroupJson1);

        ProductM productM2 = new ProductM();
        productM2.setProductId(3);
        productM2.setProductType(ProductTypeEnum.DEAL.getType());

        // 设置 sys_productName_group 属性 - 旧标准化拼接字段后缀团单
        String productNameGroupJson2 = "{\"productName_value\":\"推拿\",\"productName_suffix\":\"足疗\"}";
        productM2.setAttr("productName_group", productNameGroupJson2);

        productMMap.put("1_2", productM1);
        productMMap.put("1_3", productM2);

        // 5. 准备 ShelfItemVO 数据
        ShelfItemVO childItem1 = new ShelfItemVO();
        childItem1.setItemId(2);
        childItem1.setItemType(ProductTypeEnum.DEAL.getType());

        ShelfItemVO childItem2 = new ShelfItemVO();
        childItem2.setItemId(3);
        childItem2.setItemType(ProductTypeEnum.DEAL.getType());

        // 设置 SPU 标题
        StyleTextModel spuTitle = new StyleTextModel();
        spuTitle.setText("足疗会所");

        shelfItemVO.setItemId(1);
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setSubItems(Lists.newArrayList(childItem1, childItem2));
        shelfItemVO.setTitle(Lists.newArrayList(spuTitle));

        AggregateHandlerContext context = AggregateHandlerContext.builder()
                .productMMap(productMMap)
                .shelfItemVO(shelfItemVO)
                .build();

        // 6. 模拟 DouHuUtils.hitAnySk 方法
        try(MockedStatic<DouHuUtils> mockedStatic = Mockito.mockStatic(DouHuUtils.class)){

            mockedStatic.when(() -> DouHuUtils.hitAnySk(Mockito.anyList(), Mockito.anyList())).thenReturn(true);
            // 执行测试
            ShelfItemVO result = massageTitleProcessHandler.process(activityCxt, context);

            // 验证结果 - 应该只展示自定义部分，不展示拼接字段
            assertNotNull(result);
            assertNotNull(result.getSubItems());
            assertEquals(2, result.getSubItems().size());
            assertNotNull(result.getSubItems().get(0).getTitle());
            assertNotNull(result.getSubItems().get(1).getTitle());
            assertEquals("按摩", result.getSubItems().get(0).getTitle().get(0).getText());
            assertEquals("推拿", result.getSubItems().get(1).getTitle().get(0).getText());
        }
    }
}