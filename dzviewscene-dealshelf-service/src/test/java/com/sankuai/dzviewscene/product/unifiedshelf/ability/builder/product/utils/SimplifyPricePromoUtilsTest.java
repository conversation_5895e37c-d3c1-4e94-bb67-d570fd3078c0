package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import com.dianping.frog.sdk.util.CollectionUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfActivityStep;
import com.sankuai.athena.viewscene.framework.pmf.resource.cfg.AbilityNodeCfg;
import com.sankuai.athena.viewscene.framework.pmf.resource.cfg.ResourceCfg;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SimplifyPricePromoUtilsTest {

    private List<String> originalSimplifyExpSks;

    private Boolean originalSimplifyPricePromoSwitch;

    private List<String> originalUncoveredShelfTypes;

    private List<String> originalRetainedStrategies;

    /**
     * Test case when retainedStrategies is null
     */
    @Test
    public void testIsRetainedStrategy_NullRetainedStrategies() throws Exception {
        // arrange
        setRetainedStrategies(null);
        // act
        boolean result = SimplifyPricePromoUtils.isRetainedStrategy("someStrategy");
        // assert
        assertFalse(result);
        // reset to pre value
        setRetainedStrategies(null);
    }

    /**
     * Test case when retainedStrategies is empty
     */
    @Test
    public void testIsRetainedStrategy_EmptyRetainedStrategies() throws Exception {
        // arrange
        setRetainedStrategies(Collections.emptyList());
        // act
        boolean result = SimplifyPricePromoUtils.isRetainedStrategy("someStrategy");
        // assert
        assertFalse(result);
        // reset to pre value
        setRetainedStrategies(null);
    }

    /**
     * Test case when retainedStrategies is not empty but doesn't contain the strategy
     */
    @Test
    public void testIsRetainedStrategy_StrategyNotInList() throws Exception {
        // arrange
        setRetainedStrategies(Arrays.asList("strategy1", "strategy2"));
        // act
        boolean result = SimplifyPricePromoUtils.isRetainedStrategy("someOtherStrategy");
        // assert
        assertFalse(result);
        // reset to pre value
        setRetainedStrategies(null);
    }

    /**
     * Test case when retainedStrategies is not empty and contains the strategy
     */
    @Test
    public void testIsRetainedStrategy_StrategyInList() throws Exception {
        // arrange
        List<String> strategies = Arrays.asList("strategy1", "targetStrategy", "strategy3");
        setRetainedStrategies(strategies);
        // act
        boolean result = SimplifyPricePromoUtils.isRetainedStrategy("targetStrategy");
        // assert
        assertTrue(result);
        // reset to pre value
        setRetainedStrategies(null);
    }

    /**
     * Test case when strategy parameter is null
     */
    @Test
    public void testIsRetainedStrategy_NullStrategy() throws Exception {
        // arrange
        setRetainedStrategies(Arrays.asList("strategy1", "strategy2"));
        // act
        boolean result = SimplifyPricePromoUtils.isRetainedStrategy(null);
        // assert
        assertFalse(result);
        // reset to pre value
        setRetainedStrategies(null);
    }

    /**
     * Test case using MockedStatic to mock CollectionUtils.isNotEmpty
     */
    @Test
    public void testIsRetainedStrategy_WithMockedCollectionUtils() {
        // arrange
        try (MockedStatic<CollectionUtils> mockedCollectionUtils = Mockito.mockStatic(CollectionUtils.class)) {
            List<String> strategies = new ArrayList<>();
            strategies.add("strategy1");
            setRetainedStrategies(strategies);
            // Mock CollectionUtils.isNotEmpty to return false even though the list is not empty
            mockedCollectionUtils.when(() -> CollectionUtils.isNotEmpty(strategies)).thenReturn(false);
            // act
            boolean result = SimplifyPricePromoUtils.isRetainedStrategy("strategy1");
            // assert
            assertFalse(result);
        } catch (Exception e) {
            // Handle exception
        } finally {
            // reset to pre value
            try {
                setRetainedStrategies(null);
            } catch (Exception e) {
                // Handle exception
            }
        }
    }

    /**
     * Helper method to set retainedStrategies field using reflection
     */
    private void setRetainedStrategies(List<String> strategies) throws Exception {
        Field field = SimplifyPricePromoUtils.class.getDeclaredField("retainedStrategies");
        field.setAccessible(true);
        field.set(null, strategies);
    }

    @Before
    public void setUp() throws Exception {
        // Save original values
        originalSimplifyExpSks = getFieldValue("simplifyExpSks");
        originalSimplifyPricePromoSwitch = getFieldValue("simplifyPricePromoSwitch");
        originalUncoveredShelfTypes = getFieldValue("uncoveredShelfTypes");
        originalRetainedStrategies = getFieldValue("retainedStrategies");
        // Set default values that would make hitSimplifyPromoTagConfig return true
        setFieldValue("simplifyPricePromoSwitch", true);
        // Default to not include our test shelf type
        setFieldValue("uncoveredShelfTypes", Arrays.asList("otherShelfType"));
        setFieldValue("retainedStrategies", new ArrayList<>());
    }

    @After
    public void tearDown() throws Exception {
        // Restore original values
        setFieldValue("simplifyExpSks", originalSimplifyExpSks);
        setFieldValue("simplifyPricePromoSwitch", originalSimplifyPricePromoSwitch);
        setFieldValue("uncoveredShelfTypes", originalUncoveredShelfTypes);
        setFieldValue("retainedStrategies", originalRetainedStrategies);
    }

    private ActivityCxt createValidActivityContext() {
        ActivityCxt context = new ActivityCxt();
        // Set up activity config step with a strategy that's not in retainedStrategies
        PmfActivityStep activityConfigStep = new PmfActivityStep();
        AbilityNodeCfg abilityNode = new AbilityNodeCfg();
        abilityNode.setStrategy("testStrategy");
        activityConfigStep.setAbilityNode(abilityNode);
        context.setActivityConfigStep(activityConfigStep);
        // Set resource with shelf type that's not in uncoveredShelfTypes
        ResourceCfg resource = new ResourceCfg();
        resource.setId("testResource");
        context.setResource(resource);
        return context;
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenSimplifyExpSksNull() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", null);
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenSimplifyExpSksEmpty() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", new ArrayList<>());
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenDouHuMListNull() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", Arrays.asList("sk1", "sk2"));
        context.addParam(ShelfActivityConstants.Params.douHus, null);
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenDouHuMListEmpty() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", Arrays.asList("sk1", "sk2"));
        context.addParam(ShelfActivityConstants.Params.douHus, new ArrayList<>());
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenDouHuMListHasMatchingSk() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", Arrays.asList("sk1", "sk2"));
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk1");
        douHuMList.add(douHuM);
        context.addParam(ShelfActivityConstants.Params.douHus, douHuMList);
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitSimplifyPromoTagConfig_WhenDouHuMListHasNoMatchingSk() throws Throwable {
        // arrange
        ActivityCxt context = createValidActivityContext();
        setFieldValue("simplifyExpSks", Arrays.asList("sk1", "sk2"));
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk3");
        douHuMList.add(douHuM);
        context.addParam(ShelfActivityConstants.Params.douHus, douHuMList);
        // act
        boolean result = SimplifyPricePromoUtils.hitSimplifyPromoTagConfig(context);
        // assert
        assertFalse(result);
    }

    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(String fieldName) throws Exception {
        Field field = SimplifyPricePromoUtils.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        return (T) field.get(null);
    }

    private void setFieldValue(String fieldName, Object value) throws Exception {
        Field field = SimplifyPricePromoUtils.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }
}