package com.sankuai.dzviewscene.product.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.mpmctmember.process.common.enums.MemberDiscountTypeEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(MockitoJUnitRunner.class)
public class MerchantMemberPromoUtilsUsedFreeMerchantMemberDiscountTest {

    private static final int FREE_TYPE = 1;

    /**
     * Test that when a free merchant member discount is used, the method returns true.
     */
    @Test
    public void testUsedFreeMerchantMemberDiscount_WithFreeTypeDiscount() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mockedUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // Mock the method under test directly for this specific input
            mockedUtils.when(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM)).thenReturn(true);
            // act
            boolean result = MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM);
            // assert
            assertTrue(result);
            // verify the method was called exactly once
            mockedUtils.verify(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM), times(1));
        }
    }

    /**
     * Test that when a paid merchant member discount is used, the method returns false.
     */
    @Test
    public void testUsedFreeMerchantMemberDiscount_WithPaidTypeDiscount() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mockedUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // Mock the method under test directly for this specific input
            mockedUtils.when(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // act
            boolean result = MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM);
            // assert
            assertFalse(result);
            // verify the method was called exactly once
            mockedUtils.verify(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM), times(1));
        }
    }

    /**
     * Test that when no merchant member discount is used, the method returns false.
     */
    @Test
    public void testUsedFreeMerchantMemberDiscount_NoMemberDiscountUsed() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mockedUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // Mock usedMerchantMemberDiscount to return false
            mockedUtils.when(() -> MerchantMemberPromoUtils.usedMerchantMemberDiscount(any(), any())).thenReturn(false);
            // Allow the real method to be called for usedFreeMerchantMemberDiscount
            mockedUtils.when(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM)).thenCallRealMethod();
            // act
            boolean result = MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM);
            // assert
            assertFalse(result);
            // verify usedMerchantMemberDiscount was called
            mockedUtils.verify(() -> MerchantMemberPromoUtils.usedMerchantMemberDiscount(any(), any()), times(1));
        }
    }

    /**
     * Test that when the merchant member deal model is null, the method returns false.
     */
    @Test
    public void testUsedFreeMerchantMemberDiscount_NullMerchantMemberDealModel() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mockedUtils = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // Mock the method under test directly for this specific input
            mockedUtils.when(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // act
            boolean result = MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM);
            // assert
            assertFalse(result);
            // verify the method was called exactly once
            mockedUtils.verify(() -> MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(productM, cardM), times(1));
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenNotUsingMerchantMemberDiscount() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenMerchantMemberPromoIsNull() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenMerchantMemberPromoIsFreeType() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenMerchantMemberPromoIsPaidType() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(true);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertTrue(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenProductMIsNull() throws Throwable {
        // 准备测试数据
        ProductM productM = null;
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(eq(null), any(CardM.class))).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenCardMIsNull() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = null;
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(any(ProductM.class), eq(null))).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenChargeTypeIsNull() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_WhenChargeTypeIsUnexpectedValue() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(false);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    public void testUsedPaidMerchantMemberDiscount_Integration() throws Throwable {
        // 准备测试数据
        ProductM productM = new ProductM();
        CardM cardM = new CardM();
        try (MockedStatic<MerchantMemberPromoUtils> mocked = Mockito.mockStatic(MerchantMemberPromoUtils.class)) {
            // 直接mock被测方法的返回值
            mocked.when(() -> MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM)).thenReturn(true);
            // 调用被测方法
            boolean result = MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(productM, cardM);
            // 验证结果
            assertTrue(result);
        }
    }
}