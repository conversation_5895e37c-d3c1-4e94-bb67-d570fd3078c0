package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.MassageServiceTypeEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_HitNewIconTest {

    private AbstractFootMessageModuleOpt target;

    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = new AbstractFootMessageModuleOpt() {

        @Override
        public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, Object o, Object o2) {
            // Dummy implementation to satisfy the PmfVPoint interface
            return null;
        }
    };

    @Before
    public void setUp() {
        target = Mockito.mock(AbstractFootMessageModuleOpt.class, Mockito.CALLS_REAL_METHODS);
    }

    /**
     * Test hitNewIcon method when both douhuResultModels and expIds are empty
     */
    @Test
    public void testHitNewIconBothListsEmpty() throws Throwable {
        // arrange
        List<DouhuResultModel> douhuResultModels = Collections.emptyList();
        List<String> expIds = Collections.emptyList();
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertFalse("Expected false when both input lists are empty", result);
    }

    /**
     * Test hitNewIcon method when douhuResultModels is empty
     */
    @Test
    public void testHitNewIconDouhuResultModelsEmpty() throws Throwable {
        // arrange
        List<DouhuResultModel> douhuResultModels = Collections.emptyList();
        List<String> expIds = Arrays.asList("exp1", "exp2");
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertFalse("Expected false when douhuResultModels is empty", result);
    }

    /**
     * Test hitNewIcon method when expIds is empty
     */
    @Test
    public void testHitNewIconExpIdsEmpty() throws Throwable {
        // arrange
        DouhuResultModel model = new DouhuResultModel();
        model.setExpId("exp1");
        model.setHitSk(true);
        List<DouhuResultModel> douhuResultModels = Collections.singletonList(model);
        List<String> expIds = Collections.emptyList();
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertFalse("Expected false when expIds is empty", result);
    }

    /**
     * Test hitNewIcon method when expId matches and isHitSk is true
     */
    @Test
    public void testHitNewIconExpIdMatchesAndIsHitSkTrue() throws Throwable {
        // arrange
        DouhuResultModel model = new DouhuResultModel();
        model.setExpId("exp1");
        model.setHitSk(true);
        List<DouhuResultModel> douhuResultModels = Collections.singletonList(model);
        List<String> expIds = Arrays.asList("exp1", "exp2");
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertTrue("Expected true when expId matches and isHitSk is true", result);
    }

    /**
     * Test hitNewIcon method when expId matches but isHitSk is false
     */
    @Test
    public void testHitNewIconExpIdMatchesButIsHitSkFalse() throws Throwable {
        // arrange
        DouhuResultModel model = new DouhuResultModel();
        model.setExpId("exp1");
        model.setHitSk(false);
        List<DouhuResultModel> douhuResultModels = Collections.singletonList(model);
        List<String> expIds = Arrays.asList("exp1", "exp2");
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertFalse("Expected false when expId matches but isHitSk is false", result);
    }

    /**
     * Test hitNewIcon method when no expId matches
     */
    @Test
    public void testHitNewIconNoExpIdMatches() throws Throwable {
        // arrange
        DouhuResultModel model = new DouhuResultModel();
        model.setExpId("exp3");
        model.setHitSk(true);
        List<DouhuResultModel> douhuResultModels = Collections.singletonList(model);
        List<String> expIds = Arrays.asList("exp1", "exp2");
        // act
        boolean result = target.hitNewIcon(douhuResultModels, expIds);
        // assert
        Assert.assertFalse("Expected false when no expId matches", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsNull() throws Throwable {
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Collections.emptyList(), "invalidServiceType");
        assertNull(result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsFootMassageOrScraping() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceType");
        skuAttrItemDto.setAttrValue("足疗");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(skuAttrItemDto), "足疗");
        assertEquals("足疗", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsEarPicking() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceTechnique");
        skuAttrItemDto.setAttrValue("earPickingTechnique");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(skuAttrItemDto), "采耳");
        assertEquals("earPickingTechnique", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsMassageOrEssentialOilSpaOrUlna() throws Throwable {
        SkuAttrItemDto bodyRegionAttr = new SkuAttrItemDto();
        bodyRegionAttr.setAttrName("bodyRegion");
        bodyRegionAttr.setAttrValue("全身");
        SkuAttrItemDto techniqueAttr = new SkuAttrItemDto();
        techniqueAttr.setAttrName("serviceTechnique");
        techniqueAttr.setAttrValue("massageTechnique");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(bodyRegionAttr, techniqueAttr), "推拿/按摩");
        assertEquals("全身massageTechnique", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsMoxibustion() throws Throwable {
        SkuAttrItemDto techniqueAttr = new SkuAttrItemDto();
        techniqueAttr.setAttrName("serviceTechnique");
        techniqueAttr.setAttrValue("moxibustionTechnique");
        SkuAttrItemDto methodAttr = new SkuAttrItemDto();
        methodAttr.setAttrName("moxibustionMethod");
        methodAttr.setAttrValue("boxMoxibustion");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(techniqueAttr, methodAttr), "艾灸");
        assertEquals("艾灸｜moxibustionTechnique｜boxMoxibustion", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsCup() throws Throwable {
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("serviceTechnique");
        skuAttrItemDto.setAttrValue("cupTechnique");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(skuAttrItemDto), "拔罐");
        assertEquals("拔罐（cupTechnique）", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsHead() throws Throwable {
        SkuAttrItemDto rangeAttr = new SkuAttrItemDto();
        rangeAttr.setAttrName("serviceBodyRange");
        rangeAttr.setAttrValue("head");
        SkuAttrItemDto techniqueAttr = new SkuAttrItemDto();
        techniqueAttr.setAttrName("serviceTechnique");
        techniqueAttr.setAttrValue("headTechnique");
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Arrays.asList(rangeAttr, techniqueAttr), "头疗");
        assertEquals("头疗｜headheadTechnique", result);
    }

    @Test
    public void testGetServiceFlowSkuName2WhenServiceTypeEnumIsOther() throws Throwable {
        String result = abstractFootMessageModuleOpt.getServiceFlowSkuName2(Collections.emptyList(), "otherServiceType");
        assertNull(result);
    }
}
