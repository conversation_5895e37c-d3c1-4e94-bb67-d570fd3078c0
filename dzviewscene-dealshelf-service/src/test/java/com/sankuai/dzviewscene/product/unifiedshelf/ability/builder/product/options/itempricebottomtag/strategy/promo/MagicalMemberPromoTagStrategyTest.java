package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import static com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

import java.math.BigDecimal;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class MagicalMemberPromoTagStrategyTest {

    @InjectMocks
    private MagicalMemberPromoTagStrategy strategy;

    private PriceBottomTagBuildReq req;

    private ProductPromoPriceM promoPriceM;

    private ProductM productM;

    private CardM cardM;

    private ActivityCxt context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        req = new PriceBottomTagBuildReq();
        promoPriceM = mock(ProductPromoPriceM.class);
        productM = mock(ProductM.class);
        cardM = mock(CardM.class);
        context = mock(ActivityCxt.class);
        req.setProductM(productM);
        req.setCardM(cardM);
        req.setContext(context);
        // Assuming 1 is for DP platform
        req.setPlatform(1);
        Map<String, Object> params = new HashMap<>();
        params.put("shelfVersion", 111);
        when(context.getParameters()).thenReturn(params);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(promoPriceM));
    }

    private PromoItemM createPromoItem() {
        PromoItemM promoItem = new PromoItemM();
        promoItem.setPromotionExplanatoryTags(Collections.singletonList(3));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        promotionOtherInfoMap.put(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue(), "1000");
        promoItem.setAmount(new BigDecimal(10));
        promoItem.setPromotionOtherInfoMap(promotionOtherInfoMap);
        return promoItem;
    }

    @Test
    public void testBuildTagWithNullPromoPrice() throws Throwable {
        when(PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(null);
        ShelfTagVO result = strategy.buildTag(req);
        assertNull(result);
        // No need to verify static method calls
    }
    
    /**
     * 测试神券优惠组合不包含神会员券时的情况
     */
    @Test
    public void testBuildTagWithoutMagicalMemberCoupon() throws Throwable {
        try (MockedStatic<PriceUtils> priceUtilsMock = mockStatic(PriceUtils.class);
             MockedStatic<DzPromoUtils> dzPromoUtilsMock = mockStatic(DzPromoUtils.class)) {
            // arrange
            priceUtilsMock.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(promoPriceM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)).thenReturn(false);
            
            // act
            ShelfTagVO result = strategy.buildTag(req);
            
            // assert
            assertNull(result);
        }
    }
    
    /**
     * 测试神会员新样式（包含膨胀信息）的情况
     */
    @Test
    public void testBuildTagWithNewStyleMagicalMember() throws Throwable {
        try (MockedStatic<PriceUtils> priceUtilsMock = mockStatic(PriceUtils.class);
             MockedStatic<DzPromoUtils> dzPromoUtilsMock = mockStatic(DzPromoUtils.class);
             MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = mockStatic(MagicalMemberTagUtils.class)) {
            // arrange
            priceUtilsMock.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(promoPriceM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)).thenReturn(true);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(createPromoItem());
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.hitMagicalMemberNewStyle(context)).thenReturn(true);

            // 模拟价格信息
            when(productM.getMarketPrice()).thenReturn("100.00");
            req.setSalePrice("80.00");

            // act
            ShelfTagVO result = strategy.buildTag(req);
            
            // assert
            // 因为buildNewStyleMagicalMemberTag中有很多内部逻辑和依赖，这里主要验证分支条件是否被命中
            assertNotNull(result);
        }
    }

    /**
     * 测试神会员新样式（包含膨胀信息）的情况
     */
    @Test
    public void testBuildTagWithNewStyleMagicalMember_dp_double_shelf() throws Throwable {
        try (MockedStatic<PriceUtils> priceUtilsMock = mockStatic(PriceUtils.class);
             MockedStatic<DzPromoUtils> dzPromoUtilsMock = mockStatic(DzPromoUtils.class);
             MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = mockStatic(MagicalMemberTagUtils.class)) {
            // arrange
            priceUtilsMock.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(promoPriceM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)).thenReturn(true);
            dzPromoUtilsMock.when(() -> DzPromoUtils.getMagicalMemberPromoItem(promoPriceM)).thenReturn(createPromoItem());
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.hitMagicalMemberNewStyle(context)).thenReturn(true);

            // 模拟价格信息
            when(productM.getMarketPrice()).thenReturn("100.00");
            req.setSalePrice("80.00");
            PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
            cfg.setUseNormalMagicalTag(true);
            req.setCfg(cfg);

            // act
            ShelfTagVO result = strategy.buildTag(req);

            // assert
            // 因为buildNewStyleMagicalMemberTag中有很多内部逻辑和依赖，这里主要验证分支条件是否被命中
            assertNotNull(result);
        }
    }
    
    /**
     * 测试构建标准样式的神会员标签
     */
    @Test
    public void testBuildTagWithStandardMagicalMember() throws Throwable {
        try (MockedStatic<PriceUtils> priceUtilsMock = mockStatic(PriceUtils.class);
             MockedStatic<DzPromoUtils> dzPromoUtilsMock = mockStatic(DzPromoUtils.class);
             MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = mockStatic(MagicalMemberTagUtils.class)) {
            // arrange
            priceUtilsMock.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(promoPriceM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)).thenReturn(true);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.hitMagicalMemberNewStyle(context)).thenReturn(false);
            
            // 模拟价格信息
            when(productM.getMarketPrice()).thenReturn("100.00");
            req.setSalePrice("80.00");

            // act
            ShelfTagVO result = strategy.buildTag(req);
            
            // assert
            assertNotNull(result);
            // 验证结果是标准神会员标签（而不是新样式）
            PictureModel prePic = result.getPrePic();
            assertNotNull(prePic);
            // 验证前置图片是否是神会员图标
            assertEquals(prePic.getPicUrl(), DP_NORMAL_MAGICAL_PIC_WITH_BG);
        }
    }
    
    /**
     * 测试美团样式的神会员标签（平台为美团或版本小于限制版本）
     */
    @Test
    public void testBuildTagWithMtStyle() throws Throwable {
        try (MockedStatic<PriceUtils> priceUtilsMock = mockStatic(PriceUtils.class);
             MockedStatic<DzPromoUtils> dzPromoUtilsMock = mockStatic(DzPromoUtils.class);
             MockedStatic<MagicalMemberTagUtils> magicalMemberTagUtilsMock = mockStatic(MagicalMemberTagUtils.class)) {
            // arrange
            priceUtilsMock.when(() -> PriceUtils.getUserHasPromoPrice(productM, cardM)).thenReturn(promoPriceM);
            dzPromoUtilsMock.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)).thenReturn(true);
            magicalMemberTagUtilsMock.when(() -> MagicalMemberTagUtils.hitMagicalMemberNewStyle(context)).thenReturn(false);
            
            // 设置平台为美团
            req.setPlatform(2);
            
            // 模拟价格信息
            when(productM.getMarketPrice()).thenReturn("100.00");
            req.setSalePrice("80.00");
            // act
            ShelfTagVO result = strategy.buildTag(req);
            
            // assert
            assertNotNull(result);
            // 验证使用美团样式的标签
            PictureModel prePic = result.getPrePic();
            assertNotNull(prePic);
            // 验证前置图片是否是美团神会员图标
            assertEquals(prePic.getPicUrl(), NORMAL_MAGICAL_PIC_WITH_BG);
            assertEquals(prePic.getPicHeight(), MAGICAL_PIC_HEIGHT);
            assertEquals(prePic.getAspectRadio(), NORMAL_MAGICAL_PIC_WITH_BG_RADIO, 0.001);
        }
    }
}
