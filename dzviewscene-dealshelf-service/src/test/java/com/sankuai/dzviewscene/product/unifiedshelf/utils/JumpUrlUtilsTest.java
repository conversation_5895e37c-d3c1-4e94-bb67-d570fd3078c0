package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import static org.junit.Assert.*;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.JumpUrlPatternEnum;
import java.util.HashMap;
import org.mockito.InjectMocks;
import org.mockito.Mock;

@RunWith(MockitoJUnitRunner.class)
public class JumpUrlUtilsTest {

    /**
     * 测试 extractFields 方法，当 url 为 null 时应该抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testExtractFieldsWhenUrlIsNull() throws Throwable {
        // arrange
        String url = null;
        // act
        JumpUrlUtils.extractFields(url);
    }

    /**
     * 测试 extractFields 方法，当 url 为空字符串时
     */
    @Test
    public void testExtractFieldsWhenUrlIsEmpty() throws Throwable {
        // arrange
        String url = "";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 extractFields 方法，当 url 不包含 ? 时
     */
    @Test
    public void testExtractFieldsWhenUrlNotContainsQuestionMark() throws Throwable {
        // arrange
        String url = "http://www.example.com";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 extractFields 方法，当 url 包含 ? 但是 ? 后面没有 key=value 格式的部分时
     */
    @Test
    public void testExtractFieldsWhenUrlContainsQuestionMarkButNoKeyValue() throws Throwable {
        // arrange
        String url = "http://www.example.com?";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("http://www.example.com", result.get("path"));
    }

    /**
     * 测试 extractFields 方法，当 url 包含 ? 且 ? 后面有 key=value 格式的部分时
     */
    @Test
    public void testExtractFieldsWhenUrlContainsQuestionMarkAndKeyValue() throws Throwable {
        // arrange
        String url = "http://www.example.com?key1=value1&key2=value2";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("http://www.example.com", result.get("path"));
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
    }

    /**
     * 测试 extractFields 方法，当 url 包含多个参数和锚点时
     */
    @Test
    public void testExtractFieldsWhenUrlContainsMultipleParamsAndAnchor() throws Throwable {
        // arrange
        String url = "http://www.example.com?key1=value1&key2=value2#anchor";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("http://www.example.com", result.get("path"));
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
    }

    /**
     * 测试 extractFields 方法，当 url 包含空值参数时
     */
    @Test
    public void testExtractFieldsWhenUrlContainsEmptyValue() throws Throwable {
        // arrange
        String url = "http://www.example.com?key1=&key2=value2";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("http://www.example.com", result.get("path"));
        assertEquals("", result.get("key1"));
        assertEquals("value2", result.get("key2"));
    }

    /**
     * 测试 extractFields 方法，当 url 包含特殊字符时
     */
    @Test
    public void testExtractFieldsWhenUrlContainsSpecialChars() throws Throwable {
        // arrange
        String url = "http://www.example.com?key1=val%20ue1&key2=value@2";
        // act
        Map<String, String> result = JumpUrlUtils.extractFields(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("http://www.example.com", result.get("path"));
        assertEquals("val%20ue1", result.get("key1"));
        assertEquals("value@2", result.get("key2"));
    }

    @Test
    public void testGetJumpUrlType_NullInput_ReturnsEmptyType() throws Throwable {
        // act
        String result = JumpUrlUtils.getJumpUrlType(null);
        // assert
        assertEquals("对于null输入应返回EMPTY_TYPE", JumpUrlUtils.EMPTY_TYPE, result);
    }

    @Test
    public void testGetJumpUrlType_EmptyString_ReturnsOtherType() throws Throwable {
        // act
        String result = JumpUrlUtils.getJumpUrlType("");
        // assert
        assertEquals("空字符串输入应返回OTHER_TYPE", JumpUrlUtils.OTHER_TYPE, result);
    }

    @Test
    public void testGetJumpUrlType_UrlWithoutParams_ReturnsOtherType() throws Throwable {
        // arrange
        String url = "https://example.com";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("无参数URL应返回OTHER_TYPE", JumpUrlUtils.OTHER_TYPE, result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesTuandeal_ReturnsTuandeal() throws Throwable {
        // arrange
        String url = "https://example.com/tuandeal?mrn_entry=dealDetail";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配TUANDEAL模式", JumpUrlPatternEnum.TUANDEAL.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesCarddeal_ReturnsCarddeal() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gcbu&mrn_entry=cardDetail";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配CARDDEAL模式", JumpUrlPatternEnum.CARDDEAL.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesPrepay_ReturnsPrepay() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gc&mrn_entry=prepayPage";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配PREPAY模式", JumpUrlPatternEnum.PREPAY.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesBookdetail_ReturnsBookdetail() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gc&mrn_entry=bookDetail&mrn_component=bookDetailComponent";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配BOOKDETAIL模式", JumpUrlPatternEnum.BOOKDETAIL.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesPackagedeal_ReturnsPackagedeal() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gc123&mrn_entry=mallCard&mrn_component=productPage";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配PACKAGEDEAL模式", JumpUrlPatternEnum.PACKAGEDEAL.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesMiniprogram_ReturnsMiniprogram() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gcTest&mrn_entry=miniProgram&mrn_component=miniProgramComponent";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应匹配MINIPROGRAM模式", JumpUrlPatternEnum.MINIPROGRAM.name(), result);
    }

    @Test
    public void testGetJumpUrlType_UrlMatchesNoPattern_ReturnsOtherType() throws Throwable {
        // arrange
        String url = "https://example.com?unknown_param=value";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("不匹配任何模式应返回OTHER_TYPE", JumpUrlUtils.OTHER_TYPE, result);
    }

    @Test
    public void testGetJumpUrlType_UrlWithSpecialChars_ReturnsCorrectType() throws Throwable {
        // arrange
        String url = "https://example.com?mrn_biz=gc&mrn_entry=prepay%20page";
        // act
        String result = JumpUrlUtils.getJumpUrlType(url);
        // assert
        assertEquals("应能处理包含特殊字符的URL", JumpUrlPatternEnum.PREPAY.name(), result);
    }

    @Test
    public void testGetJumpUrlType_DifferentParamOrder_ReturnsSameResult() throws Throwable {
        // arrange
        String url1 = "https://example.com?mrn_biz=gc&mrn_entry=bookDetail&mrn_component=bookDetailComponent";
        String url2 = "https://example.com?mrn_component=bookDetailComponent&mrn_entry=bookDetail&mrn_biz=gc";
        // act
        String result1 = JumpUrlUtils.getJumpUrlType(url1);
        String result2 = JumpUrlUtils.getJumpUrlType(url2);
        // assert
        assertEquals("参数顺序不应影响匹配结果", JumpUrlPatternEnum.BOOKDETAIL.name(), result1);
        assertEquals("参数顺序不应影响匹配结果", result1, result2);
    }

    @Test(expected = NullPointerException.class)
    public void testClassify_NullParams_ThrowsNPE() throws Throwable {
        JumpUrlUtils.classify(null);
    }

    @Test
    public void testClassify_EmptyParams_ReturnsNull() throws Throwable {
        Map<String, String> emptyParams = new HashMap<>();
        assertNull("Empty map should return null", JumpUrlUtils.classify(emptyParams));
    }

    @Test
    public void testClassify_NoPatternKeys_ReturnsNull() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("unrelated_key", "test_value");
        assertNull("Map with unrelated keys should return null", JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassify_ValueNotMatch_ReturnsNull() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "invalid_pattern_value");
        assertNull("Key present but value does not match pattern, should return null", JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassify_MatchTuandeal_ReturnsTuandeal() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "product_tuandeal_123");
        assertEquals("Should match TUANDEAL pattern", JumpUrlPatternEnum.TUANDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassify_MatchCarddeal_ReturnsCarddeal() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("mrn_biz", "gcbu");
        params.put("mrn_entry", "card_detail_page_v2");
        assertEquals("Should match CARDDEAL pattern", JumpUrlPatternEnum.CARDDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassify_MultipleMatches_ReturnsFirst() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "special_deal_detail");
        params.put("mrn_entry", "deal_detail_page");
        assertEquals("Should return the first matching enum (TUANDEAL)", JumpUrlPatternEnum.TUANDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsIsEmpty() throws Throwable {
        Map<String, String> emptyParams = new HashMap<>();
        assertNull(JumpUrlUtils.classify(emptyParams));
    }

    @Test
    public void testClassifyWhenParamsContainsNoPatternKey() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("other_key", "value");
        assertNull(JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsContainsKeyButValueNotMatch() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "invalid_value");
        assertNull(JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsMatchTuandealPattern() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "tuandeal123");
        assertEquals(JumpUrlPatternEnum.TUANDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsMatchCarddealPattern() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("mrn_biz", "gcbu");
        params.put("mrn_entry", "card_detail");
        assertEquals(JumpUrlPatternEnum.CARDDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsMatchMultiplePatterns() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", "deal_detail");
        params.put("mrn_entry", "deal_detail");
        assertEquals(JumpUrlPatternEnum.TUANDEAL, JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsContainsNoKey() throws Throwable {
        Map<String, String> params = new HashMap<>();
        assertNull(JumpUrlUtils.classify(params));
    }

    @Test
    public void testClassifyWhenParamsContainsKeyAndValueMatch() throws Throwable {
        Map<String, String> params = new HashMap<>();
        params.put("path", ".*tuandeal.*");
        assertEquals(JumpUrlPatternEnum.TUANDEAL, JumpUrlUtils.classify(params));
    }
}