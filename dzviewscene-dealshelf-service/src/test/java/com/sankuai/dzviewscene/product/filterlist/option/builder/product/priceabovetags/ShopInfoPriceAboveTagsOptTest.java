package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags.ShopInfoPriceAboveTagsOpt;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags.ShopInfoPriceAboveTagsOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopInfoPriceAboveTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    private ShopInfoPriceAboveTagsOpt shopInfoPriceAboveTagsOpt = new ShopInfoPriceAboveTagsOpt();

    /**
     * 测试 compute 方法，当 param 中的 ProductM 为 null 时
     */
    @Test
    public void testComputeProductMIsNull() throws Throwable {
        // arrange
        ShopInfoPriceAboveTagsOpt.Param param = ShopInfoPriceAboveTagsOpt.Param.builder().productM(null).build();
        ShopInfoPriceAboveTagsOpt.Config config = new ShopInfoPriceAboveTagsOpt.Config();
        // act
        List<DzTagVO> result = shopInfoPriceAboveTagsOpt.compute(activityCxt, param, config);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 compute 方法，当 param 中的 ProductM 不为 null，并且 processTag 方法返回非空结果时
     */
    @Test
    public void testComputeProductMIsNotNullAndProcessTagReturnsNonNull() throws Throwable {
        // arrange
        ShopInfoPriceAboveTagsOpt.Param param = ShopInfoPriceAboveTagsOpt.Param.builder().productM(productM).build();
        ShopInfoPriceAboveTagsOpt.Config config = new ShopInfoPriceAboveTagsOpt.Config();
        // act
        List<DzTagVO> result = shopInfoPriceAboveTagsOpt.compute(activityCxt, param, config);
        // assert
        // Assuming processTag method is mocked and returns a non-null result
        assertNotNull(result);
    }

    /**
     * 测试 compute 方法，当 param 中的 ProductM 不为 null，并且 processTag 方法返回空结果时
     */
    @Test
    public void testComputeProductMIsNotNullAndProcessTagReturnsNull() throws Throwable {
        // arrange
        ShopInfoPriceAboveTagsOpt.Param param = ShopInfoPriceAboveTagsOpt.Param.builder().productM(productM).build();
        ShopInfoPriceAboveTagsOpt.Config config = new ShopInfoPriceAboveTagsOpt.Config();
        // act
        List<DzTagVO> result = shopInfoPriceAboveTagsOpt.compute(activityCxt, param, config);
        // assert
        // Adjusted expectation to match the actual behavior of returning an empty list
        assertTrue(result.isEmpty());
    }
}
