package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PromoPriceBottomTagsUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductCouponM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoPriceBottomTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private PromoPriceBottomTagsOpt.Param param;

    @Mock
    private PromoPriceBottomTagsOpt.Config config;

    @Mock
    private ProductM productM;

    @Mock
    private ProductCouponM productCouponM;

    @Mock
    private ProductPromoPriceM productPromoPriceM;

    private PromoPriceBottomTagsOpt promoPriceBottomTagsOpt;

    @Before
    public void setUp() throws Exception {
        promoPriceBottomTagsOpt = new PromoPriceBottomTagsOpt();
        // Default mock setup
        when(param.getProductM()).thenReturn(productM);
        when(param.getPlatform()).thenReturn(1);
        when(config.getPopType()).thenReturn(1);
        when(config.isHideOnlyDealPromo()).thenReturn(false);
        // Setup product with default values
        when(productM.getCoupons()).thenReturn(Arrays.asList(productCouponM));
        when(productM.getPromo(anyInt())).thenReturn(productPromoPriceM);
        when(productCouponM.getCouponTag()).thenReturn("Test Coupon");
    }

    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        List<DzTagVO> result = promoPriceBottomTagsOpt.compute(activityCxt, param, null);
        assertNotNull(result);
    }

    @Test
    public void testComputeWhenConfigFieldsAreNull() throws Throwable {
        List<DzTagVO> result = promoPriceBottomTagsOpt.compute(activityCxt, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeWithValidProductMAndConfig() throws Throwable {
        // Setup valid product with promo
        when(productPromoPriceM.getPromoTag()).thenReturn("Test Promo");
        when(productPromoPriceM.getPromoItemList()).thenReturn(new ArrayList<>());
        List<DzTagVO> result = promoPriceBottomTagsOpt.compute(activityCxt, param, config);
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty with valid product", result.isEmpty());
    }

    @Test
    public void testComputeWithValidProductMAndNullPromo() throws Throwable {
        // Setup product with null promo
        when(productM.getPromo(anyInt())).thenReturn(null);
        List<DzTagVO> result = promoPriceBottomTagsOpt.compute(activityCxt, param, config);
        assertNotNull("Result should not be null", result);
        // Should contain at least coupon tags since we mocked coupon data
        assertFalse("Result should contain coupon tags", result.isEmpty());
    }

    @Test
    public void testComputeWithEmptyCoupons() throws Throwable {
        // Setup product with empty coupons
        when(productM.getCoupons()).thenReturn(new ArrayList<>());
        when(productM.getPromo(anyInt())).thenReturn(null);
        List<DzTagVO> result = promoPriceBottomTagsOpt.compute(activityCxt, param, config);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty with no promos and no coupons", result.isEmpty());
    }
}
