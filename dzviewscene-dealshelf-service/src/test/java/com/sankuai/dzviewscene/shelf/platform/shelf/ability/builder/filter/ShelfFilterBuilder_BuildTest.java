package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterComponentVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFilterBuilder_BuildTest {

    @Mock
    private ActivityContext ctx;

    @Mock
    private ShelfGroupM shelfGroupM;

    @Mock
    private FilterBuilderExt filterBuilderExt;

    @Test
    public void testBuildWhenMainDataIsNull() throws Throwable {
        ShelfFilterBuilder shelfFilterBuilder = new ShelfFilterBuilder();
        when(ctx.getMainData()).thenReturn(null);
        CompletableFuture<Map<String, FilterComponentVO>> result = shelfFilterBuilder.build(ctx);
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }
}
