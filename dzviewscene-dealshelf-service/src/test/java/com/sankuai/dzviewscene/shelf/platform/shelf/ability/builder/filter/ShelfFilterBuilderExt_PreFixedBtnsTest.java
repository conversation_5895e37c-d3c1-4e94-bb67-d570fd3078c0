package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFilterBuilderExt_PreFixedBtnsTest {

    /**
     * 测试 preFixedBtns 方法是否返回 null
     */
    @Test
    public void testPreFixedBtnsReturnNull() throws Throwable {
        // arrange
        ShelfFilterBuilderExt shelfFilterBuilderExt = new ShelfFilterBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        // act
        List<IconRichLabelVO> result = shelfFilterBuilderExt.preFixedBtns(activityContext, groupName);
        // assert
        assertNull(result);
    }
}
