package com.sankuai.dzviewscene.shelf.platform.list;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductListActivityContextBuilderTest {

    @InjectMocks
    private ProductListActivityContextBuilder productListActivityContextBuilder;

    @Mock
    private ActivityRequest activityRequest;

    @Mock
    private ActivityContext activityContext;

    /**
     * Test normal case.
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // Arrange
        Map<String, Object> params = new HashMap<>();
        params.put("platform", 1);
        when(activityRequest.getParams()).thenReturn(params);
        // Act
        ActivityContext result = productListActivityContextBuilder.build(activityRequest);
        // Assert
        assertNotNull(result);
        assertEquals(params, result.getParameters());
    }

    /**
     * Test case when ActivityRequest object is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildActivityRequestIsNull() throws Throwable {
        // Act
        productListActivityContextBuilder.build(null);
    }
}
