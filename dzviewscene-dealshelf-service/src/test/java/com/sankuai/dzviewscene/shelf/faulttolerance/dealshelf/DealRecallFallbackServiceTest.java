package com.sankuai.dzviewscene.shelf.faulttolerance.dealshelf;

import com.dianping.product.shelf.common.request.ShelfRequest;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.common.DealRecallFallbackComponent;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import graphql.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Maps;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.it.iam.common_base.exception.BizAssert.assertNull;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * 测试 DealRecallFallbackService 的 recallFallback 方法
 */
public class DealRecallFallbackServiceTest {

    @InjectMocks
    private DealRecallFallbackComponent service;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ActivityCxt ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(ctx.getActivityCode()).thenReturn(DealShelfActivity.CODE);
    }

    /**
     * 测试配置为空
     */
    @Test
    public void testRecallFallbackConfigNull() throws Throwable {
        // arrange
        service.config = null;

        // act
        service.recallFallback(ctx);

        // assert
        assertNull(service.config);
    }

    /**
     * 测试黑名单场景
     */
    @Test
    public void testRecallFallbackBlackList() throws Throwable {
        // arrange
        service.config = new DealRecallFallbackComponent.RecallFallbackConfig();
        service.config.setBlackSceneCodes(Lists.newArrayList("111"));
        boolean result = true;
        ctx.addParam("sceneCode", "111");
        // act
        service.recallFallback(ctx);

        // assert
        assertFalse(service.config.isOpen());
    }

    /**
     * 测试开关关闭
     */
    @Test
    public void testRecallFallbackSwitchOff() throws Throwable {
        // arrange
        service.config = new DealRecallFallbackComponent.RecallFallbackConfig();
        service.config.setOpen(false);
        boolean result = true;

        // act
        service.recallFallback(ctx);

        // assert
        assertFalse(service.config.isOpen());
    }


    /**
     * 测试串行查询降级
     */
    @Test
    public void testRecallFallbackLog() throws Throwable {
        // arrange
        service.config = new DealRecallFallbackComponent.RecallFallbackConfig();
        service.config.setOpen(true);

        Map<String, FilterM> map = Maps.newHashMap();
        map.put("团购", new FilterM());
        when(ctx.getSource(FilterFirstFetcher.CODE)).thenReturn(map);
        when(compositeAtomService.queryShopSaleDealIds(anyLong(), anyInt())).thenReturn(CompletableFuture.completedFuture(Lists.newArrayList(1L)));

        // act
        service.recallFallback(ctx);

        // assert
        assertTrue(service.config.isOpen());
    }

    /**
     * 测试并行查询降级
     */
    @Test
    public void testRecallFallbackLogParallel() throws Throwable {
        // arrange
        service.config = new DealRecallFallbackComponent.RecallFallbackConfig();
        service.config.setOpen(true);

        Map<String, FilterM> map = Maps.newHashMap();
        map.put("团购", new FilterM());
        when(ctx.getSource(FilterFirstFetcher.CODE)).thenReturn(map);
        when(ctx.getParam(ShelfActivityConstants.Params.hasShopDeal)).thenReturn(true);

        // act
        service.recallFallback(ctx);

        // assert
        assertTrue(service.config.isOpen());
    }

    @Test
    public void testBuildShelfRequest(){
        DealRecallFallbackComponent dealRecallFallbackComponent = new DealRecallFallbackComponent();
        ActivityCxt activityCxt = new ActivityCxt();
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(ShelfActivityConstants.Params.platform, 1);
        parameters.put(ShelfActivityConstants.Params.mtUserId, 1);
        parameters.put(ShelfActivityConstants.Params.dpUserId, 1);
        parameters.put(ShelfActivityConstants.Params.userAgent, 100);
        parameters.put(ShelfActivityConstants.Params.clientType, "ios");
        parameters.put(ShelfActivityConstants.Params.dpPoiIdL, 1L);
        parameters.put(ShelfActivityConstants.Params.mtPoiIdL, 1L);
        parameters.put(ShelfActivityConstants.Params.appVersion, "1.0");
        activityCxt.setParameters(parameters);
        Map map = dealRecallFallbackComponent.buildLogRequest(activityCxt);
        Assert.assertNotNull(map);
    }
}
