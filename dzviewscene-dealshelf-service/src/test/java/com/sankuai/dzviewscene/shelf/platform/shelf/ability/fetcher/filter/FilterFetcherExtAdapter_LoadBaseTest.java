package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FilterFetcherExtAdapter_LoadBaseTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductGroupM productGroupM;

    /**
     * 测试 loadBase 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testLoadBaseReturnNull() throws Throwable {
        // arrange
        FilterFetcherExtAdapter filterFetcherExtAdapter = new FilterFetcherExtAdapter() {

            @Override
            public FilterM loadBase(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
                return null;
            }
        };
        // act
        FilterM result = filterFetcherExtAdapter.loadBase(activityContext, "groupName", productGroupM);
        // assert
        assertNull(result);
    }
}
