package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FloorsBuilderExtAdapter_ItemComponentPreTitleTagTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductM productM;

    private FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {
    };

    /**
     * 测试 itemComponentPreTitleTag 方法，无论输入参数是什么，都会返回 null
     */
    @Test
    public void testItemComponentPreTitleTagReturnNull() throws Throwable {
        // arrange
        FloorsBuilderExtAdapter floorsBuilderExtAdapter = new FloorsBuilderExtAdapter() {

            @Override
            public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, long filterId, ProductM productM) {
                return null;
            }
        };
        // act
        FloatTagVO result = floorsBuilderExtAdapter.itemComponentPreTitleTag(activityContext, "groupName", 1L, productM);
        // assert
        assertNull(result);
    }

    /**
     * Test case for when productEntry is null. This should throw a NullPointerException as the method under test does not handle null inputs gracefully.
     */
    @Test(expected = NullPointerException.class)
    public void testCheckShowFilterProductEntryProductEntryIsNull() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        Map.Entry<Long, List<ProductM>> productEntry = null;
        // act
        boolean result = floorsBuilderExtAdapter.checkShowFilterProductEntry(activityContext, productEntry);
        // assert is handled by the expected exception
    }

    /**
     * Test case for when productEntry's value is null. This should return false as per the method's logic.
     */
    @Test
    public void testCheckShowFilterProductEntryValueIsNull() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        Map<Long, List<ProductM>> map = new HashMap<>();
        map.put(1L, null);
        Map.Entry<Long, List<ProductM>> productEntry = map.entrySet().iterator().next();
        // act
        boolean result = floorsBuilderExtAdapter.checkShowFilterProductEntry(activityContext, productEntry);
        // assert
        assertFalse("Expected false when productEntry's value is null", result);
    }

    /**
     * Test case for when productEntry's value is not null. This should return true as per the method's logic.
     */
    @Test
    public void testCheckShowFilterProductEntryValueIsNotNull() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        Map<Long, List<ProductM>> map = new HashMap<>();
        map.put(1L, Collections.singletonList(new ProductM()));
        Map.Entry<Long, List<ProductM>> productEntry = map.entrySet().iterator().next();
        // act
        boolean result = floorsBuilderExtAdapter.checkShowFilterProductEntry(activityContext, productEntry);
        // assert
        assertTrue("Expected true when productEntry's value is not null", result);
    }
}
