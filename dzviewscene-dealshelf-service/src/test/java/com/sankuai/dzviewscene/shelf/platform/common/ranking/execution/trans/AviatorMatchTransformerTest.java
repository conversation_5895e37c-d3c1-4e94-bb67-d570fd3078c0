package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AviatorMatchTransformerTest {

    @Mock
    private RankingContext context;

    @Mock
    private RankingItem item;

    /**
     * 测试 execute 方法，当 items 列表为空时
     */
    @Test
    public void testExecuteWhenItemsIsEmpty() throws Throwable {
        // arrange
        AviatorMatchTransformer transformer = new AviatorMatchTransformer("true");
        List<RankingItem> items = Collections.emptyList();
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 execute 方法，当 items 列表不为空，但所有元素都不满足 match 条件时
     */
    @Test
    public void testExecuteWhenItemsIsNotEmptyButNoItemMatches() throws Throwable {
        // arrange
        AviatorMatchTransformer transformer = new AviatorMatchTransformer("false");
        List<RankingItem> items = Arrays.asList(item);
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 execute 方法，当 items 列表不为空，且存在至少一个元素满足 match 条件时
     */
    @Test
    public void testExecuteWhenItemsIsNotEmptyAndItemMatches() throws Throwable {
        // arrange
        AviatorMatchTransformer transformer = new AviatorMatchTransformer("true");
        List<RankingItem> items = Arrays.asList(item);
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(items, result);
    }

    /**
     * 测试 match 方法，正常情况
     */
    @Test
    public void testMatchNormal() throws Throwable {
        // arrange
        AviatorMatchTransformer transformer = new AviatorMatchTransformer("true");
        // act
        boolean result = transformer.match(context, item);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 match 方法，异常情况
     */
    @Test(expected = Exception.class)
    public void testMatchException() throws Throwable {
        // arrange
        AviatorMatchTransformer transformer = new AviatorMatchTransformer("1 / 0");
        // act
        transformer.match(context, item);
    }
}
