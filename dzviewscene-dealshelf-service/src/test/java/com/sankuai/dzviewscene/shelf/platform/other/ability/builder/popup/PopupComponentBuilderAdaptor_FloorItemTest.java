package com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.business.other.popup.vo.DzPopupItemVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PopupComponentBuilderAdaptor_FloorItemTest {

    private PopupComponentBuilderAdaptor popupComponentBuilderAdaptor = new PopupComponentBuilderAdaptor();

    /**
     * 测试 floorItem 方法是否返回 null
     */
    @Test
    public void testFloorItemReturnNull() throws Throwable {
        // arrange
        long filterId = 1L;
        ProductM productM = new ProductM();
        ActivityContext ctx = new ActivityContext();
        // act
        DzPopupItemVO result = popupComponentBuilderAdaptor.floorItem(filterId, productM, ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试 floorItem 方法，当 filterId 为 0 时，是否返回 null
     */
    @Test
    public void testFloorItemWithZeroFilterId() throws Throwable {
        // arrange
        long filterId = 0L;
        ProductM productM = new ProductM();
        ActivityContext ctx = new ActivityContext();
        // act
        DzPopupItemVO result = popupComponentBuilderAdaptor.floorItem(filterId, productM, ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试 floorItem 方法，当 productM 为 null 时，是否返回 null
     */
    @Test
    public void testFloorItemWithNullProductM() throws Throwable {
        // arrange
        long filterId = 1L;
        ProductM productM = null;
        ActivityContext ctx = new ActivityContext();
        // act
        DzPopupItemVO result = popupComponentBuilderAdaptor.floorItem(filterId, productM, ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试 floorItem 方法，当 ctx 为 null 时，是否返回 null
     */
    @Test
    public void testFloorItemWithNullCtx() throws Throwable {
        // arrange
        long filterId = 1L;
        ProductM productM = new ProductM();
        ActivityContext ctx = null;
        // act
        DzPopupItemVO result = popupComponentBuilderAdaptor.floorItem(filterId, productM, ctx);
        // assert
        assertNull(result);
    }
}
