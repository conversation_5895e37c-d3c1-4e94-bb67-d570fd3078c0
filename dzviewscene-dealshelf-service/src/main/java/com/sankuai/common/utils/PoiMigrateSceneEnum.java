package com.sankuai.common.utils;

import lombok.Getter;

@Getter
public enum PoiMigrateSceneEnum {

    DealShelfOpenApiRPCService("DealShelfOpenApiRPCService", "团购货架 OpenApi 接口"),

    ActivityContextRequestBuilder("ActivityContextRequestBuilder", "活动上下文请求参数构造器"),

    CooperationValidator("CooperationValidator", "合作权限校验器"),

    HomeValidator("HomeValidator", "家居权限校验器"),

    ProductShelfFactory("ProductShelfFactory", "家居权限校验器"),

    DealFilterListActivityCtxBuilder("DealFilterListActivityCtxBuilder", "团购筛选列表上下文构造"),

    ProductShelfActivityCtxBuilder("ProductShelfActivityCtxBuilder", "商品货架上下文构造器"),

    ProductDetailActivityContextBuilder("ProductDetailActivityContextBuilder", "商品详情活动上下文构造器"),

    FindDealNearestShopIdOpt("FindDealNearestShopIdOpt", "最近团单门店id取数"),

    DealShelfActivityCtxBuilder("DealShelfActivityCtxBuilder", "团购货架上下文构造器"),

    ProductListActivityContextBuilder("ProductListActivityContextBuilder", "商品列表活动上下文构造器"),

    OtherActivityContextBuilder("OtherActivityContextBuilder", "非标准场景上下文构造器"),

    ShelfActivityContextBuilder("ShelfActivityContextBuilder", "货架上下文构造器"),

    BackroomActivityContextBuilder("BackroomActivityContextBuilder", "密室商品列表上下文构造器"),

    ResourceShopsHandler("ResourceShopsHandler", "基于推荐的剧本杀标品可玩门店召回"),

    ShopGeneralProductHandler("ShopGeneralProductHandler", "泛商品召回能力, 返回商品模型列表"),

    BackRoomQueryFetcher("BackRoomQueryFetcher", "密室预订货架召回能力"),
    UgcContext("UgcContext", "ugc的Context"),
    GeneralBookingHandler("GeneralBookingHandler", "泛商品预定召回能力"),
    SpuPaddingHandler("SpuPaddingHandler", "SpuPaddingHandler"),
    VenueShopConfigContextExt("VenueShopConfigContextExt", "获取场馆门店配置"),
    GeneralSearchProductHandler("GeneralSearchProductHandler", "泛商品按项目类型召回能力"),
    MultiGroupMergeQueryCacheFetcher("MultiGroupMergeQueryCacheFetcher", "缓存版本融合查询能力, 包含召回、填充、排序、截断等能力实现"),

    ;

    private final String scene;
    private final String desc;

    PoiMigrateSceneEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }
}
