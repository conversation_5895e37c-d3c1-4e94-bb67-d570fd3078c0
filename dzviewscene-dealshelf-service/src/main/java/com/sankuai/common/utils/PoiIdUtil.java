package com.sankuai.common.utils;

import com.dianping.cat.Cat;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.gmkt.activity.api.web.dto.response.CommodityRecord;
import com.dianping.gmkt.activity.api.web.dto.response.DealRecord;
import com.dianping.gmkt.activity.api.web.dto.response.NewDealRecord;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardItemDTO;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.SearchBoxJumpUrlVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.MoreComponentUrlVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.OceanBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.ocean.ProductShelfOceanRequest;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.UnifiedShelfOceanBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.productshelf.nr.assemble.req.HospitalDealShelfReq;
import com.sankuai.dzviewscene.productshelf.vu.vo.ProductShelfContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.cache.model.ShopCacheM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class PoiIdUtil {

    /**
     * POIID 加解密开关
     * 说明：团购货架展示逻辑已统一，但对外提供了 HTTP、RPC 协议的 API，POIID 加解密只需要改造 HTTP API，而不需要改造 RPC API，
     * 通过 Trace 传递该开关控制底层逻辑中对 POIID 的加解密
     */
    public static final String POIID_CRYPTO_ENABLE = "mtsi.poiId.crypto.enable";
    public static Long getMtPoiIdL(Map<String, Object> parameters, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(parameters, longName),
                ParamsUtil.getIntSafely(parameters, integerName));
    }

    public static Long getDpPoiIdL(Map<String, Object> parameters, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(parameters, longName),
                ParamsUtil.getIntSafely(parameters, integerName));
    }

    public static Long getMtPoiIdL(ActivityCxt activityCxt, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(activityCxt.getParameters(), longName),
                ParamsUtil.getIntSafely(activityCxt.getParameters(), integerName));
    }

    public static Long getDpPoiIdL(ActivityCxt activityCxt, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(activityCxt.getParameters(), longName),
                ParamsUtil.getIntSafely(activityCxt.getParameters(), integerName));
    }

    public static Long getMtPoiIdL(ActivityContext activityContext, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(activityContext, longName),
                ParamsUtil.getIntSafely(activityContext, integerName));
    }

    public static Long getDpPoiIdL(ActivityContext activityContext, String longName, String integerName) {
        return getLongId(ParamsUtil.getLongSafely(activityContext, longName),
                ParamsUtil.getIntSafely(activityContext, integerName));
    }

    public static String getMtPoiIdStr(ActivityContext activityContext, String longName, String integerName) {
        return String.valueOf(getLongId(ParamsUtil.getLongSafely(activityContext, longName),
                ParamsUtil.getIntSafely(activityContext, integerName)));
    }

    public static String getDpPoiIdStr(ActivityContext activityContext, String longName, String integerName) {
        return String.valueOf(getLongId(ParamsUtil.getLongSafely(activityContext, longName),
                ParamsUtil.getIntSafely(activityContext, integerName)));
    }

    // ------------
    public static Long getDpPoiIdL(ProductShelfOceanRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.common.utils.PoiIdUtil.getDpPoiIdL(com.sankuai.dzviewscene.product.shelf.ability.ocean.ProductShelfOceanRequest)");
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(ProductShelfOceanRequest request) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.common.utils.PoiIdUtil.getMtPoiIdL(com.sankuai.dzviewscene.product.shelf.ability.ocean.ProductShelfOceanRequest)");
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(DealListResponseAssembler.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(DealListResponseAssembler.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(ProductActivitiesFetcher.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(ProductActivitiesFetcher.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(ShelfDouHuFetcher.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getDpPoiIdL(CardFetcher.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(CardFetcher.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(OceanBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(OceanBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(UnifiedShelfOceanBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(UnifiedShelfOceanBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(UnifiedProductAreaBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), 0);
    }

    public static Long getMtPoiIdL(UnifiedProductAreaBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), 0);
    }

    public static Long getDpPoiIdL(FloorsBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(FloorsBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(FilterBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(FilterBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    public static Long getDpPoiIdL(UnifiedShelfFilterBuilder.Request request) {
        return getLongId(request.getDpPoiIdL(), request.getDpPoiId());
    }

    public static Long getMtPoiIdL(UnifiedShelfFilterBuilder.Request request) {
        return getLongId(request.getMtPoiIdL(), request.getMtPoiId());
    }

    // ---------

    public static Long getDpPoiIdL(SearchBoxJumpUrlVP.Param param) {
        return getLongId(param.getDpPoiIdL(), param.getDpPoiId());
    }

    public static Long getMtPoiIdL(SearchBoxJumpUrlVP.Param param){
        return getLongId(param.getMtPoiIdL(), param.getMtPoiId());
    }

//    public static Long getDpPoiIdL(ItemOceanLabsVP.Param param) {
//        return getLongId(param.getDpPoiIdL(), param.getDpPoiId());
//    }
//
//    public static Long getMtPoiIdL(ItemOceanLabsVP.Param param) {
//        return getLongId(param.getMtPoiIdL(), param.getMtPoiId());
//    }

    public static Long getDpPoiIdL(ItemJumpUrlVP.Param param) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.common.utils.PoiIdUtil.getDpPoiIdL(com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP$Param)");
        return getLongId(param.getDpPoiIdL(), param.getDpPoiId());
    }

    public static Long getMtPoiIdL(ItemJumpUrlVP.Param param) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.common.utils.PoiIdUtil.getMtPoiIdL(com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP$Param)");
        return getLongId(param.getMtPoiIdL(), param.getMtPoiId());
    }

    public static Long getDpPoiIdL(MoreComponentUrlVP.Param param) {
        return getLongId(param.getDpPoiIdL(), param.getDpPoiId());
    }

    public static Long getMtPoiIdL(MoreComponentUrlVP.Param param) {
        return getLongId(param.getMtPoiIdL(), param.getMtPoiId());
    }

    public static Long getDpShopIdL(SimpleShopDTO simpleShopDTO){
        return getLongId(simpleShopDTO.getLongDpShopId(), simpleShopDTO.getDpShopId());
    }

    // -----------------

    public static Long getShopIdL(HospitalDealShelfReq hospitalDealShelfReq){
        return getLongId(hospitalDealShelfReq.getShopIdL(), hospitalDealShelfReq.getShopId());
    }

    public static Long getShopIdL(StandardItemDTO standardItemDTO){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.common.utils.PoiIdUtil.getShopIdL(com.dianping.tpfun.product.api.sku.aggregate.dto.StandardItemDTO)");
        return getLongId(standardItemDTO.getLongShopId(), standardItemDTO.getShopId());
    }

    public static Long getShopIdL(ProductShelfContext context){
        return getLongId(context.getShopIdL(), context.getShopId());
    }

    public static Long getShopIdL(ShopCacheM shopCacheM){
        return getLongId(shopCacheM.getDpShopIdL(), shopCacheM.getDpShopId());
    }

    public static Long getShopIdL(ShopM shopM){
        return getLongId(shopM.getLongShopId(), shopM.getShopId());
    }

    public static Long getShopIdL(CommodityRecord commodityRecord){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.common.utils.PoiIdUtil.getShopIdL(com.dianping.gmkt.activity.api.web.dto.response.CommodityRecord)");
        return getLongId(commodityRecord.getShopIdL(), commodityRecord.getShopId());
    }

    public static List<Long> getShopIdsL(ProductM productM){
        return getLongIds(productM.getShopLongIds(), productM.getShopIds());
    }

    public static Long getShopIdL(NewDealRecord newDealRecord){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.common.utils.PoiIdUtil.getShopIdL(com.dianping.gmkt.activity.api.web.dto.response.NewDealRecord)");
        return getLongId(newDealRecord.getShopIdL(), newDealRecord.getShopId());
    }

    public static Long getShopIdL(DealGroupShop dealGroupShop){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.common.utils.PoiIdUtil.getShopIdL(com.dianping.deal.shop.dto.DealGroupShop)");
        return getLongId(dealGroupShop.getLongShopId(), dealGroupShop.getShopId());
    }

    public static Long getShopIdL(DealRecord dealRecord){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.common.utils.PoiIdUtil.getShopIdL(com.dianping.gmkt.activity.api.web.dto.response.DealRecord)");
        return getLongId(dealRecord.getShopIdL(), dealRecord.getShopId());
    }

    public static Long getMtPoiIdL(NewDealRecord newDealRecord){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.common.utils.PoiIdUtil.getMtPoiIdL(com.dianping.gmkt.activity.api.web.dto.response.NewDealRecord)");
        return getLongId(newDealRecord.getMtPoiIdL(), newDealRecord.getMtPoiId());
    }

    // ---------


    private static Long getLongId(Long longId, Integer integerId) {
        if (Objects.nonNull(longId) && longId != 0) {
            return longId;
        } else if (Objects.nonNull(integerId)) {
            return integerId.longValue();
        }
        return 0L;
    }

    private static List<Long> getLongIds(List<Long> longIds, List<Integer> integerIds) {
        if (CollectionUtils.isNotEmpty(longIds)) {
            return longIds;
        } else if (CollectionUtils.isNotEmpty(integerIds)) {
            return integerIds.stream().map(Integer::longValue).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
