package com.sankuai.dzviewscene.shelf.platform.common.model;

import com.sankuai.dztheme.deal.dto.DealPromoItemDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/9/8.
 */
public class ProductPromoPriceM {

    /**
     * 商品id
     */
    private long productId;

    /**
     * 优惠类型, 如:玩乐卡, 普通折扣卡, 会员日折扣卡, 优惠, 立减,com.sankuai.dztheme.deal.enums.PromoTypeEnum
     */
    private int promoType;

    /**
     * 优惠后价格
     */
    private BigDecimal promoPrice;

    /**
     * 优惠的价格标签, 如: 已省80
     */
    private String promoTag;

    /**
     * 国补价
     */
    private BigDecimal nationalSubsidyPrice;

    /**
     * 优惠文案前缀，比如：已优惠
     */
    private String promoTagPrefix;

    /**
     * 优惠之后价格标签
     */
    private String promoPriceTag;

    /**
     * 划线价
     */
    private String marketPrice;

    /**
     * 国补商品划线价
     */
    private BigDecimal nationalSubsidyMarketPrice;

    /**
     * 计算公式 = 1 - ((basePrice - promoPrice) / basePrice)
     */
    private BigDecimal discount;

    /**
     * 折扣标签
     */
    private String discountTag;

    /**
     * 可用时间
     */
    private String availableTime;

    /**
     * 用户是否持有卡
     */
    private boolean userHasCard;

    /**
     * 总共优惠多少
     */
    private BigDecimal totalPromoPrice;

    /**
     * 总共优惠多少
     */
    private String totalPromoPriceTag;

    /**
     * 优惠细节
     */
    private List<PromoItemM> promoItemList;

    /**
     * 券信息列表
     */
    private List<ProductCouponM> coupons;

    /**
     * 开始时间，单位是毫秒
     */
    private long startTime;

    /**
     * 结束时间，单位是毫秒
     */
    private long endTime;

    /**
     * 优惠需满足数量
     */
    private int promoQuantityLimit;

    /**
     * 优惠icon
     */
    private String icon;

    /**
     * 优惠icon文案
     */
    private String iconText;

    /**
     * 优惠文案类型
     *
     * @see com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum
     */
    private Integer promoTagType;

    /**
     * 团购次卡交易类型时有值：单次优惠后价格
     */
    private BigDecimal singlePrice;

    /**
     * 补充展示价格列表
     * key 参考 {@link com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum}
     */
    private Map<Integer, List<DealPromoItemDTO>> pricePromoInfoMap;

    /**
     * 扩展展示信息
     * key 枚举参考 {@link com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum}
     */
    private Map<String, String> extendDisplayInfo;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconText() {
        return iconText;
    }

    public void setIconText(String iconText) {
        this.iconText = iconText;
    }

    public int getPromoQuantityLimit() {
        return promoQuantityLimit;
    }

    public void setPromoQuantityLimit(int promoQuantityLimit) {
        this.promoQuantityLimit = promoQuantityLimit;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getPromoTagPrefix() {
        return promoTagPrefix;
    }

    public void setPromoTagPrefix(String promoTagPrefix) {
        this.promoTagPrefix = promoTagPrefix;
    }

    public List<ProductCouponM> getCoupons() {
        return coupons;
    }

    public void setCoupons(List<ProductCouponM> coupons) {
        this.coupons = coupons;
    }

    public boolean isUserHasCard() {
        return userHasCard;
    }

    public void setUserHasCard(boolean userHasCard) {
        this.userHasCard = userHasCard;
    }

    public String getDiscountTag() {
        return discountTag;
    }

    public void setDiscountTag(String discountTag) {
        this.discountTag = discountTag;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getAvailableTime() {
        return availableTime;
    }

    public void setAvailableTime(String availableTime) {
        this.availableTime = availableTime;
    }

    public int getPromoType() {
        return promoType;
    }

    public void setPromoType(int promoType) {
        this.promoType = promoType;
    }

    public BigDecimal getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(BigDecimal promoPrice) {
        this.promoPrice = promoPrice;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public String getPromoPriceTag() {
        return promoPriceTag;
    }

    public void setPromoPriceTag(String promoPriceTag) {
        this.promoPriceTag = promoPriceTag;
    }

    public BigDecimal getTotalPromoPrice() {
        return totalPromoPrice;
    }

    public void setTotalPromoPrice(BigDecimal totalPromoPrice) {
        this.totalPromoPrice = totalPromoPrice;
    }

    public String getTotalPromoPriceTag() {
        return totalPromoPriceTag;
    }

    public void setTotalPromoPriceTag(String totalPromoPriceTag) {
        this.totalPromoPriceTag = totalPromoPriceTag;
    }

    public List<PromoItemM> getPromoItemList() {
        return promoItemList;
    }

    public void setPromoItemList(List<PromoItemM> promoItemList) {
        this.promoItemList = promoItemList;
    }

    public String getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(String marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getPromoTagType() {
        return promoTagType;
    }

    public void setPromoTagType(Integer promoTagType) {
        this.promoTagType = promoTagType;
    }

    public BigDecimal getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(BigDecimal singlePrice) {
        this.singlePrice = singlePrice;
    }

    public Map<Integer, List<DealPromoItemDTO>> getPricePromoInfoMap() {
        return pricePromoInfoMap;
    }

    public void setPricePromoInfoMap(Map<Integer, List<DealPromoItemDTO>> pricePromoInfoMap) {
        this.pricePromoInfoMap = pricePromoInfoMap;
    }

    public Map<String, String> getExtendDisplayInfo() {
        return extendDisplayInfo;
    }

    public void setExtendDisplayInfo(Map<String, String> extendDisplayInfo) {
        this.extendDisplayInfo = extendDisplayInfo;
    }

    public long getProductId() {
        return productId;
    }

    public void setProductId(long productId) {
        this.productId = productId;
    }

    public BigDecimal getNationalSubsidyPrice() {
        return nationalSubsidyPrice;
    }

    public void setNationalSubsidyPrice(BigDecimal nationalSubsidyPrice) {
        this.nationalSubsidyPrice = nationalSubsidyPrice;
    }

    public BigDecimal getNationalSubsidyMarketPrice() {
        return nationalSubsidyMarketPrice;
    }

    public void setNationalSubsidyMarketPrice(BigDecimal nationalSubsidyMarketPrice) {
        this.nationalSubsidyMarketPrice = nationalSubsidyMarketPrice;
    }
}
