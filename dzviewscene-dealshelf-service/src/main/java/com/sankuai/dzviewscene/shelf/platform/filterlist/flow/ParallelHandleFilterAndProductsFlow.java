package com.sankuai.dzviewscene.shelf.platform.filterlist.flow;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@ActivityFlow(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE,
        flowCode = ParallelHandleFilterAndProductsFlow.FILTER_LIST_CODE, name = "并行处理筛选和商品区")
public class ParallelHandleFilterAndProductsFlow implements IActivityFlow<FilterProductsM> {

    public static final String FILTER_LIST_CODE = "FilterFirstWithProductsFlow";

    @Override
    public CompletableFuture<FilterProductsM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.flow.ParallelHandleFilterAndProductsFlow.execute(AbstractActivity,ActivityContext)");
        // 1. 生成筛选
        CompletableFuture<List<FilterM>> filtersCompletableFuture = activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE).build(ctx);
        // 3. 召回: 商品组名->商品列表
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx).thenCompose(productGroups -> {
            // 4. 暂存召回结果, 能力实现自行填充
            ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
        });
        return CompletableFuture.allOf(filtersCompletableFuture, productGroupsCompletableFuture).thenApply(v -> {
            FilterProductsM filterProductsM = new FilterProductsM();
            List<FilterM> filterList = filtersCompletableFuture.join();
            if (CollectionUtils.isNotEmpty(filterList)) {
                filterProductsM.setFilters(filterList);
            }
            Map<String, ProductGroupM> productGroupMap = productGroupsCompletableFuture.join();
            if (MapUtils.isNotEmpty(productGroupMap)) {
                filterProductsM.setProductGroup(productGroupMap.entrySet().stream().findFirst().map(Map.Entry::getValue).orElse(null));
            }
            return filterProductsM;
        });
    }
}
