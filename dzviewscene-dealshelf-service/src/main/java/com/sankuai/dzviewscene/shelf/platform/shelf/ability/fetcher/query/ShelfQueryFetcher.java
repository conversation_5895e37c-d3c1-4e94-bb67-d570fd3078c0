package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.Platform;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.product.shelf.common.dto.*;
import com.dianping.product.shelf.common.dto.featureoption.NavTagOption;
import com.dianping.product.shelf.common.dto.featureoption.OptionIdentity;
import com.dianping.product.shelf.common.enums.NavRouterTypeEnum;
import com.dianping.product.shelf.common.enums.ProductType;
import com.dianping.product.shelf.common.enums.ShelfItemDataNameEnum;
import com.dianping.product.shelf.common.enums.ShelfTypeEnum;
import com.dianping.product.shelf.common.request.ShelfNavTabProductRequest;
import com.dianping.product.shelf.common.request.ShelfRequestAttrMapKeys;
import com.dianping.product.shelf.common.shelf.item.data.dto.SkuDTO;
import com.dianping.product.shelf.common.utils.ShelfItemDataDtoGsonUtil;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.product.shelf.common.ProductAnchorInfo;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.medicalbeauty.ProductPlatformUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.ability.config.DegradeConfig;
import com.sankuai.dzviewscene.shelf.platform.common.ability.degrade.query.PlatformProductDegradeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.ShelfMultiRankService;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.ShelfCacheGreyConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.ShelfSceneCacheConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.WholePlatformShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.BPShelfRequestBuilder;
import com.sankuai.dzviewscene.shelf.platform.utils.HomeStoreUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 参数列表:
 * 1. {@link ShelfActivityConstants.Params#selectedFilterId}
 * 2. {@link ShelfActivityConstants.Params#platform}
 * 3. {@link ShelfActivityConstants.Params#clientType}
 * 4. {@link ShelfActivityConstants.Params#dpPoiId}
 * 5. {@link ShelfActivityConstants.Params#mtPoiId}
 * <p>
 * 返回结果:
 * 1. {@link ProductGroupM} 只会填充ID字段
 * <p>
 * 通用召回逻辑
 * <p>
 * Created by float on 2020/8/23.
 */
@AbilityInstance(name = "平台默认货架商品召回实例")
public class ShelfQueryFetcher extends QueryFetcher {

    private static final String ATTR_RECOMMEND = "attr_recommend";

    /**
     * 平台召回缓存过期时间
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformquery.cache.expire.time", defaultValue = "86400")
    private int platformQueryCacheExpireTime;

    /**
     * 平台召回缓存刷新时间
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformquery.cache.refresh.time", defaultValue = "60")
    private int platformQueryCacheRefreshTime;

    /**
     * 平台召回缓存总开关
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformquery.cache.switch", defaultValue = "false")
    private boolean queryCacheSwitch;

    /**
     * 平台召回灰度策略
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformquery.cache.grey.config")
    private ShelfCacheGreyConfig queryGreyConfig;

    /**
     * 召回缓存首屏商品大小
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.platformquery.cache.firstscreen.size", defaultValue = "20")
    private int queryCacheFirstScreenSize;

    /**
     * 置顶商品场景列表
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.platformquery.top.product.scenecode.list", defaultValue = "[]")
    private List<String> topSceneCodes;

    @Resource
    private DegradeConfig degradeConfig;

    public static final String ADDITION_ATTR_KEY = "navAdditionAttr";

    private static final String REDIS_FILTER_CATEGORY = "dzp_plat_filter";

    private static final String REDIS_QUERY_CATEGORY = "dzp_plat_filterproducts";

    private static final String FILTER_PARAM_SEPARATOR = ",";

    private static final String FILTER_PARAM_NAV_SEPARATOR = "$";

    private static CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-vc", 100);

    private static TypeReference<FilterProductGroups> queryCacheTypeReference = new TypeReference<FilterProductGroups>() {
    };

    private static final String ALGORITHM_QUERY_ID_ATTR_KEY = "dz_query_id";

    private static final String TOP_DISPLAY_ADDITIONAL_KEY = "isDuitou";

    private static final String TOP_DISPLAY_EXT_EDU_KEY = "isEduDuiTou";
    private static final String TOP_DISPLAY_EXT_EDU_CATE_KEY = "isEduCategoryDuiTou";
    /**
     * 医疗货架堆头标识
     */
    private static final String TOP_DISPLAY_EXT_MEDICAL_KEY = "isMedicalDuiTou";

    /**
     * 月子中心房型套餐-特惠团购标识
     */
    private static final String TOP_DISPLAY_EXT_NURSING_ROOM_KEY = "isSuperPreferential";

    /**
     * 旗舰店主体货架的 scene
     */
    private static final List<String> FLAGSHIP_STORE_SHELF_SCENE = Lists.newArrayList("joy_flagship_store_hot_deal_module", "joy_flagship_store_hot_deal_landing_page");

    @Resource
    private AtomFacadeService atomFacadeService;

    @Resource
    private PlatformProductDegradeQueryFetcher platformProductDegradeQueryFetcher;

    @Resource
    private WholePlatformShelfFilterFetcher wholePlatformShelfFilterFetcher;

    @Resource
    private WholePlatformShelfQueryFetcher wholePlatformShelfQueryFetcher;

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityContext ctx) {
        /**
         * 商品组名对应{@link ShelfNavProductComponent#getName()}字段, 所以当适应当前能力实现的时候, 商品组名需要
         */
        Map<String, String> productComponent2Group = ctx.getParam(Params.productComponent2Group);
        if (MapUtils.isEmpty(productComponent2Group)) {
            throw new BusinessException("需要传入productComponent2Group参数指定商品组件ID和商品组名映射关系");
        }
        // F型货架商品查询
        if (ParamsUtil.getBooleanSafely(ctx.getParameters(), ShelfActivityConstants.Params.isFShelf)) {
//            wholePlatformShelfFilterFetcher.build(ctx);
            return wholePlatformShelfQueryFetcher.build(ctx);
        }

        // T型货架商品查询
        ShelfNavTabProductRequest queryRequest = buildQueryRequest(ctx);

        // 构造组件和商品组映射
        CompletableFuture<Map<String, ProductGroupM>> componentId2ProductGroupCompletableFuture = getProductGroupComponent(queryRequest, ctx.getSceneCode())
                .thenApply(groupComponent -> buildComponentId2ProductGroup(groupComponent, ctx));

        // 2. 转换为商品组合商品组映射
        CompletableFuture<Map<String, ProductGroupM>> groupNameProductGroupMMapFuture = componentId2ProductGroupCompletableFuture.thenApply(componentId2Product -> convertGroupNameProductGroupMMap(componentId2Product, productComponent2Group));
        // 3. 根据商品召回结果降级
        return groupNameProductGroupMMapFuture.thenCompose(groupNameProductGroupMMap -> getGroupNameProductGroupMMapFutureByResultDegrade(groupNameProductGroupMMap, ctx));
    }

    private Map<String, ProductGroupM> convertGroupNameProductGroupMMap(Map<String, ProductGroupM> componentId2Product, Map<String, String> productComponent2Group) {
        if (MapUtils.isEmpty(componentId2Product)) {
            return Maps.newHashMap();
        }
        return componentId2Product.entrySet()
                .stream()
                .collect(HashMap::new,
                        (map, componentId2ProductEntry) -> {
                            map.put(findGroupName(componentId2ProductEntry.getKey(), productComponent2Group), componentId2ProductEntry.getValue());
                        }, HashMap::putAll);
    }

    private CompletableFuture<Map<String, ProductGroupM>> getGroupNameProductGroupMMapFutureByResultDegrade(Map<String, ProductGroupM> groupNameProductGroupMMap, ActivityContext ctx) {
        boolean validQueryResult = isValidQueryResult(groupNameProductGroupMMap);
        if (validQueryResult || !degradeConfig.queryDegradeSwitchOnBySceneCode(ctx.getSceneCode())) {
            return CompletableFuture.completedFuture(groupNameProductGroupMMap);
        }
        return platformProductDegradeQueryFetcher.build(ctx);
    }

    /**
     * 商品组合商品组映射中所有映射都没有值才降级，否则不降级
     * 注：教培的货架中的某个筛选项下有团购和泛商品货架，如果团购货架没有数据，可能是符合预期的（针对这中情况无法区分是否正常）。针对多个货架的情况进行粗略降级
     *
     * @param groupNameProductGroupMMap
     * @return
     */
    private boolean isValidQueryResult(Map<String, ProductGroupM> groupNameProductGroupMMap) {
        if (MapUtils.isEmpty(groupNameProductGroupMMap)) {
            return false;
        }
        ProductGroupM validProductGroupM = groupNameProductGroupMMap.values().stream()
                .filter(productGroupM -> productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts()))
                .findFirst()
                .orElse(null);
        return validProductGroupM != null;
    }

    private Map<String, ProductGroupM> buildComponentId2ProductGroup(FilterProductGroups filterProductGroups, ActivityContext ctx) {
        if (filterProductGroups == null || CollectionUtils.isEmpty(filterProductGroups.getGroups())) {
            return null;
        }
        addQueryParams(filterProductGroups, ctx);
        addNavTagIdParams(filterProductGroups, ctx);
        return filterProductGroups.getGroups()
                .stream()
                .collect(HashMap::new,
                        (map, productGroup) -> {
                            ProductGroupM productGroupM = buildProductGroup(productGroup, ctx);
                            map.put(String.valueOf(productGroup.getGroupId()), productGroupM);
                            // 由于同一个类目下货架的Id可能不同，所以兼容使用name做筛选
                            map.put(productGroup.getName(), productGroupM);

                        },
                        HashMap::putAll);
    }

    private String findGroupName(String componentId, Map<String, String> component2Group) {
        if (component2Group.get(componentId) == null) {
            return componentId;
        }
        return component2Group.get(componentId);
    }

    private void addQueryParams(FilterProductGroups filterProductGroups, ActivityContext ctx) {
        if (MapUtils.isEmpty(filterProductGroups.getAttrs()) || !filterProductGroups.attrs.containsKey(ALGORITHM_QUERY_ID_ATTR_KEY)) {
            return;
        }
        ctx.addParam(Params.algorithmQueryId, filterProductGroups.attrs.get(ALGORITHM_QUERY_ID_ATTR_KEY));
    }

    private void addNavTagIdParams(FilterProductGroups filterProductGroups, ActivityContext ctx) {
        if (filterProductGroups.getShelfNavTagId() <= 0) {
            return;
        }
        ctx.addParam(ShelfActivityConstants.Params.recalledFilterId, filterProductGroups.getShelfNavTagId());
    }

    private ProductGroupM buildProductGroup(ProductGroup productGroup, ActivityContext ctx) {
        if (productGroup == null) {
            return new ProductGroupM();
        }

        ProductGroupM productGroupM = new ProductGroupM();
        int pageNo = NumberUtils.toInt(ctx.getParam(ShelfActivityConstants.Params.pageNo) + "", 0);
        int pageSize = NumberUtils.toInt(ctx.getParam(ShelfActivityConstants.Params.pageSize) + "", 0);
        if (pageNo * pageSize < productGroup.getTotalCount()) {
            productGroupM.setHasNext(true);
        }
        productGroupM.setTotal(productGroup.getTotalCount());
        if (CollectionUtils.isEmpty(productGroup.getProducts())) {
            return productGroupM;
        }
        Map<String, Integer> productType2ShelfMinVersion = getProductType2ShelfMinVersion(productGroup.getName(), ctx);
        int shelfVersion = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfVersion);
        productGroupM.setProducts(
                productGroup.getProducts()
                        .stream()
                        .filter(product -> satisfyProductTypeVersionCondition(productType2ShelfMinVersion, product, shelfVersion))
                        .map(product -> buildProductM(product, buildExtAttrM(productGroup.getAttrs()), ctx))
                        .collect(Collectors.toList())
        );
        productGroupM.setProductHierarchyRoot(productGroup.getProductHierarchyRoot());
        return productGroupM;
    }

    private Map<String, Integer> getProductType2ShelfMinVersion(String groupName, ActivityContext ctx) {
        Map<String, Map<String, Object>> groupParams = ctx.getParam(Params.groupParams);
        if (MapUtils.isEmpty(groupParams) || MapUtils.isEmpty(groupParams.get(groupName))) {
            return new HashMap<>();
        }
        return (Map<String, Integer>) ParamsUtil.getValue(groupParams.get(groupName), Params.productType2ShelfMinVersion, new HashMap<>());
    }

    private boolean satisfyProductTypeVersionCondition(Map<String, Integer> productType2ShelfMinVersion, Product product, int shelfVersion) {
        //召回配置中的数据无效，默认商品满足要求，不需要过滤
        if (MapUtils.isEmpty(productType2ShelfMinVersion)
                || productType2ShelfMinVersion.get(String.valueOf(product.getProductType())) == null
                || productType2ShelfMinVersion.get(String.valueOf(product.getProductType())) <= 0) {
            return true;
        }
        //货架的版本大约等于当前场景的最低展示版本，则说明商品版本满足要求，不需要过滤
        return shelfVersion >= productType2ShelfMinVersion.get(String.valueOf(product.getProductType()));
    }

    private ProductHierarchyNodeM buildProductHierarchy(List<ProductBaseInfo> productBaseInfos) {
        if (CollectionUtils.isEmpty(productBaseInfos)) {
            return null;
        }
        // 虚拟根节点
        ProductHierarchyNodeM rootNode = new ProductHierarchyNodeM();
        // 构建层级节点
        List<ProductHierarchyNodeM> productHierarchyNodes = productBaseInfos.stream()
                .map(productBaseInfo -> convertToHierarchyNode(productBaseInfo, rootNode))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        rootNode.setChildren(productHierarchyNodes);
        return rootNode;
    }

    private ProductHierarchyNodeM convertToHierarchyNode(ProductBaseInfo productBaseInfo, ProductHierarchyNodeM parent) {
        if (productBaseInfo == null) {
            return null;
        }
        ProductHierarchyNodeM node = new ProductHierarchyNodeM();
        // 设置基本属性
        node.setProductId(Long.parseLong(productBaseInfo.getProductId()));
        node.setProductType(productBaseInfo.getProductType());
        node.setParent(parent);
        // 递归构建子节点
        if (CollectionUtils.isNotEmpty(productBaseInfo.getChildProductList())) {
            List<ProductHierarchyNodeM> childNodes = productBaseInfo.getChildProductList().stream()
                    .map(child -> convertToHierarchyNode(child, node))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            node.setChildren(childNodes);
        }
        return node;
    }

    /**
     * 构造ProductM的extAttr
     */
    private List<AttrM> buildExtAttrM(Map<String, String> attrs) {
        if (MapUtils.isEmpty(attrs)) {
            return null;
        }
        List<AttrM> attrMList = new ArrayList<>();
        AttrM attrM = new AttrM(ADDITION_ATTR_KEY, JsonCodec.encode(attrs));
        attrMList.add(attrM);
        return attrMList;
    }

    private ProductM buildProductM(Product product, List<AttrM> attrs, ActivityContext ctx) {
        ProductM productM = new ProductM();
        productM.setProductId(product.getProductId());
        productM.setProductType(product.getProductType());
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            ProductIdM productIdM = new ProductIdM();
            productIdM.setId(productM.getProductId());
            //一口价类标品的id类型（资源类型）
            productIdM.setType(16);
            productM.setId(productIdM);
        }
        productM.setExtAttrs(attrs);

        productM.setSkuIdList(product.getSkuIdList());
        productM.setSptSpuId(product.getSptSpuId());
        setItemUnitList(productM, product);
        productM.setLowPriceSkuId(product.getLowPriceSkuId());

        if (HomeStoreUtils.isHomeStoreScene(ctx.getSceneCode())) {
            setProductMShopIdsFromExtraMap(product, ctx, productM);
        }

        setShopRecommendAttr(productM, product.isRecommend());
        setTopDisplayProductAttr(productM, product.isTopDisplay());
        setExtraAttr(product, productM);
        return productM;
    }

    private void setProductMShopIdsFromExtraMap(Product product, ActivityContext ctx, ProductM productM) {
        if (product == null || MapUtils.isEmpty(product.getExtraMap())) {
            return;
        }
        Object platformObj = ctx.getParam(ShelfActivityConstants.Params.platform);
        int platform = platformObj != null ? NumberUtils.toInt(platformObj.toString(), 1) : 1;
        Map<String, String> extraMap = product.getExtraMap();
        String shopIdStr = platform == VCPlatformEnum.MT.getType() ? extraMap.get("MT_POIID_IN_MALL") : extraMap.get("DP_POIID_IN_MALL");
        Long shopId = null;
        if (shopIdStr != null) {
            try {
                shopId = Long.valueOf(shopIdStr);
            } catch (NumberFormatException e) {
                Cat.logError("Long.valueOf(shopIdStr) is error", e);
            }
        }
        productM.setShopIds(shopId != null ? Lists.newArrayList(shopId.intValue()) : new ArrayList<>());
        productM.setShopLongIds(shopId != null ? Lists.newArrayList(shopId) : new ArrayList<>());
    }


    private void setExtraAttr(Product product, ProductM productM) {
        if (Objects.isNull(product) || Objects.isNull(productM)) {
            return;
        }
        Map<String, String> extraMap = product.getExtraMap();
        //扩展属性见com.dianping.product.shelf.common.enums.ProductBaseInfoExtraKeyEnum
        if (MapUtils.isNotEmpty(extraMap)) {
            extraMap.forEach(productM::setAttr);
        }
    }

    private void setShopRecommendAttr(ProductM productM, boolean isShopRecommend) {
        if (productM == null) {
            return;
        }
        if (isShopRecommend) {
            productM.setAttr(ATTR_RECOMMEND, Boolean.TRUE.toString());
            return;
        }
        // productM.setAttr(ATTR_RECOMMEND, Boolean.FALSE.toString());
        return;
    }

    private void setTopDisplayProductAttr(ProductM productM, boolean isTopDisplay) {
        if (isTopDisplay) {
            productM.setAttr(ProductMAttrUtils.TOP_DISPLAY_PRODUCT, Boolean.TRUE.toString());
        }
    }

    private void setItemUnitList(ProductM productM, Product product) {
        if (CollectionUtils.isNotEmpty(product.getItemUnitList())) {
            productM.setItemUnitList(product.getItemUnitList().stream().map(t -> {
                ItemUnitM itemUnitM = new ItemUnitM();
                itemUnitM.setItemUnitId(t.getItemUnitId());
                itemUnitM.setItemUnitType(t.getItemUnitType());
                return itemUnitM;
            }).collect(Collectors.toList()));
        }
    }

    protected ShelfNavTabProductRequest buildQueryRequest(ActivityContext activityContext) {
        ShelfNavTabProductRequest request = new ShelfNavTabProductRequest();
        int platform = NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.platform) + "", 1);
        request.setPageNo(NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.pageNo) + "", 0));
        request.setPageSize(NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.pageSize) + "", 0));
        request.setShelfNavTagId(NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.selectedFilterId) + "", 0));
        request.setPlatform(platform);
        request.setClientType(ProductPlatformUtils.getClientType(activityContext.getParam(ShelfActivityConstants.Params.userAgent), activityContext.getParam(ShelfActivityConstants.Params.clientType)));
        request.setDpPoiid(PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId));
        request.setMtPoiid(PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId));
        request.setSortType(getSortType(activityContext));
        request.setCityId(getCityId(activityContext, request.getPlatform()));
        request.setAttrMap(buildQueryRequestAttr(activityContext));
        request.setUserId(getUserId(activityContext, request.getPlatform()));
        request.setSceneCode(activityContext.getParam(QueryFetcher.Params.platformSceneCode));
        request.setVersion(activityContext.getParam(ShelfActivityConstants.Params.appVersion));
        request.setExps(buildExpsOfShelfRequest(activityContext));
        String deviceId = activityContext.getParam(ShelfActivityConstants.Params.deviceId);
        if (PlatformUtil.isMT(platform)) {
            request.setUuid(deviceId);
        } else {
            request.setDpId(deviceId);
        }
        BPShelfRequestBuilder.addSceneTypeParam(activityContext, request);
        //填充多条件筛选选项
        paddingFilterOptionParam(request, activityContext);
        //填充品牌旗舰店的请求
        paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(request, activityContext);
        //导航路由参数赋值
        paddingNavRouterRequest(request, activityContext);
        return request;
    }

    private int getSortType(ActivityContext activityContext) {
        // 货架只需要有一个排序组合类型，具体每个filter的排序在商品BP配置
        int sortTypeFromRequest = NumberUtils.toInt(activityContext.getParam(Params.querySortType) + "", 0);
        if (sortTypeFromRequest != 0) {
            return sortTypeFromRequest;
        }
        Map<String, Map<String, Object>> groupParam = activityContext.getParam(QueryFetcher.Params.groupParams);
        if (MapUtils.isEmpty(groupParam)) {
            return 0;
        }
        for (Map<String, Object> paramValues : groupParam.values()) {
            int sortTypeFromGroupParam = NumberUtils.toInt(paramValues.get(Params.querySortType) + "", 0);
            if (sortTypeFromGroupParam != 0) {
                return sortTypeFromGroupParam;
            }
        }
        return 0;
    }

    private Map<String, String> buildQueryRequestAttr(ActivityContext ctx) {
        Map<String, String> attrMap = Maps.newHashMap();
        attrMap.put("unionId", ctx.getParam(ShelfActivityConstants.Params.unionId));
        String searchKeyword = ctx.getParam(ShelfActivityConstants.Params.keyword);
        if (StringUtils.isNotEmpty(searchKeyword)) {
            attrMap.put("searchkeyword", searchKeyword);
            buildSearchKeyScene(attrMap, searchKeyword);
        }
        //搜索词命中品牌词则不叠加可解释性置顶，这里将下挂商品置空，是避免后面楼层构造的时候被置顶
        if (hitBrandKeyWord(ctx)) {
            ctx.addParam(ShelfActivityConstants.Params.summaryProductIds, StringUtils.EMPTY);
        }
        //搜索可解释置顶商品
        addDealAndSpuTopIds(ctx, attrMap);
        // 经纬度
        if (ctx.getParam(ShelfActivityConstants.Params.lat) != null) {
            attrMap.put("lat", ctx.getParam(ShelfActivityConstants.Params.lat).toString());
            attrMap.put("lng", ctx.getParam(ShelfActivityConstants.Params.lng).toString());
        }
        addZdcTagMatchParam(ctx, attrMap);
        // 春节不打烊
        addSpringFestivalNoClose(ctx, attrMap);
        // 透传搜索页品牌信息
        addBrandInfoParam(ctx, attrMap);
        // 透传货架堆头类型
        addShelfTopDisplayParam(ctx, attrMap);
        //神券筛选标签
        BPShelfRequestBuilder.addMagicalMemberParam(ctx, attrMap);
        // 向bp和选单透传定位城市
        BPShelfRequestBuilder.addLocationCityId(ctx, attrMap);

        //游戏厅游戏币货架仅召回游戏币筛选项
        BPShelfRequestBuilder.addGameCoinParam(ctx, attrMap);
        //界面来源
        attrMap.put("pageSource", ctx.getParam(ShelfActivityConstants.Params.pageSource));
        //用户搜索词
        addSearchItemParam(ctx, attrMap);
        //是否预订货架
        addBookShelfParam(ctx, attrMap);
        return attrMap;
    }

    private void addBrandInfoParam(ActivityContext ctx, Map<String, String> attrMap) {
        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
        List<String> brandSelect = ParamUtil.extractBySummarypids(summaryProductIds, "brandSelect");
        if (CollectionUtils.isNotEmpty(brandSelect)) {
            attrMap.put(ShelfRequestAttrMapKeys.SEARCH_IMPLANT_BRAND.getKey(), brandSelect.get(0));
        }
    }

    public void buildSearchKeyScene(Map<String, String> attrMap, String searchKeyword) {
        Map<String, Map> stringMapMap = LionConfigHelper.getSearchKeyScene();
        if (MapUtils.isEmpty(stringMapMap) || StringUtils.isEmpty(searchKeyword)) {
            return;
        }
        Map<String, List<String>> searchKeyScene = stringMapMap.get(ShelfRequestAttrMapKeys.SEARCH_KEY_SCENE.getKey());
        if (MapUtils.isEmpty(searchKeyScene)) {
            return;
        }
        for (Map.Entry<String, List<String>> entry : searchKeyScene.entrySet()) {
            String key = entry.getKey();
            List<String> value = entry.getValue();
            if(CollectionUtils.isEmpty(value) || StringUtils.isEmpty(key)){
                return;
            }
            String searchLowerCase = searchKeyword.toLowerCase();
            boolean isContains = value.stream().map(String::toLowerCase).anyMatch(searchLowerCase::equals);
            if (isContains) {
                attrMap.put(ShelfRequestAttrMapKeys.SEARCH_KEY_SCENE.getKey(), key);
                break;
            }
        }
    }

    private void addDealAndSpuTopIds(ActivityContext ctx, Map<String, String> attrMap) {
        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
        List<Integer> dealIds = ParamUtil.getAggregateTopDealIds(ctx);
        List<Integer> spuIds = ParamUtil.getSummarySpuIds(summaryProductIds, null);
        if (CollectionUtils.isNotEmpty(dealIds)) {
            attrMap.put(ShelfRequestAttrMapKeys.TOP_DEALIDS.getKey(), convertListAsStr(dealIds));
        }
        if (CollectionUtils.isNotEmpty(spuIds)) {
            attrMap.put(ShelfRequestAttrMapKeys.TOP_PRODUCTIDS.getKey(), convertListAsStr(spuIds));
        }
    }

    private void addShelfTopDisplayParam(ActivityContext ctx, Map<String, String> attrMap) {
        Map<String, Object> recallConfig = ctx.getParam(Params.recallConfig);
        if (MapUtils.isEmpty(recallConfig)) {
            return;
        }

        boolean duiTou = (boolean) ParamsUtil.getValue(recallConfig, QueryFetcher.Params.eduDuiTou, false);
        attrMap.put("eduDuiTou", String.valueOf(duiTou));

        List<DouHuM> douHuMList = ctx.getParam(ShelfActivityConstants.Params.douHus);
        Map<String, String> douHuSk2topDisplayType = (Map<String, String>) ParamsUtil.getValue(recallConfig, Params.douHuSk2topDisplayType, new HashMap<>());
        if (MapUtils.isEmpty(douHuSk2topDisplayType)) {
            return;
        }
        Optional<String> topDisplayType = (Optional<String>) DouHuUtils.getConfigByDouHu(douHuMList, douHuSk2topDisplayType);
        if (topDisplayType.isPresent() && StringUtils.isNotEmpty(topDisplayType.get())) {
            attrMap.put("duitouType", topDisplayType.get());
        }
    }

    private void addZdcTagMatchParam(ActivityContext ctx, Map<String, String> attrMap) {
        if (Objects.nonNull(ctx.getParam(ShelfActivityConstants.Params.matchZdcTag))
                && (boolean) ctx.getParam(ShelfActivityConstants.Params.matchZdcTag)) {
            attrMap.put("PoiZdcTagMatch", "true");
        }
    }

    public void addSpringFestivalNoClose(ActivityContext ctx, Map<String, String> attrMap){
        if (Objects.nonNull(ctx.getParam(ShelfActivityConstants.Params.springFestivalNoClose))
                && (boolean) ctx.getParam(ShelfActivityConstants.Params.springFestivalNoClose)) {
            attrMap.put("isHolidayClosedShop", "true");
        }
    }

    public void addSearchItemParam(ActivityContext ctx, Map<String, String> attrMap) {
        String searchTerm = ctx.getParam(ShelfActivityConstants.Params.searchterm);
        if (StringUtils.isNotEmpty(searchTerm)) {
            attrMap.put("searchItemTitle", searchTerm);
        }
    }

    private String convertListAsStr(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<String> topIdsStr = ids.stream().map(productId -> String.valueOf(productId)).collect(Collectors.toList());
        return String.join(",", topIdsStr);
    }

    private boolean hitBrandKeyWord(ActivityContext ctx) {
        String searchKeyword = ctx.getParam(ShelfActivityConstants.Params.keyword);
        CompletableFuture<Boolean> isBrandKeyWordFuture = ctx.getParam(ShelfActivityConstants.Params.isBrandKeyWord);
        if (StringUtils.isEmpty(searchKeyword) || isBrandKeyWordFuture == null) {
            return false;
        }
        Boolean isBrandKeyWord = isBrandKeyWordFuture.join();
        if (isBrandKeyWord == null || !isBrandKeyWord) {
            return false;
        }
        return true;
    }

    private long getUserId(ActivityContext activityContext, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.mtUserId) + "", 0);
        }
        return NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.dpUserId) + "", 0);
    }

    private int getCityId(ActivityContext activityContext, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.mtCityId) + "", 0);
        }
        return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.dpCityId) + "", 0);
    }

    private CompletableFuture<FilterProductGroups> getProductGroupComponent(ShelfNavTabProductRequest request, String sceneCode) {
        if (!validateQueryParam(request)) {
            LogUtils.recordKeyMsg(request.getUserId(),"multiGetProductList-invalidRequest",request);
            return CompletableFuture.completedFuture(null);
        }
        long shopId = Platform.MT.getSource() == request.getPlatform() ? request.getMtPoiid() : request.getDpPoiid();
        ShelfSceneCacheConfig sceneCacheConfig = ShelfSceneCacheConfig.getCacheConfigBySceneCode(sceneCode);
        //开关、灰度策略
        if (sceneCacheConfig == null || !useQueryCache(request, shopId, sceneCacheConfig)) {
            return atomFacadeService.multiGetProductList(request).thenApply(response -> convertToProductGroupsComponent(request.getUserId(), response));
        }
        CacheKey cacheKey = buildQueryCacheKey(request.getPlatform(), shopId, request.getShelfNavTagId());
        DataLoader<FilterProductGroups> dataLoader = key -> {
            if (key == null) {
                return CompletableFuture.completedFuture(null);
            }
            CompletableFuture<FilterProductGroups> productGroupsComponentFuture = atomFacadeService.multiGetProductList(buildCacheQueryRequest(request)).thenApply(response -> convertToProductGroupsComponent(request.getUserId(), response));
            productGroupsComponentFuture.thenAccept(filterProductGroups -> fixCacheConsistency(filterProductGroups, request.getPlatform(), shopId));
            return productGroupsComponentFuture;
        };
        CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);
        CompletableFuture<FilterProductGroups> productGroupCacheFuture = cacheClient.asyncGetReadThrough(cacheKey, queryCacheTypeReference, dataLoader, platformQueryCacheExpireTime + new Random().nextInt(300), platformQueryCacheRefreshTime);
        return productGroupCacheFuture.thenApply(filterProductGroups -> {
            cachePageFilter(filterProductGroups, request);
            return filterProductGroups;
        });
    }

    private void fixCacheConsistency(FilterProductGroups filterProductGroups, int platform, long shopId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.fixCacheConsistency(ShelfQueryFetcher$FilterProductGroups,int,long)");
        CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);
        if (isProductGroupEmpty(filterProductGroups)) {
            cacheClient.asyncDelete(buildFilterCacheKey(platform, shopId));
        }
    }

    private boolean isProductGroupEmpty(FilterProductGroups filterProductGroups) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.isProductGroupEmpty(ShelfQueryFetcher$FilterProductGroups)");
        if (filterProductGroups == null || CollectionUtils.isEmpty(filterProductGroups.getGroups())) {
            return true;
        }
        if (filterProductGroups.getGroups().stream().allMatch(group -> CollectionUtils.isEmpty(group.getProducts()))) {
            return true;
        }
        return false;
    }

    private void cachePageFilter(FilterProductGroups filterProductGroups, ShelfNavTabProductRequest request) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.cachePageFilter(ShelfQueryFetcher$FilterProductGroups,ShelfNavTabProductRequest)");
        if (filterProductGroups == null || CollectionUtils.isEmpty(filterProductGroups.getGroups())) {
            return;
        }
        for (ProductGroup productGroup : filterProductGroups.getGroups()) {
            if (CollectionUtils.isEmpty(productGroup.getProducts())) {
                continue;
            }
            productGroup.setProducts(productGroup.getProducts().stream().skip(request.getPageSize() * (request.getPageNo() - 1)).limit(request.getPageSize()).collect(Collectors.toList()));
        }
    }

    private ShelfNavTabProductRequest buildCacheQueryRequest(ShelfNavTabProductRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.buildCacheQueryRequest(com.dianping.product.shelf.common.request.ShelfNavTabProductRequest)");
        ShelfNavTabProductRequest cacheQueryRequest = new ShelfNavTabProductRequest();
        BeanUtils.copyProperties(request, cacheQueryRequest);
        cacheQueryRequest.setAttrMap(Maps.newHashMap());
        cacheQueryRequest.setPageNo(1);
        cacheQueryRequest.setPageSize(queryCacheFirstScreenSize);
        return cacheQueryRequest;
    }

    private FilterProductGroups convertToProductGroupsComponent(long userId, Response<ShelfNavTabProductList> response) {
        LogUtils.recordKeyMsg(userId, "queryShelfNavTabProductListEnd", response);
        if (response == null || !response.isSuccess()) {
            return null;
        }
        FilterProductGroups filterProductGroups = new FilterProductGroups();
        List<ProductGroup> productGroups = Optional.of(response)
                .filter(Response::isSuccess)
                .map(Response::getContent)
                .map(ShelfNavTabProductList::getShelfNavProductComponentList)
                .filter(CollectionUtils::isNotEmpty)
                .map(productComponentList -> productComponentList
                        .stream()
                        .filter(Objects::nonNull)
                        .map(this::convertToProductGroup)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        filterProductGroups.setGroups(productGroups);
        Map<String, String> attrMap = Optional.of(response)
                .filter(Response::isSuccess)
                .map(Response::getContent)
                .map(ShelfNavTabProductList::getAttrMap)
                .orElse(Maps.newHashMap());
        filterProductGroups.setAttrs(attrMap);
        filterProductGroups.setShelfNavTagId(Optional.ofNullable(response.getContent())
                .map(ShelfNavTabProductList::getShelfNavTagId)
                .orElse(0L));
        return filterProductGroups;
    }

    private ProductGroup convertToProductGroup(ShelfNavProductComponent navProductComponent) {
        ProductGroup productGroup = new ProductGroup();
        if (CollectionUtils.isEmpty(navProductComponent.getProductIds())) {
            return productGroup;
        }
        productGroup.setGroupId(navProductComponent.getId());
        productGroup.setName(getProductGroupName(navProductComponent));
        productGroup.setProducts(buildProducts(navProductComponent.getProductIds()));
        productGroup.setProductHierarchyRoot(buildProductHierarchy(navProductComponent.getProductIds()));
        productGroup.setTotalCount(navProductComponent.getTotalCount());
        productGroup.setAttrs(buildAttributes(navProductComponent));
        return productGroup;
    }

    /**
     * @param component
     * @return 获取货架的组名，优先取业务定制名称（如浴资票），其次取原始的商品类型组名（如团购）
     */
    private String getProductGroupName(ShelfNavProductComponent component) {
        if (StringUtils.isNotEmpty(component.getBusinessName())) {
            return component.getBusinessName();
        }
        return component.getName();
    }

    private List<Product> buildProducts(List<ProductBaseInfo> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        List<Product> flatProducts = Lists.newArrayList();
        //支持dealgroup、spu-dealgroup或dealgroup-sku结构
        for (ProductBaseInfo productBaseInfo : productIds) {
            if(productBaseInfo.getProductType() == ProductType.SPT_SPU.getType()){
                List<Product> childProducts = Optional.ofNullable(productBaseInfo.getChildProductList()).orElse(Lists.newArrayList())
                        .stream().map(product -> {
                            Product item = convertToProduct(product);
                            item.setSptSpuId(productBaseInfo.getProductId());
                            return item;
                        }).collect(Collectors.toList());
                flatProducts.addAll(childProducts);
            }else {
                Product product = convertToProduct(productBaseInfo);
                flatProducts.add(product);
                flatProducts.addAll(buildChildProducts(productBaseInfo.getChildProductList(), product));
            }
        }
        return flatProducts;
    }

    private List<Product> buildChildProducts(List<ProductBaseInfo> productBaseInfos, Product parent){
        if(CollectionUtils.isEmpty(productBaseInfos)){
            return Lists.newArrayList();
        }
        List<Product> childProducts = Lists.newArrayList();
        List<String> skuIds = Lists.newArrayList();
        for(ProductBaseInfo productBaseInfo : productBaseInfos){
            if(productBaseInfo.getProductType() == ProductType.SKU.getType()){
                skuIds.add(productBaseInfo.getProductId());
            }else{
                childProducts.add(convertToProduct(productBaseInfo));
            }
        }
        parent.setSkuIdList(skuIds);
        return childProducts;
    }

    /**
     * 构造ProductGroup attr属性
     */
    private Map<String, String> buildAttributes(ShelfNavProductComponent navProductComponent) {
        if (CollectionUtils.isEmpty(navProductComponent.getAdditionalInfo())) {
            return null;
        }
        return navProductComponent.getAdditionalInfo().stream().collect(Collectors.toMap(NavAdditionalInfo::getName, NavAdditionalInfo::getValue));
    }

    private Product convertToProduct(ProductBaseInfo productBaseInfo) {
        Product product = new Product();
        product.setProductId(Integer.parseInt(productBaseInfo.getProductId()));
        product.setProductType(ProductTypeEnum.getTypeByOriginType(productBaseInfo.getProductType()));
        product.setRecommend(isRecommend(productBaseInfo));
        product.setTopDisplay(isTopDisplay(productBaseInfo));
        product.setExtraMap(productBaseInfo.getExtraMap());
        parseShelfItem(productBaseInfo, product);
        return product;
    }

    public void parseShelfItem(ProductBaseInfo productBaseInfo, Product product) {
        try {
            if (CollectionUtils.isNotEmpty(productBaseInfo.getShelfItemDataList())) {
                List<String> skuIdList = Collections.emptyList();
                Long spuId = null;
                Long lowPriceSkuId = null;
                for (ShelfItemDataDTO shelfItemDataDTO : productBaseInfo.getShelfItemDataList()) {
                    if (ShelfItemDataNameEnum.SKU_INFO.getDataName().equals(shelfItemDataDTO.getDataName())) {
                        List<SkuDTO> skuDTOList = ShelfItemDataDtoGsonUtil.fromJsonDataValue(shelfItemDataDTO);
                        if (CollectionUtils.isNotEmpty(skuDTOList)) {
                            skuIdList = skuDTOList.stream().map(t -> t.getSkuId()).collect(Collectors.toList());
                        }
                    }
                    if(ShelfItemDataNameEnum.SPU_ID.getDataName().equals(shelfItemDataDTO.getDataName())){
                        spuId = ShelfItemDataDtoGsonUtil.fromJsonDataValue(shelfItemDataDTO);
                    }
                    if(ShelfItemDataNameEnum.LOW_PRICE_SKU_ID.getDataName().equals(shelfItemDataDTO.getDataName())){
                        lowPriceSkuId = ShelfItemDataDtoGsonUtil.fromJsonDataValue(shelfItemDataDTO);
                    }
                }
                if (CollectionUtils.isNotEmpty(skuIdList)) {
                    product.setSkuIdList(skuIdList);
                }
                if (spuId != null && StringUtils.isEmpty(product.getSptSpuId())){
                    product.setSptSpuId(String.valueOf(spuId));
                }
                if (lowPriceSkuId != null){
                    product.setLowPriceSkuId(lowPriceSkuId);
                }
            }
            if (CollectionUtils.isNotEmpty(productBaseInfo.getShelfItemUnitList())) {
                product.setItemUnitList(productBaseInfo.getShelfItemUnitList().stream().map(t -> {
                    ItemUnit itemUnit = new ItemUnit();
                    itemUnit.setItemUnitId(t.getItemUnitId());
                    itemUnit.setItemUnitType(t.getItemUnitType());
                    return itemUnit;
                }).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            Cat.logError("parseShelfItemErr", e);
        }
    }

    private boolean isRecommend(ProductBaseInfo productBaseInfo) {
        if (productBaseInfo == null) {
            return false;
        }
        if (productBaseInfo instanceof ShelfProduct) {
            ShelfProduct shelfProduct = (ShelfProduct) productBaseInfo;
            return shelfProduct.isTopProduct();
        }
        return false;
    }

    private boolean isTopDisplay(ProductBaseInfo productBaseInfo) {
        if (productBaseInfo == null) {
            return false;
        }
        //先从扩展字段中取，取不到再走后面的逻辑
        boolean hasTopDisplayFromExt = hasTopDisplayFromExt(productBaseInfo.getExtraMap());
        if (hasTopDisplayFromExt) {
            return true;
        }

        if (productBaseInfo instanceof ShelfProduct) {
            ShelfProduct shelfProduct = (ShelfProduct) productBaseInfo;
            if (CollectionUtils.isEmpty(shelfProduct.getAdditionalInfo())) {
                return false;
            }
            return shelfProduct.getAdditionalInfo().contains(TOP_DISPLAY_ADDITIONAL_KEY);
        }
        return false;
    }

    private boolean hasTopDisplayFromExt(Map<String, String> extraMap) {
        if (MapUtils.isEmpty(extraMap)) {
            return false;
        }
        return (extraMap.containsKey(TOP_DISPLAY_EXT_EDU_KEY)
                && Boolean.parseBoolean(extraMap.get(TOP_DISPLAY_EXT_EDU_KEY)))
                || (extraMap.containsKey(TOP_DISPLAY_EXT_EDU_CATE_KEY)
                && Boolean.parseBoolean(extraMap.get(TOP_DISPLAY_EXT_EDU_CATE_KEY)))
                || (extraMap.containsKey(TOP_DISPLAY_EXT_MEDICAL_KEY)
                && Boolean.parseBoolean(extraMap.get(TOP_DISPLAY_EXT_MEDICAL_KEY)))
                || (extraMap.containsKey(TOP_DISPLAY_ADDITIONAL_KEY)
                && Boolean.parseBoolean(extraMap.get(TOP_DISPLAY_ADDITIONAL_KEY)))
                || (extraMap.containsKey(TOP_DISPLAY_EXT_NURSING_ROOM_KEY)
                && Boolean.parseBoolean(extraMap.get(TOP_DISPLAY_EXT_NURSING_ROOM_KEY)))
                ;
    }

    private boolean useQueryCache(ShelfNavTabProductRequest request, long shopId, ShelfSceneCacheConfig sceneCacheConfig) {
        if (!sceneCacheConfig.isUseQueryCache()) {
            return false;
        }
        if (queryCacheFirstScreenSize < sceneCacheConfig.getQueryUseSize()) {
            return false;
        }
        int queryMaxSize = request.getPageSize() * request.getPageNo();
        if (queryMaxSize > sceneCacheConfig.getQueryUseSize()) {
            return false;
        }
        return queryCacheSwitch && isGrey(shopId);
    }


    /**
     * 灰度策略
     *
     * @param shopId 当次查询门店ID
     * @return
     */
    private boolean isGrey(long shopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.isGrey(long)");
        if (queryGreyConfig == null) {
            return true;
        }
        return queryGreyConfig.isGrey(shopId);
    }


    private boolean validateQueryParam(ShelfNavTabProductRequest request) {
        if (request == null) {
            return false;
        }
        if (Platform.of(request.getPlatform()) == null) {
            return false;
        }
        if (request.getDpPoiid() <= 0 && request.getMtPoiid() <= 0 && request.getShelfType() != ShelfTypeEnum.FLAGSHIP_STORE_SHELF) {
            return false;
        }
        if (request.getShelfNavTagId() <= 0 && request.getNavRouterType() == null) {
            return false;
        }
        return true;
    }

    private CacheKey buildQueryCacheKey(int platform, long shopId, long filterId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.buildQueryCacheKey(int,long,long)");
        return new CacheKey(REDIS_QUERY_CATEGORY, platform, shopId, filterId);
    }

    private CacheKey buildFilterCacheKey(int platform, long shopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.buildFilterCacheKey(int,long)");
        return new CacheKey(REDIS_FILTER_CATEGORY, platform, shopId);
    }

    public static String buildExpsOfShelfRequest(ActivityContext activityContext) {
        //当前支持使用 PMF 框架斗斛，Tpl 框架的 PlatformMultiDouHuFetcher，PlatformDouHuFetcher
        List<DouHuM> douHuMList = activityContext.getParam(ShelfActivityConstants.Params.douHus);
        if (CollectionUtils.isNotEmpty(douHuMList)) {
            List<String> sks = douHuMList.stream().map(DouHuM::getSk).filter(Objects::nonNull).collect(Collectors.toList());
            return String.join(",", sks);
        }
        return null;
    }

    public void paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(ShelfNavTabProductRequest shelfRequest, ActivityContext activityContext) {
        if (!isFlagshipStoreReq(activityContext.getSceneCode(), activityContext.getParam(ShelfActivityConstants.Params.spaceKey)) || shelfRequest == null) {
            return;
        }
        shelfRequest.setShelfType(ShelfTypeEnum.FLAGSHIP_STORE_SHELF);
        shelfRequest.setStoreId(getFlagshipStoreIdByStoreUUID(activityContext.getParam(ShelfActivityConstants.Params.entityId), activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore)));
        shelfRequest.setPageNo(activityContext.getParam(ShelfActivityConstants.Params.pageNo));
        shelfRequest.setPageSize(activityContext.getParam(ShelfActivityConstants.Params.pageSize));
    }

    /**
     * @param sceneCode
     * @return 是否旗舰店货架： true - 是
     */
    public static boolean isFlagshipStoreReq(String sceneCode, String spaceKey) {
        return FLAGSHIP_STORE_SHELF_SCENE.contains(sceneCode) || Objects.equals(spaceKey, DealFilterListActivity.SpaceKey.FLAGSHIP_STORE_SHELF);
    }

    private Long getFlagshipStoreIdByStoreUUID(String storeUUID, FlagshipStoreM flagshipStoreM) {
        if (flagshipStoreM != null && flagshipStoreM.getStoreId() > 0) {
            return flagshipStoreM.getStoreId();
        }
        if (StringUtils.isEmpty(storeUUID)) {
            return 0L;
        }
        try {
            return atomFacadeService.getStoreIdByUUID(storeUUID).get(200, TimeUnit.MILLISECONDS);
        } catch (Exception ex) {
            Cat.logError(ex);
            return 0L;
        }
    }

    public void paddingFilterOptionParam(ShelfNavTabProductRequest shelfRequest, ActivityContext activityContext) {
        String filterParams = activityContext.getParam(ShelfActivityConstants.Params.selectedFilterParams);
        if (StringUtils.isEmpty(filterParams)) {
            return;
        }
        List<String> filterParamList = Arrays.stream(filterParams.split(FILTER_PARAM_SEPARATOR)).distinct().collect(Collectors.toList());
        long navTagId = NumberUtils.toLong(StringUtils.substringBefore(filterParamList.get(0), FILTER_PARAM_NAV_SEPARATOR));
        //取筛选参数关联的导航tag，设置选中导航tag
        if (navTagId > 0) {
            shelfRequest.setShelfNavTagId(navTagId);
            activityContext.addParam(ShelfActivityConstants.Params.selectedFilterId, navTagId);
        }
        List<NavTagOption> navTagOptionList = filterParamList.stream()
                .map(this::buildNavTagOption)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        shelfRequest.setNavTagOptionList(navTagOptionList);
    }

    private NavTagOption buildNavTagOption(String filterParam) {
        String identityName = StringUtils.substringAfter(filterParam, FILTER_PARAM_NAV_SEPARATOR);
        if (StringUtils.isEmpty(identityName)) {
            return null;
        }
        NavTagOption option = new NavTagOption();
        OptionIdentity optionIdentity = new OptionIdentity();
        optionIdentity.setIdentityName(identityName);
        option.setOptionIdentity(optionIdentity);
        return option;
    }

    private void paddingNavRouterRequest(ShelfNavTabProductRequest shelfRequest, ActivityContext activityContext){
        //有导航id,则无需锚定路由
        if(shelfRequest.getShelfNavTagId() > 0){
            //标记下首屏请求
            String requestType = activityContext.getParam(ShelfActivityConstants.Params.requestType);
            if(RequestTypeEnum.API_DEAL_SHELF.getType().equals(requestType)
                || RequestTypeEnum.API_UNIFIED_SHELF.getType().equals(requestType)){
                shelfRequest.setNavRouterType(NavRouterTypeEnum.DEAL_NAV_NAME_TYPE_BIZ.getType());
            }
            return;
        }
        ProductAnchorInfo productAnchorInfo = activityContext.getParam(Params.productAnchorInfo);
        if(productAnchorInfo == null){
            return;
        }
        shelfRequest.setNavRouterType(productAnchorInfo.getRouterType());
        if(productAnchorInfo.getRouterType() == NavRouterTypeEnum.DEAL_NAV_NAME_TYPE.getType()
            || productAnchorInfo.getRouterType() == NavRouterTypeEnum.DEAL_SEARCH_WORD_TYPE.getType()
            || productAnchorInfo.getRouterType() ==NavRouterTypeEnum.DEAL_RECOGNIZE_INTENTION_TYPE.getType()){
            shelfRequest.getAttrMap().put("searchWord", productAnchorInfo.getMatchKey());
        }
    }

    private void addBookShelfParam(ActivityContext ctx, Map<String, String> attrMap) {
        Map<String, Object> recallConfig = ctx.getParam(Params.recallConfig);
        if (MapUtils.isEmpty(recallConfig)) {
            return;
        }
        String isBook = (String) ParamsUtil.getValue(recallConfig, Params.isBook, null);
        attrMap.put("isBook", isBook);
    }

    /**
     * 平台召回筛选缓存召回对象
     *
     * <AUTHOR>
     */
    @Data
    static class Product implements Serializable {

        /**
         * 商品ID
         */
        private int productId;
        private boolean isRecommend;
        private int productType;
        //是否堆头商品
        private boolean isTopDisplay;
        //扩展字段
        private Map<String, String> extraMap;
        /**
         * 商品关联的skuIds
         */
        private List<String> skuIdList;
        /**
         * 单元Item
         */
        private List<ItemUnit> itemUnitList;
        /**
         * 商品关联标准spuId
         */
        private String sptSpuId;
        /**
         * 商品的最低价格skuid
         */
        private Long lowPriceSkuId;
    }

    /**
     * 项目单元
     */
    @Data
    public static class ItemUnit implements Serializable {
        /**
         * 单元类型
         *
         * @see com.dianping.product.shelf.common.enums.ShelfItemUnitTypeEnum
         */
        private int itemUnitType;
        private String itemUnitId;
    }

    /**
     * 平台召回商品组缓存
     */
    @Data
    private static class ProductGroup implements Serializable {

        /**
         * 商品组ID
         */
        private int groupId;

        /**
         * 商品组名称
         * 逻辑：从 ShelfNavProductComponent 取值，优先取业务定制名称（如浴资票），其次取原始的商品类型组名（如团购）
         */
        private String name;

        /**
         * 商品列表
         */
        private List<Product> products;

        /**
         * 商品层次结构
         */
        private ProductHierarchyNodeM productHierarchyRoot;

        /**
         * 该组商品大小
         */
        private int totalCount;

        /**
         * 货架属性
         */
        private Map<String, String> attrs;
    }

    @Data
    private static class FilterProductGroups implements Serializable {


        /**
         * 商品组列表
         */
        private List<ProductGroup> groups;

        /**
         * 货架属性
         */
        private Map<String, String> attrs;

        /**
         * 商品所属导航id
         */
        private long shelfNavTagId;
    }
}