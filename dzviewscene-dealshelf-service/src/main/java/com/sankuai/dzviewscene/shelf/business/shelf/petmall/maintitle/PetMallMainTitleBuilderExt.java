package com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;

import java.util.List;


@ExtPointInstance(name = "宠物电商货架主标题构造扩展点")
public class PetMallMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    /**
     * 门店权益icon
     */
    private static String SHOP_RIGHT_ICON = "https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png";

    @Override
    public String jumpUrl(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle.PetMallMainTitleBuilderExt.jumpUrl(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle.PetMallMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return "本店商品";
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle.PetMallMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle.PetMallMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<IconRichLabelVO> richLabels = Lists.newArrayList();
        richLabels.add(buildRichLabelVO(SHOP_RIGHT_ICON,"可自提"));
        richLabels.add(buildRichLabelVO(SHOP_RIGHT_ICON,"可配送"));
        return richLabels;
    }

    private IconRichLabelVO buildRichLabelVO(String icon,String text){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.maintitle.PetMallMainTitleBuilderExt.buildRichLabelVO(java.lang.String,java.lang.String)");
        IconRichLabelVO richLabelVO = new IconRichLabelVO();
        richLabelVO.setIcon(SHOP_RIGHT_ICON);
        richLabelVO.setText(new RichLabelVO(11, "#777777", text));
        return richLabelVO;
    }
}
