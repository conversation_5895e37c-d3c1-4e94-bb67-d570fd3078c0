package com.sankuai.dzviewscene.shelf.platform.common.model;

import lombok.Data;

import java.util.List;

/**
 * 上下文处理器-处理结果
 *
 * <AUTHOR>
 * @since 2024/1/9 15:20
 */
@Data
public class ContextHandlerResult {
    /**
     * 门店是否有门店维度的买贵必赔标签
     */
    private boolean shopHasShopGuaranteeTag;

    /**
     * 门店预约数
     */
    private int shopReservationCount;

    /**
     * 门店回头客推荐语
     */
    private List<String> shopCustomerRecommendWord;

    /**
     * 搜索意图解析
     */
    private List<String> recognizeList;

    /**
     * 家政保洁商家时效 秒
     */
    private long avgAcceptOderTime;

    /**
     * EXPOSED用户可先用后付
     * EXPOSED：建议曝光，UNEXPOSED：建议不曝光
     */
    private String exposure;
}
