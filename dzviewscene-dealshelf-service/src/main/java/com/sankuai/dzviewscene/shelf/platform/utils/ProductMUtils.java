package com.sankuai.dzviewscene.shelf.platform.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.product.shelf.common.enums.ProductType;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.dztheme.deal.res.structured.DealDetailStructuredDTO;
import com.sankuai.dztheme.deal.res.structured.DealSkuItemDTO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date :2023/8/3
 */
@Slf4j
public class ProductMUtils {

    private static ExecutorService diffExecutorService = ExecutorServices.forThreadPoolExecutor("diffProductM", 50, 100, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024));

    private static CopyOptions copyOption = CopyOptions.create().setIgnoreNullValue(true);

    public static void copyProductM(ProductM productM, ProductM preProductM) {
        mergeProductMAttrs(productM, preProductM);
        BeanUtil.copyProperties(preProductM, productM, copyOption);
    }

    public static void asyncDiffPaddingPreHandler(ActivityContext activityContext, ProductGroupM productGroupM, int productType) {
        try {
            if (CollectionUtils.isEmpty(productGroupM.getProducts())) {
                return;
            }
            List<ProductM> preProducts = productGroupM.getPreLoadProducts() != null ? Optional.ofNullable(productGroupM.getPreLoadProducts().get(productType))
                    .orElse(Lists.newArrayList()) : Lists.newArrayList();
            if (CollectionUtils.isEmpty(preProducts) || !needDiffProduct(activityContext, productType)) {
                return;
            }
            Map<Integer, ProductM> productMap = productGroupM.getProducts().stream()
                    .collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), productM), HashMap::putAll);
            preProducts.stream().forEach(preProductM -> {
                ProductM productM = productMap.get(preProductM.getProductId());
                mergeProductMAttrs(productM, preProductM);
            });
        } catch (Exception e) {
            Cat.logError("asyncDiffPaddingPreHandler", e);
        }
    }

    public static void asyncDiffPrePadding(ActivityContext activityContext, ProductGroupM productGroupM, int productType) {
        try {
            if (CollectionUtils.isEmpty(productGroupM.getProducts())) {
                return;
            }
            List<ProductM> preProducts = productGroupM.getPreLoadProducts() != null ? Optional.ofNullable(productGroupM.getPreLoadProducts().get(productType))
                    .orElse(Lists.newArrayList()) : Lists.newArrayList();
            if (CollectionUtils.isEmpty(preProducts) || !needDiffProduct(activityContext, productType)) {
                return;
            }
            diffExecutorService.submit(() -> diffProductMListByType(activityContext, productGroupM.getProducts(), preProducts, productType));
        } catch (Exception e) {
            Cat.logError("asyncDiffPrePadding", e);
        }
    }

    public static boolean enableProductDiff(ActivityContext activityContext, int productType) {
        List<Integer> enablePaddingDiffProductTypes = ParamsUtil.getValue(activityContext, PaddingFetcher.Params.enablePaddingDiffProductTypes, new ArrayList<Integer>());
        return CollectionUtils.isNotEmpty(enablePaddingDiffProductTypes) && enablePaddingDiffProductTypes.contains(productType);
    }

    private static void diffProductMListByType(ActivityContext activityContext, List<ProductM> products, List<ProductM> preProducts, int productType) {
        try {
            Cat.logEvent("Diff.Padding", "productType#" + productType + ".diff");
            if (products.size() != preProducts.size()) {
                Cat.logEvent("Diff.Padding", "productType#" + productType + ".diff.size.not.equal");
            }
            Map<Integer, ProductM> preProductMap = preProducts.stream()
                    .collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), productM), HashMap::putAll);
            ProductM diffOriProductM = null;
            ProductM diffPreProductM = null;
            String compareMsg = null;
            for (ProductM productM : products) {
                ProductM preProductM = preProductMap.get(productM.getProductId());
                if (preProductM == null) {
                    Cat.logEvent("Diff.Padding", "productType#" + productType + "diff.pre.null");
                    diffOriProductM = productM;
                    break;
                }
                compareMsg = ProductMUtils.compareProductM(productM, preProductM);
                if (StringUtils.isNotEmpty(compareMsg)) {
                    Cat.logEvent("Diff.Padding", "productType#" + productType + ".diff.fail");
                    Cat.logEvent("Diff.Padding.Msg", "productType#" + productType + ":" + compareMsg);
                    diffOriProductM = productM;
                    diffPreProductM = preProductM;
                    break;
                }
            }
            if (diffOriProductM != null) {
                logDiffInfoByProductType(activityContext, compareMsg, diffOriProductM, diffPreProductM, productType);
            }
        } catch (Exception e) {
            Cat.logEvent("Diff.Padding", "productType#" + productType + ".diff.error");
            log.error("diff异常", e);
        }
    }

    private static void logDiffInfoByProductType(ActivityContext activityContext, String compareMsg, ProductM diffOriProductM, ProductM diffPreProductM, int productType) {
        Map<Integer, Integer> diffLogFlowMap = (Map<Integer, Integer>) ParamsUtil.getValue(activityContext, PaddingFetcher.Params.productType2DiffLogFlowMap, new HashMap<Integer, Integer>());
        int diffLogFlow = MapUtils.isNotEmpty(diffLogFlowMap) && diffLogFlowMap.containsKey(productType) ? diffLogFlowMap.get(productType) : 0;
        long dpShopId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL);
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        if (diffLogFlow <= 0) {
            return;
        }
        if (System.currentTimeMillis() % diffLogFlow == 0) {
            log.info(XMDLogFormat.build()
                    .putTag("method", "productType" + productType + ".logDiffInfo")
                    .message(String.format(ProductType.of(productType).getDesc() + "预填充结果不一致，compareMsg:%s, dpShopId:%s, platform:%s, origin:%s, pre:%s",
                            compareMsg, dpShopId, platform, JsonCodec.encode(diffOriProductM), JsonCodec.encode(diffPreProductM))));
        }
    }

    private static boolean needDiffProduct(ActivityContext activityContext, int productType) {
        //兼容下之前的
        return enableProductDiff(activityContext, productType) || (productType == ProductTypeEnum.DEAL.getType() && ParamsUtil.getBooleanSafely(activityContext, PaddingFetcher.Params.enablePaddingDiff));
    }

    public static String compareProductM(ProductM productM, ProductM preProductM) {
        try {
            // 1. 团单ID
            compareNum(productM.getProductId(), preProductM.getProductId(), "productId");
            // 2. 团单标题
            compareString(productM.getTitle(), preProductM.getTitle(), "title");
            // 3. 团单详情跳转URL
            // 4. 团单销量
            compareObject(productM.getSale(), preProductM.getSale(), "sale");
            // 5. 团单售卖价格标签
            compareString(productM.getBasePriceTag(), preProductM.getBasePriceTag(), "basePriceTag");
            // 7. 团单市场价格
            compareString(productM.getMarketPrice(), preProductM.getMarketPrice(), "marketPrice");
            // 8. 融合优惠
            compareList(productM.getPromoPrices(), preProductM.getPromoPrices(), "promoPrices");
            // 9. 团单购买信息
            compareString(productM.getPurchase(), preProductM.getPurchase(), "purchase");
            // 10. 团单拼团标签
            compareObject(productM.getPinPrice(), preProductM.getPinPrice(), "pinPrice");
            // 11. 团单次卡标签
            compareObject(productM.getCardPrice(), preProductM.getCardPrice(), "cardPrice");
            // 12. 团单跳转链接
            // 13. 团单标准商品标签
            compareList(productM.getProductTags(), preProductM.getProductTags(), "productTags");
            // 13. 团单商品头图
            compareString(productM.getPicUrl(), preProductM.getPicUrl(), "picUrl");
            // 14. 团单扩展属性
            compareList(productM.getExtAttrs(), preProductM.getExtAttrs(), "extAttrs");
            // 15. 关联商户信息
            compareList(productM.getShopMs(), preProductM.getShopMs(), "shopMs");
            // 16、消费返券
            compareList(productM.getCoupons(), preProductM.getCoupons(), "coupons");
            //17.商品原始售卖价格
            compareBigDecimal(productM.getBasePrice(), preProductM.getBasePrice(), "basePrice");
            //18.商品类目
            compareNum(productM.getCategoryId(), preProductM.getCategoryId(), "categoryId");
            compareString(productM.getCategoryName(), preProductM.getCategoryName(), "categoryName");
            //19.商品活动
            compareList(productM.getActivities(), preProductM.getActivities(), "activities");
            //20.团单售卖价格描述
            compareString(productM.getBasePriceDesc(), preProductM.getBasePriceDesc(), "basePriceDesc");
            //21.库存
            compareObject(productM.getStock(), preProductM.getStock(), "stock");
            //22.标签列表
            compareList(productM.getProductTagList(), preProductM.getProductTagList(), "productTagList");
            compareNum(productM.getBeginDate(), preProductM.getBeginDate(), "beginDate");
            compareNum(productM.getEndDate(), preProductM.getEndDate(), "endDate");
            //24.下单链接
            // 29. 团单交易类型
            compareNum(productM.getTradeType(), preProductM.getTradeType(), "tradeType");
            // 31. 团单加项列表
            compareList(productM.getAdditionalProjectList(), preProductM.getAdditionalProjectList(), "additionalProjectList");
            // 32. 团单品牌名称
            compareString(productM.getBrandName(), preProductM.getBrandName(), "brandName");
        } catch (Exception e) {
            return e.getMessage();
        }
        return null;
    }

    public static void mergeProductMAttrs(ProductM productM, ProductM preProductM) {
        if (productM == null || preProductM == null || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return;
        }
        if (CollectionUtils.isEmpty(preProductM.getExtAttrs())) {
            preProductM.setExtAttrs(Lists.newArrayList());
        }
        preProductM.getExtAttrs().addAll(0, productM.getExtAttrs());
    }

    private static void compareNum(long i, long j, String field) {
        if (i != j) {
            throw new RuntimeException(field + " not equal");
        }
    }

    private static void compareString(String i, String j, String field) {
        if (!StringUtils.equals(i, j)) {
            throw new RuntimeException(field + " not equal");
        }
    }

    private static void compareBigDecimal(BigDecimal i, BigDecimal j, String field) {
        if (i == null && j == null) {
            return;
        }
        if (i == null || j == null || i.compareTo(j) != 0) {
            throw new RuntimeException(field + " not equal");
        }
    }

    private static void compareList(List<?> list1, List<?> list2, String field) {
        if (list1 == null && list2 == null) {
            return;
        }
        if (list1 == null || list2 == null || list1.size() != list2.size()) {
            throw new RuntimeException(field + " not equal");
        }
        JSONArray jsonArray1 = JSON.parseArray(JSON.toJSONString(list1));
        JSONArray jsonArray2 = JSON.parseArray(JSON.toJSONString(list2));
        compareJsonArray(jsonArray1, jsonArray2, field);
    }

    private static void compareObject(Object ob1, Object ob2, String field) {
        if (ob1 == null && ob2 == null) {
            return;
        }
        if (ob1 == null || ob2 == null) {
            throw new RuntimeException(field + " not equal");
        }
        JSONObject jsonObject1 = JSON.parseObject(JSON.toJSONString(ob1));
        JSONObject jsonObject2 = JSON.parseObject(JSON.toJSONString(ob2));
        compareJsonObject(jsonObject1, jsonObject2, field);
    }

    /**
     * 比较两个jsonObject对象是否相等
     *
     * @param ob1
     * @param ob2
     */
    private static void compareJsonObject(JSONObject ob1, JSONObject ob2, String field) {
        if (ob1 == null && ob2 == null) {
            return;
        }
        if (ob1 == null || ob2 == null) {
            throw new RuntimeException(field + " not equal");
        }
        for (String key : ob1.keySet()) {
            Object value1 = ob1.get(key);
            Object value2 = ob2.get(key);

            if (value1 instanceof JSONObject) {
                compareJsonObject((JSONObject) value1, (JSONObject) value2, field);
            } else if (value1 instanceof JSONArray) {
                compareJsonArray((JSONArray) value1, (JSONArray) value2, field);
            } else {
                String str1 = String.valueOf(value1);
                String str2 = String.valueOf(value2);
                if (!StringUtils.equals(str1, str2)) {
                    throw new RuntimeException(field + " not equal");
                }
            }
        }
    }

    /**
     * 比较两个jsonArray列表是否相等
     *
     * @param jsonArray1
     * @param jsonArray2
     */
    private static void compareJsonArray(JSONArray jsonArray1, JSONArray jsonArray2, String field) {
        if (jsonArray1 == null && jsonArray2 == null) {
            return;
        }
        if (jsonArray1 == null || jsonArray2 == null || jsonArray1.size() != jsonArray2.size()) {
            throw new RuntimeException(field + " not equal");
        }
        for (int i = 0; i < jsonArray1.size(); i++) {
            Object value1 = jsonArray1.get(i);
            Object value2 = jsonArray2.get(i);

            if (value1 instanceof JSONObject) {
                compareJsonObject((JSONObject) value1, (JSONObject) value2, field);
            } else if (value1 instanceof JSONArray) {
                compareJsonArray((JSONArray) value1, (JSONArray) value2, field);
            } else {
                String str1 = String.valueOf(value1);
                String str2 = String.valueOf(value2);
                if (!StringUtils.equals(str1, str2)) {
                    throw new RuntimeException(field + " not equal");
                }
            }
        }
    }

    /**
     * 将source添加到result中，如果source中的元素在result中已经存在，则跳过
     *
     * @param result
     * @param source
     */
    public static void add2ResultWhenNotExist(List<ProductM> result, List<ProductM> source) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        for (ProductM productM : source) {
            if (findDuplicateProduct(result, productM)) {
                continue;
            }
            result.add(productM);
        }
    }

    private static boolean findDuplicateProduct(List<ProductM> result, ProductM productM) {
        if (CollectionUtils.isEmpty(result) || productM == null) {
            return false;
        }
        return result.stream().anyMatch(product -> product.getProductId() == productM.getProductId() && product.getProductType() == productM.getProductType());
    }

    public static List<DealSkuItemDTO> getAllDetailStructuredSkuItemDTOs(ProductM productM) {
        if (productM == null || productM.getDealDetailStructuredDTO() == null) {
            return Lists.newArrayList();
        }

        DealDetailStructuredDTO dealDetailStructuredDTO = productM.getDealDetailStructuredDTO();
        List<DealSkuItemDTO> result = Lists.newArrayList();

        addMustGroupSkuItems(dealDetailStructuredDTO, result);
        addOptionalGroupSkuItems(dealDetailStructuredDTO, result);

        return result;
    }

    private static void addMustGroupSkuItems(DealDetailStructuredDTO dealDetailStructuredDTO, List<DealSkuItemDTO> result) {
        if (CollectionUtils.isEmpty(dealDetailStructuredDTO.getMustGroups())) {
            return;
        }

        dealDetailStructuredDTO.getMustGroups().stream()
                .filter(group -> CollectionUtils.isNotEmpty(group.getSkuItems()))
                .forEach(group -> result.addAll(group.getSkuItems()));
    }

    private static void addOptionalGroupSkuItems(DealDetailStructuredDTO dealDetailStructuredDTO, List<DealSkuItemDTO> result) {
        if (CollectionUtils.isEmpty(dealDetailStructuredDTO.getOptionalGroups())) {
            return;
        }

        dealDetailStructuredDTO.getOptionalGroups().stream()
                .filter(group -> CollectionUtils.isNotEmpty(group.getSkuItems()))
                .forEach(group -> result.addAll(group.getSkuItems()));
    }
}
