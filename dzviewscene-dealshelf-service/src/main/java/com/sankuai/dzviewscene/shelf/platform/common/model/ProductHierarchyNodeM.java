package com.sankuai.dzviewscene.shelf.platform.common.model;

import lombok.Data;

import java.util.List;

@Data
public class ProductHierarchyNodeM {

    /**
     * 商品ID, 与平台返回商品id保持一致
     */
    private long productId;

    /**
     * 商品类型 {@link com.sankuai.dzviewscene.product.enums.ProductTypeEnum}
     */
    private int productType;

    /**
     * 父项节点
     */
    private ProductHierarchyNodeM parent;

    /**
     * 子项节点
     */
    private List<ProductHierarchyNodeM> children;

    public String getIdentityKey() {
        return identityKey(productType, productId);
    }

    public static String identityKey(int productType, long productId){
        return productType + "_" + productId;
    }

    public String toString(){
        return "ProductHierarchyNodeM{" +
                "productId=" + productId +
                ", productType='" + productType + '\'' +
                '}';
    }
}
