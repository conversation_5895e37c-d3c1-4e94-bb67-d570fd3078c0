package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ParamUtil {

    /**
     * 从上下文中获取商户的类目id
     */
    public static int getShopCategoryId(ActivityContext activityContext) {
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        return shopM == null ? 0 : shopM.getCategory();
    }

    /**
     * 从上下文中获取poiId
     * @param activityContext
     * @return
     */
    public static long getPoiId(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    public static long getPoiId(ActivityCxt activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    public static long getPoiIdL(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.ParamUtil.getPoiIdL(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    public static long getDpPoiId(ActivityCxt activityContext) {
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    /**
     * 从上下文中获取cityId
     * @param activityContext
     * @return
     */
    public static int getCityId(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) {
            return Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        }
        return Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
    }

    /**
     * 解析 summaryProductIds、anchorGoodId 来返回新的置顶顺序
     */
    public static List<Integer> getAggregateTopDealIds(ActivityContext ctx) {
        return getAggregateTopDealIds(ctx, false);
    }

    /**
     * 解析 summaryProductIds、anchorGoodId 来返回新的置顶顺序
     */
    public static List<Integer> getAggregateTopDealIds(ActivityContext ctx, boolean includeTop) {
        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
        String productIds = null;
        if (includeTop) {
            productIds = ctx.getParam(ShelfActivityConstants.Params.topProductIds);
        }
        List<Integer> summaryDealIds0 = getSummaryDealIds(summaryProductIds, productIds);
        List<Integer> summaryDealIds = ObjectUtils.defaultIfNull(summaryDealIds0, Lists.newArrayList());
        int anchorGoodId = getAnchorGoodId(ctx);
        // 有anchorGoodId时需要强制置顶并去重
        if (anchorGoodId > 0) {
            if (summaryDealIds.contains(anchorGoodId)
                    || Objects.equals("deal", ctx.getParam(ShelfActivityConstants.Params.bizType))) {
                Cat.logEvent("DealShelf", "anchorGoodId-hit");
                Set<Integer> linkedSet = Sets.newLinkedHashSet();
                linkedSet.add(anchorGoodId);
                linkedSet.addAll(summaryDealIds);
                return new ArrayList<>(linkedSet);
            }
        }
        // 没有anchorGoodId时按照老逻辑来走
        return summaryDealIds;
    }

    public static int getAnchorGoodId(ActivityContext ctx) {
        Object anchorGoodIdStr = ctx.getParam(ShelfActivityConstants.Params.anchorGoodId);
        if (anchorGoodIdStr != null) {
            return org.apache.commons.lang3.math.NumberUtils.toInt(String.valueOf(anchorGoodIdStr));
        }
        return 0;
    }

    /**
     * 解析下挂商品字符串，获取团单Id
     * @param summarypids
     * @return
     */
    public static List<Integer> getSummaryDealIds(String summarypids, String productids) {
        if (StringUtils.isNotEmpty(summarypids)) {
            return extractIdBySummarypids(summarypids, "deal", "1");
        }
        return extractIdByProductids(productids);
    }

    /**
     * 解析下挂商品字符串，获取泛商品Id
     * {"deal":"1,2,3", "spu":"4,5,6"}
     * @param summarypids 置顶的商品Id，Json格式的Map<String, String>
     * @param productids 置顶的商品Id，可以同时包含团购、泛商品，格式为id列表，逗号分隔
     * @return product ids
     */
    public static List<Integer> getSummarySpuIds(String summarypids, String productids) {
        if (StringUtils.isNotEmpty(summarypids)) {
            return extractIdBySummarypids(summarypids, "spu", "3");
        }
        return extractIdByProductids(productids);
    }

    private static List<Integer> extractIdByProductids(String productids) {
        if (StringUtils.isEmpty(productids)) {
            return null;
        }
        return NumberUtils.toIntListByStrings(Arrays.asList(productids.split(",")));
    }

    private static List<Integer> extractIdBySummarypids(String summaryStr, String key, String backupKey) {
        if (StringUtils.isEmpty(summaryStr)) {
            return new ArrayList<>();
        }
        Map<String,String> maps = JsonCodec.decode(summaryStr, new TypeReference<Map<String,String>>() {});
        if (MapUtils.isEmpty(maps)){
            return new ArrayList<>();
        }
        String pidStr = maps.get(key);
        if (StringUtils.isEmpty(pidStr)) {
            //兼容下，待上游修复后下线
            pidStr = maps.get(backupKey);
            if (StringUtils.isEmpty(pidStr)) {
                return new ArrayList<>();
            }
        }
        String[] pids = pidStr.split(",");
        return NumberUtils.toIntListByStrings(Arrays.asList(pids));
    }

    public static List<String> extractBySummarypids(String summaryStr, String key) {
        if (StringUtils.isEmpty(summaryStr)) {
            return new ArrayList<>();
        }
        Map<String,String> maps = JsonCodec.decode(summaryStr, new TypeReference<Map<String,String>>() {});
        if (MapUtils.isEmpty(maps)){
            return new ArrayList<>();
        }
        String secondaryFilter = maps.get(key);
        if (StringUtils.isEmpty(secondaryFilter)) {
            return new ArrayList<>();
        }
        return Arrays.asList(secondaryFilter.split(","));
    }
}
