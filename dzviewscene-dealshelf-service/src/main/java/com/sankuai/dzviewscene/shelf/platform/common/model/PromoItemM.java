package com.sankuai.dzviewscene.shelf.platform.common.model;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2021/5/3 下午3:27
 */
public class PromoItemM {

    /**
     * 优惠的ID
     */
    private long promoId;

    /**
     * 优惠的类型Id
     */
    private int promoTypeCode;

    /**
     * 优惠类型，如：商家立减
     */
    private String promoType;

    /**
     * 优惠描述
     */
    private String desc;

    /**
     * 优惠金额标签
     */
    private String promoTag;

    /**
     * 优惠价格
     */
    private BigDecimal promoPrice;

    /**
     * 是否新客
     */
    private boolean isNewUser;

    /**
     * 是否可领优惠
     */
    private boolean canAssign;

    /**
     * 优惠来源类型
     */
    private int sourceType;

    /**
     * 优惠标识，当数据源为价格服务时透传价格服务的标识，其他业务可自定义
     */
    private String promoIdentity;

    /**
     * 优惠图标
     */
    private String icon;

    /**
     * 优惠结束时间
     */
    private long endTime;

    /**
     * 优惠剩余库存
     */
    private int remainStock;

    /**
     * 优惠当日结束时间,用于长期优惠中每日分时段的优惠时间计算
     */
    private long effectiveEndTime;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 券id
     */
    private String couponId;

    /**
     * 券金额
     */
    private BigDecimal amount;

    /**
     * 券门槛金额，单位元
     */
    private BigDecimal minConsumptionAmount;

    /**
     * 优惠解释性标签, 从营销查询优惠策略映射而来，枚举值见 {@link com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum}
     */
    private List<Integer> promotionExplanatoryTags;

    /**
     * 透传的营销扩展信息
     * 扩展字段枚举参考：{@link com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum}
     */
    private Map<String, String> promotionOtherInfoMap;

    /**
     * 优惠文案展示相关信息，纯展示用
     */
    private PromoItemTextM promoItemText;

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    public boolean isCanAssign() {
        return canAssign;
    }

    public void setCanAssign(boolean canAssign) {
        this.canAssign = canAssign;
    }

    public String getPromoType() {
        return promoType;
    }

    public void setPromoType(String promoType) {
        this.promoType = promoType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public BigDecimal getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(BigDecimal promoPrice) {
        this.promoPrice = promoPrice;
    }

    public long getPromoId() {
        return promoId;
    }

    public void setPromoId(long promoId) {
        this.promoId = promoId;
    }

    public int getPromoTypeCode() {
        return promoTypeCode;
    }

    public void setPromoTypeCode(int promoTypeCode) {
        this.promoTypeCode = promoTypeCode;
    }

    public String getPromoIdentity() {
        return promoIdentity;
    }

    public void setPromoIdentity(String promoIdentity) {
        this.promoIdentity = promoIdentity;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getRemainStock() {
        return remainStock;
    }

    public void setRemainStock(int remainStock) {
        this.remainStock = remainStock;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public void setEffectiveEndTime(long effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }

    public long getEffectiveEndTime() {
        return effectiveEndTime;
    }

    public PromoItemTextM getPromoItemText() {
        return promoItemText;
    }

    public void setPromoItemText(PromoItemTextM promoItemText) {
        this.promoItemText = promoItemText;
    }

    public List<Integer> getPromotionExplanatoryTags() {
        return promotionExplanatoryTags;
    }

    public void setPromotionExplanatoryTags(List<Integer> promotionExplanatoryTags) {
        this.promotionExplanatoryTags = promotionExplanatoryTags;
    }

    public Map<String, String> getPromotionOtherInfoMap() {
        return promotionOtherInfoMap;
    }

    public void setPromotionOtherInfoMap(Map<String, String> promotionOtherInfoMap) {
        this.promotionOtherInfoMap = promotionOtherInfoMap;
    }

    public BigDecimal getMinConsumptionAmount() {
        return minConsumptionAmount;
    }

    public void setMinConsumptionAmount(BigDecimal minConsumptionAmount) {
        this.minConsumptionAmount = minConsumptionAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCouponGroupId() {
        return couponGroupId;
    }

    public void setCouponGroupId(String couponGroupId) {
        this.couponGroupId = couponGroupId;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }


}
