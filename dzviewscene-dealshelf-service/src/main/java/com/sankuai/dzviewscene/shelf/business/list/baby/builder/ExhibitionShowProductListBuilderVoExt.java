package com.sankuai.dzviewscene.shelf.business.list.baby.builder;

import com.dianping.cat.Cat;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.list.vo.AttrVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.ShopVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 亲子-展览演出-列表页VO构造扩展点
 *
 * <AUTHOR>
 */
@ExtPointInstance(name = "亲子-展览演出-列表页VO构造扩展点")
public class ExhibitionShowProductListBuilderVoExt extends ProductListBuilderExtAdapter {

    @Override
    public ShopVO shop(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.baby.builder.ExhibitionShowProductListBuilderVoExt.shop(ActivityContext,ProductM)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return null;
        }
        ShopM shopM = productM.getShopMs().get(0);
        ShopVO shopVO = new ShopVO();
        shopVO.setShopId(PoiIdUtil.getShopIdL(shopM));
        shopVO.setDistance(shopM.getDistance());
        shopVO.setShopName(shopM.getShopName());
        shopVO.setAddress(shopM.getAddress());
        return shopVO;
    }


    @Override
    public String price(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.list.baby.builder.ExhibitionShowProductListBuilderVoExt.price(ActivityContext,ProductM)");
        return productM.getBasePriceDesc();
    }

    @Override
    public List<AttrVO> attrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.list.baby.builder.ExhibitionShowProductListBuilderVoExt.attrs(ActivityContext,ProductM)");
        AttrVO timeAttrVO = new AttrVO();
        timeAttrVO.setName("timeRange");
        timeAttrVO.setValue(getTimeAttr(productM));
        return new ArrayList<AttrVO>() {{
            add(timeAttrVO);
        }};
    }

    private String getTimeAttr(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.baby.builder.ExhibitionShowProductListBuilderVoExt.getTimeAttr(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        List<AttrM> extAttrList = productM.getExtAttrs();
        Optional<AttrM> extAttrOptional = extAttrList.stream().filter(attr -> "timeAttr".equals(attr.getName())).findFirst();
        return extAttrOptional.isPresent() ? extAttrOptional.get().getValue() : "";
    }

    @Override
    public String categoryTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.baby.builder.ExhibitionShowProductListBuilderVoExt.categoryTag(ActivityContext,ProductM)");
        return "演出/展览";
    }
}
