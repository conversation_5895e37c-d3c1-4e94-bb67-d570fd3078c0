package com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils;

import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.enums.OceanTypeEnums;

/**
 * 打点常量
 * Created by zhangsuping on 2022/4/6.
 */
public interface OceanConstantUtils {

    /**
     * 商户ID
     */
    String POI_ID = "poi_id";
    /**
     * 商户ID密文
     */
    String POI_ID_ENCRYPT = "poi_idEncrypt";

    /**
     * 城市id
     */
    String CITY_ID = "city_id";

    /**
     * 货架标题
     */
    String MODULE_NAME = "module_name";

    /**
     * 是否是标准化货架，枚举：0-否、1-是
     */
    String ITEM_TYPE = "item_type";

    /**
     * 标准化货架上报结构模式
     */
    String SHOW_MODE = "show_mode";

    /**
     * 交易产品类型，枚举：团购、预订、预付、次卡等 @see {@link OceanTypeEnums}
     */
    String TYPE = "type";

    /**
     * CIA系统所需公共打点，格式："chimera_common":{ "old_bid_mc":"xxx","old_bid_mv":"xxx","scene_code":'xxx'}
     */
    String CHIMERA_COMMON = "chimera_common";

    /**
     * CIA系统所需老bid_mc，上报新的bid_mc关联的老的bid_mc
     */
    String OLD_BID_MC = "old_bid_mc";

    /**
     * CIA系统所需老bid_mv，上报新的bid_mv关联的老的bid_mv
     */
    String OLD_BID_MV = "old_bid_mv";

    /**
     * CIA系统所需场景参数，上报bid所在的具体业务场景
     */
    String SCENE_CODE = "scene_code";

    /**
     * 标题
     */
    String TITLE = "title";

    /**
     * 团购商品ID
     */
    String DEAL_ID = "deal_id";

    /**
     * 泛商品ID
     */
    String PRODUCT_ID = "product_id";

    /**
     * 商品是否是默认展示，0-不是、1-是
     */
    String STATUS = "status";

    /**
     * 索引，从0开始
     */
    String INDEX = "index";

    /**
     * 一级筛选项位次，0、1、2、3
     */
    String TAB_INDEX = "tab_index";

    /**
     * 一级筛选项文案
     */
    String TAB_NAME = "tab_name";

    /**
     * 商品实际售卖价格
     */
    String PRICE = "price";

    /**
     * 推荐流唯一id
     */
    String DZ_REAL_QUERY_ID = "dz_real_query_id";

    String PIC_URL = "pic_url";

    String DZ_ABTEST = "dz_abtest";

    /**
     * 是否SPU货架
     */
    String SPU_TYPE = "spu_type";

    String LABEL_NAME = "label_name";

    //神券价标签
    String PRICE_TITLE = "price_title";
}
