package com.sankuai.dzviewscene.shelf.platform.shelf.model;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

/**
 * 筛选标签
 * <p>
 * Created by float.lu on 2020/8/21.
 */
@Data
public class FilterBtnM {
    /**
     * 筛选ID
     */
    private long filterId;

    /**
     * 类型
     */
    private int type;

    /**
     * 导航标签类型，参考枚举:com.dianping.product.shelf.common.enums.NavTagTypeEnum
     */
    private Integer navTagType;

    /**
     * 层级深度，从1开始
     */
    private int depth = 1;

    /**
     * 标签
     */
    private String tag;

    /**
     * 标签名字
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 扩展内容
     */
    private String extra;

    /**
     * 当前筛选下商品总数
     */
    private int totalCount;

    /**
     * 选中状态
     */
    private boolean selected;

    /**
     * 是否多选
     */
    private boolean multiSelect = false;

    /**
     * 是否活动筛选
     */
    private boolean isActivity;

    /**
     * 活动筛选样式
     */
    private ActivityStyle activityStyle;

    /**
     * 筛选选项树
     */
    private FilterOptionM filterOptionTree;

    /**
     * 孩子节点
     */
    private List<FilterBtnM> children;

    /**
     * 筛选下商品
     */
    private List<ProductM> products;

}
