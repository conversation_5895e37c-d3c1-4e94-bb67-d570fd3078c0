package com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.data.Converters;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.utils.ProductMCompareUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.query.HasIdlePromoSimplexQuery;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.MultiplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.Query;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@ExtPointInstance(name = "丽人-闲时样式货架筛选父类的扩展点")
public class BeautyIdleStyleFilterFetcherExt extends AbstractConfigFilterFetcherExt {

    //////////////////////////////////////常量定义//////////////////////////////////////////
    //丽人filter中的热门、大促、错峰和其他的filterId都是固定的。
    private static final long HOT_FILTER_ID = 1L;
    private static final long ACTIVITY_FILTER_ID = 2L;
    private static final long IDLE_PROMO_FILTER_ID = 3L;
    private static final long OTHER_FILTER_ID = 4L;
    private static final long PERFECT_FILTER_ID = 19L;
    private static final String CATEGORIES = "serviceItemCategories";
    private static final String ATTR_MAIN_TAG = "beautyMainTag";
    private static final String PERFECT_FILTER_TITLE = "玩美季";

    //////////////////////////////////////业务配置//////////////////////////////////////////
    // 产品配置的商户闲时折扣
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.discount.config", defaultValue = "0.5")
    private double discount = 0.5;
    // 丽人美容spa筛选项名称和团单后台分类ID的映射
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.filter.name.category.id.config", defaultValue = "{}")
    private Map<String, String> spaFilterNameCategoryIdMap;
    // 美发筛选项名称和主标签名称列表的映射
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.hair.filter.name.main.tags.config", defaultValue = "{}")
    private Map<String, List<String>> hairFilterNameMainTagsMap;

    /**
     * 养发筛选项名称和主标签名称列表的映射
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.raisehair.filter.name.main.tags.config", defaultValue = "{}")
    private Map<String, List<String>> raiseHairFilterNameMainTagsMap;

    //美甲美睫筛选项名称和主标签名称列表的映射
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.meijiameijie.filter.name.main.tags.config", defaultValue = "{}")
    private Map<String, List<String>> meiJiaMeiJieFilterNameMainTagsMap;
    //商品分类常量
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.dealgroup.category.constant.config", defaultValue = "{}")
    private ProductCategoryConstant productCategoryConstant;
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.constant.config", defaultValue = "{}")
    private IdleStyleConstant idleStyleConstant;

    private static final int TWO_PRODUCTS = 2;
    private static final int ONE_PRODUCT = 1;
    private static final int FILTER_COUNT_MIN = 1;

    //////////////////////////////////////公共扩展点实现//////////////////////////////////////////

    @Override
    public FilterM loadBase(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        FilterM filterM = super.loadBase(activityContext, groupName, productGroupM);
        //美发场景下[beauty_poi_hair_idle_style_deal_shelf]，针对[烫染]Tab需做二次的拆解。
        if (Objects.equals(activityContext.getSceneCode(), "beauty_poi_hair_idle_style_deal_shelf")) {
            if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
                return filterM;
            }
            //拆解逻辑：无n选1团单 OR 当n选1团单占一半一下，且至少有一个烫发团单和一个染发团单时。拆[烫染]为[烫发]和[染发]
            BeautyHairTabFilterHelper.splitTangAndRanTabDynamic(productGroupM.getProducts(), filterM);
            //“拉直”tab的展示规则：门店下含有仅拉直团单
            BeautyHairTabFilterHelper.hideLaZhiTabIfNeed(productGroupM.getProducts(), filterM);
        }
        return filterM;
    }

    @Override
    public long selected(ActivityContext activityContext, String groupName, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.selected(ActivityContext,String,FilterM)");
        return super.selectedWithOptimized(activityContext, groupName, filterM);
    }

    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.endIntercept(ActivityContext,String,FilterM)");
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) return;
        Map<Long, FilterBtnM> filterIdFilterMap = Converters.newMapByPropertyNameConverter("filterId", filterM.getFilters());
        /*tab展示规则: 货架内有大促团单*/
        if (hasLeastOneProduct(filterIdFilterMap, ACTIVITY_FILTER_ID)) return;
        if (hasLeastOneProduct(filterIdFilterMap, PERFECT_FILTER_ID)) {
            return;
        }
        List<FilterBtnM> filters = filterM.getFilters().stream().filter(filterBtnM -> CollectionUtils.isNotEmpty(filterBtnM.getProducts()) && filterBtnM.getProducts().size() >= TWO_PRODUCTS && !getHotIdleActivityAndOtherFilterIds().contains(filterBtnM.getFilterId())).collect(Collectors.toList());
        /*tab展示规则: 分类标签数>=1且该标签的团单数>=2且闲时优惠团单>=1，热门tab和其他不计算在标签分类里*/
        if (hasLeastOneProduct(filterIdFilterMap, IDLE_PROMO_FILTER_ID) && filters.size() >= FILTER_COUNT_MIN)
            return;
        /*tab展示规则: 分类标签数>=2且该标签的团单数>=2，热门tab和其他不计算在标签分类里*/
        if (filters.size() >= TWO_PRODUCTS) {
            return;
        }
        filterM.setFilters(new ArrayList<>());
    }

    private boolean hasLeastOneProduct(Map<Long, FilterBtnM> filterIdFilterMap, long filterId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.hasLeastOneProduct(java.util.Map,long)");
        if (CollectionUtils.isEmpty(filterIdFilterMap.keySet()) || filterIdFilterMap.get(filterId) == null || CollectionUtils.isEmpty(filterIdFilterMap.get(filterId).getProducts())) {
            return false;
        }
        return filterIdFilterMap.keySet().contains(filterId) && filterIdFilterMap.get(filterId).getProducts().size() >= ONE_PRODUCT;
    }

    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.endIntercept(ActivityContext,String,FilterBtnM)");
        /*闲时立减tab拼接规则="错峰x折"*/
        if (filterBtnM.getFilterId() == IDLE_PROMO_FILTER_ID) {
            filterBtnM.setTitle(idleStyleConstant.getIdleFilterPattern());
            return;
        }
        /*取大促标签名作为tab名*/
        if (filterBtnM.getFilterId() == ACTIVITY_FILTER_ID && CollectionUtils.isNotEmpty(filterBtnM.getProducts())) {
            String activityFilterName = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
            if (StringUtils.isEmpty(activityFilterName)) {
                return;
            }
            filterBtnM.setTitle(activityFilterName);
        }
        /*取玩美季标签名作为tab名*/
        if (filterBtnM.getFilterId() == PERFECT_FILTER_ID && CollectionUtils.isNotEmpty(filterBtnM.getProducts())) {
            String activityFilterName = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
            if (StringUtils.isEmpty(activityFilterName)) {
                return;
            }
            filterBtnM.setTitle(activityFilterName);
        }
    }

    @Override
    public Map<Long, Query> loadQuery(ActivityContext activityContext, String groupName, FilterM filter) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.loadQuery(ActivityContext,String,FilterM)");
        if (filter == null || CollectionUtils.isEmpty(filter.getFilters())) {
            return new HashMap<>();
        }
        Map<Long, Query> filterToQuery = new HashMap<>();
        filter.getFilters().forEach(filterBtnM -> {
            filterToQuery.put(filterBtnM.getFilterId(), buildQuery(filterBtnM, activityContext));
        });
        return filterToQuery;
    }

    //////////////////////////////////////供给召回策略实现//////////////////////////////////////////

    protected SimplexQuery activityQuery(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.activityQuery(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String activityTag = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
                return StringUtils.isNotEmpty(activityTag) && CollectionUtils.isNotEmpty(product.getActivities()) && isNotShowPerfectActivity(activityTag, product);
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    private boolean isNotShowPerfectActivity(String activityTag, ProductM product) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.isNotShowPerfectActivity(java.lang.String,com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        return !activityTag.equals(PERFECT_FILTER_TITLE) && !PerfectActivityBuildUtils.isShowPerfectActivity(product);
    }

    protected SimplexQuery perfectActivityQuery(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.perfectActivityQuery(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String activityTag = getActivityTabTitle(activityContext.getExtContext(ActivityContextExt.ACTIVITY_CONTEXT));
                return StringUtils.isNotEmpty(activityTag) && PerfectActivityBuildUtils.isShowPerfectActivity(product);
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    protected SimplexQuery idlePromoQuery(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.idlePromoQuery(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                long shopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
                int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
                if (platform == VCPlatformEnum.MT.getType()) {
                    shopId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
                }
                return new HasIdlePromoSimplexQuery(shopId, platform).match(context, product);
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    private List<Long> getHotIdleActivityAndOtherFilterIds() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.getHotIdleActivityAndOtherFilterIds()");
        return Lists.newArrayList(HOT_FILTER_ID, IDLE_PROMO_FILTER_ID, ACTIVITY_FILTER_ID, OTHER_FILTER_ID);
    }

    private String getActivityTabTitle(CompletableFuture<List<DealActivityDTO>> activityDTOsCompletableFuture) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.getActivityTabTitle(java.util.concurrent.CompletableFuture)");
        if (activityDTOsCompletableFuture == null) {
            return Strings.EMPTY;
        }
        List<DealActivityDTO> activityDTOS = activityDTOsCompletableFuture.join();
        if (CollectionUtils.isNotEmpty(activityDTOS) && activityDTOS.get(0) != null && CollectionUtils.isNotEmpty(activityDTOS.get(0).getTitles())) {
            return activityDTOS.get(0).getTitles().get(0);
        }
        return Strings.EMPTY;
    }

    private Query buildQuery(FilterBtnM filterBtnM, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.buildQuery(FilterBtnM,ActivityContext)");
        //热门
        if (HOT_FILTER_ID == filterBtnM.getFilterId()) {
            return hotQuery(ctx);
        }
        //玩美季
        if (PERFECT_FILTER_ID == filterBtnM.getFilterId()) {
            return perfectActivityQuery(ctx);
        }
        //大促
        if (ACTIVITY_FILTER_ID == filterBtnM.getFilterId()) {
            return activityQuery(ctx);
        }
        //闲时优惠
        if (IDLE_PROMO_FILTER_ID == filterBtnM.getFilterId()) {
            return idlePromoQuery(ctx);
        }
        //其他
        if (OTHER_FILTER_ID == filterBtnM.getFilterId()) {
            return otherQuery(ctx.getSceneCode());
        }
        //养发-主标签召回
        if (raiseHairFilterNameMainTagsMap.keySet().contains(filterBtnM.getTitle()) && Objects.equals(ctx.getSceneCode(), "beauty_poi_raise_hair_idle_style_deal_shelf")) {
            return mainTagQuery(raiseHairFilterNameMainTagsMap.get(filterBtnM.getTitle()), productCategoryConstant.getRaiseHairIds());
        }
        //美发-主标签
        if (hairFilterNameMainTagsMap.keySet().contains(filterBtnM.getTitle()) && !Objects.equals(ctx.getSceneCode(), "beauty_poi_raise_hair_idle_style_deal_shelf")) {
            return mainTagQuery(hairFilterNameMainTagsMap.get(filterBtnM.getTitle()), productCategoryConstant.getHairIds());
        }
        //美甲美甲-主标签
        if (meiJiaMeiJieFilterNameMainTagsMap.keySet().contains(filterBtnM.getTitle())) {
            return mainTagQuery(meiJiaMeiJieFilterNameMainTagsMap.get(filterBtnM.getTitle()), productCategoryConstant.getMeiJiaMeiJieIds());
        }
        //服务类型分类ID
        if (spaFilterNameCategoryIdMap.keySet().contains(filterBtnM.getTitle())) {
            return categoryQuery(spaFilterNameCategoryIdMap.get(filterBtnM.getTitle()));
        }
        //美发N选1的召回
        if (BeautyHairTabFilterHelper.BEAUTY_HAIR_TARGET_TAG_ID_MAP.containsKey(filterBtnM.getTitle())) {
            return BeautyHairTabFilterHelper.buildProductTagQueryWithNToOne(filterBtnM.getTitle());
        }
        return defaultQuery();
    }

    private MultiplexQuery hotQuery(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.hotQuery(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return new MultiplexQuery() {
            @Override
            public List<SimplexQuery> loadQuery() {
                return new ArrayList<SimplexQuery>() {{
                    add(new SimplexQuery() {
                        @Override
                        public int topN(QueryContext context) {
                            return 3;
                        }
                    });
                    add(new SimplexQuery() {
                        @Override
                        public boolean match(QueryContext context, ProductM product) {
                            return product.getPromo(PromoTypeEnum.DIRECT_PROMO.getType()) != null;
                        }
                    });
                    add(new SimplexQuery() {
                        @Override
                        public boolean match(QueryContext context, ProductM product) {
                            if (CollectionUtils.size(context.getMatched()) >= 5)
                                return false;
                            return true;
                        }

                        @Override
                        public int topN(QueryContext context) {
                            return 5 - context.getMatched().size();
                        }
                    });
                }};
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                long shopId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
                int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
                if (platform == VCPlatformEnum.MT.getType()) {
                    shopId = PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
                }
                return buildIdleStyleHotComparator(context.getMatched(), shopId, platform);
            }
        };
    }

    /**
     * 置顶两个闲时闲时优惠，如果前两个都是闲时优惠团单则销量倒序，其他的团单销量倒序
     * @param products
     * @return
     */
    private Comparator<ProductM> buildIdleStyleHotComparator(List<ProductM> products, long shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.buildIdleStyleHotComparator(java.util.List,long,int)");
        List<Integer>  topTwoIdleProductIds = getTopTwoIdleProductIds(products, shopId, platform);
        return new Comparator<ProductM>() {
            @Override
            public int compare(ProductM o1, ProductM o2) {
                //优先商家推荐
                int cmpRecommend = ProductMCompareUtils.shopRecommendFirst(o1, o2);
                if (cmpRecommend != ProductMCompareUtils.EQ) {
                    return cmpRecommend;
                }
                //其次排序两个闲时优惠
                int idlePromo = compareIdlePromoProduct(o1, o2, topTwoIdleProductIds);
                if (idlePromo != 0) {
                    return idlePromo;
                }
                //再其次是销量
                return sortSaleByDesc(o1, o2);
            }
        };
    }

    private List<Integer> getTopTwoIdleProductIds(List<ProductM> products, long shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.getTopTwoIdleProductIds(java.util.List,long,int)");
        HasIdlePromoSimplexQuery hasIdlePromoSimplexQuery = new HasIdlePromoSimplexQuery(shopId, platform);
        return products.stream()
                .filter(productM -> hasIdlePromoSimplexQuery.match(null, productM))
                .sorted((o1, o2) -> sortSaleByDesc(o1, o2))
                .map(ProductM::getProductId)
                .limit(idleStyleConstant.hotIdleDealMaxCount)
                .collect(Collectors.toList());
    }

    private SimplexQuery defaultQuery() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.defaultQuery()");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return false;
            }
        };
    }

    private SimplexQuery otherQuery(String sceneCode) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.otherQuery(java.lang.String)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String mainTag = product.getAttr(ATTR_MAIN_TAG);
                if (hitMainTag(mainTag, hairFilterNameMainTagsMap.values(), productCategoryConstant.getHairIds(), product.getCategoryId()) && !Objects.equals(sceneCode, "beauty_poi_raise_hair_idle_style_deal_shelf")) {
                    return false;
                }
                if (hitMainTag(mainTag, raiseHairFilterNameMainTagsMap.values(), productCategoryConstant.getRaiseHairIds(), product.getCategoryId()) && Objects.equals(sceneCode, "beauty_poi_raise_hair_idle_style_deal_shelf")) {
                    return false;
                }
                if (hitMainTag(mainTag, meiJiaMeiJieFilterNameMainTagsMap.values(), productCategoryConstant.getMeiJiaMeiJieIds(), product.getCategoryId())) {
                    return false;
                }
                if (hasCategoryFilterIds(product.getAttr(CATEGORIES), productCategoryConstant.spaIds, product.getCategoryId())) {
                    return false;
                }
                //不匹配主标签，且也不匹配项目分类
                return true;
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    private boolean hasCategoryFilterIds(String categoriesStr, List<Integer> spaCategoryIds, int categoryId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.hasCategoryFilterIds(java.lang.String,java.util.List,int)");
        if (StringUtils.isEmpty(categoriesStr)) {
            return false;
        }
        List<String> categories = getCategories(categoriesStr);
        for (String category : spaFilterNameCategoryIdMap.values()) {
            if (categories.contains(category) && spaCategoryIds.contains(categoryId)) {
                return true;
            }
        }
        return false;
    }

    private boolean hitMainTag(String mainTag, Collection<List<String>> mainTagsList, List<Integer> productCategoryIds, int categoryId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.hitMainTag(java.lang.String,java.util.Collection,java.util.List,int)");
        if (CollectionUtils.isEmpty(mainTagsList) || !productCategoryIds.contains(categoryId)) {
            return false;
        }
        List<String> mainTags = new ArrayList<>();
        mainTagsList.stream().forEach(list -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            mainTags.addAll(list);
        });
        return CollectionUtils.isNotEmpty(mainTags) && mainTags.contains(mainTag);
    }

    private SimplexQuery categoryQuery(String categoryId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.categoryQuery(java.lang.String)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                return StringUtils.isNotEmpty(categoryId) && getCategories(product.getAttr(CATEGORIES)).contains(categoryId) && productCategoryConstant.spaIds.contains(product.getCategoryId());
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    private List<String> getCategories(String categoriesStr) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.getCategories(java.lang.String)");
        if (StringUtils.isEmpty(categoriesStr)) return new ArrayList<>();
        return NumberUtils.splitToList(categoriesStr);
    }

    private SimplexQuery mainTagQuery(List<String> mainTags, List<Integer> productCategoryIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.mainTagQuery(java.util.List,java.util.List)");
        return new SimplexQuery() {
            @Override
            public boolean match(QueryContext context, ProductM product) {
                String attrMainTag = product.getAttr(ATTR_MAIN_TAG);
                return StringUtils.isNotBlank(attrMainTag) && mainTags.contains(attrMainTag) && productCategoryIds.contains(product.getCategoryId());
            }

            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (ProductMCompareUtils::shopRecommendFirst);
            }
        };
    }

    /**
     * 比较商品闲时优惠优先级
     */
    private int compareIdlePromoProduct(ProductM o1, ProductM o2, List<Integer> idleProductIds) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.compareIdlePromoProduct(ProductM,ProductM,List)");
        if (CollectionUtils.isEmpty(idleProductIds)) {
            return 0;
        }
        boolean aHasIdlePromo = idleProductIds.contains(o1.getProductId());
        boolean bHasIdlePromo = idleProductIds.contains(o2.getProductId());
        //两个商品都没有闲时优惠
        if (!aHasIdlePromo && !bHasIdlePromo) {
            return 0;
        }
        //两个商品都有闲时优惠 比较销量
        if (aHasIdlePromo && bHasIdlePromo) {
            return sortSaleByDesc(o1, o2);
        }
        //哪个商品有闲时优惠，排在前面
        if (aHasIdlePromo) {
            return -1;
        }
        return 1;
    }

    private int sortSaleByDesc(ProductM o1, ProductM o2) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.beauty.fetcher.BeautyIdleStyleFilterFetcherExt.sortSaleByDesc(ProductM,ProductM)");
        int aDisplaySale = o1.getSale() == null ? 0 : o1.getSale().getSale();
        int bDisplaySale = o2.getSale() == null ? 0 : o2.getSale().getSale();
        return bDisplaySale - aDisplaySale;
    }

    @Data
    private static class ProductCategoryConstant {
        private List<Integer> hairIds;
        private List<Integer> meiJiaMeiJieIds;
        private List<Integer> spaIds;
        private List<Integer> raiseHairIds;
    }

    @Data
    private static class IdleStyleConstant {
        private String dpIdleStyleSk;
        private String mtIdleStyleSk;
        private String idleFilterPattern;
        private int hotIdleDealMaxCount = 2;
    }
}
