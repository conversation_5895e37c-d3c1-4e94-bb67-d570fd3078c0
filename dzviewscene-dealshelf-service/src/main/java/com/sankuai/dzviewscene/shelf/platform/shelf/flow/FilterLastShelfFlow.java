package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

/**
 * Created by float.lu on 2020/9/11.
 */

import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 分组模式生成货架, 流程步骤如下:
 * 1. 第一步: 斗斛(可选)
 * 2. 第二步: 召回
 * 3. 第三步: 填充
 * 4. 第四步: 筛选生成
 *
 * 适应场景: 筛选生成的数据需要等填充之后
 *
 * Created by float.lu on 2020/9/4.
 */
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = FilterLastShelfFlow.ACTIVITY_FLOW_FILTER_LAST_SHELF_FLOW, name = "")
public class FilterLastShelfFlow implements IActivityFlow<ShelfGroupM> {

    public static final String ACTIVITY_FLOW_FILTER_LAST_SHELF_FLOW = "FilterLastShelfFlow";

    @Override
    public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        // 1. 查询斗斛
         CompletableFuture<DouHuM> douHuMCompletableFuture = activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE).build(ctx);

        // 2. 召回: 商品组名->商品列表
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx);

        // 3. 召回之后生成填充
        CompletableFuture<Map<String, ProductGroupM>> paddingProductGroupsCompletableFuture = productGroupsCompletableFuture.thenCompose(productGroups -> {
            // 3.1 暂存召回结果, 能力实现自行填充
            ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
        });

        // 4. 填充之后生成筛选: 商品组名->生成筛选
        CompletableFuture<Map<String, FilterM>> filterMCompletableFuture = paddingProductGroupsCompletableFuture.thenCompose(productGroups -> {
            // 4.1 放入召回结果, 能力实现基于召回结果生成筛选
            ctx.attach(FilterFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE).build(ctx);
        });


        return CompletableFuture.allOf(douHuMCompletableFuture, filterMCompletableFuture, paddingProductGroupsCompletableFuture).thenApply(v -> {
            ShelfGroupM shelfGroupM = new ShelfGroupM();
            shelfGroupM.setDouHu(douHuMCompletableFuture.join());
            shelfGroupM.setFilterMs(filterMCompletableFuture.join());
            shelfGroupM.setProductGroupMs(paddingProductGroupsCompletableFuture.join());
            return shelfGroupM;
        });
    }
}
