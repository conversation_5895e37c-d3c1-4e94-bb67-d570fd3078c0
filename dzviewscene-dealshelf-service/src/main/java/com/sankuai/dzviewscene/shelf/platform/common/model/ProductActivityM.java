package com.sankuai.dzviewscene.shelf.platform.common.model;

import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 商品活动
 *
 * Created by float.lu on 2020/9/15.
 */
@Data
public class ProductActivityM {
    public static final String ICON_WIDTH_KEY = "iconWidth";
    public static final String ICON_HEIGHT_KEY = "iconHeight";
    public static final String TITLE = "title";
    public static final String TITLE_COLOR = "titleColor";
    /**
     * 倒计时展示控制标记（团详，商详需要关心）
     */
    private boolean timeDisplayFlag;

    /**
     * 活动剩余时间，单位：秒
     */
    private long remainingTime;

    /**
     * 距活动剩余时间，单位：毫秒
     */
    private long remainingTimeMillisecond;

    /**
     * 活动头图URL
     */
    private String url;

    /**
     * 活动图片对应的宽高比
     */
    private double urlAspectRadio;

    /**
     * 活动的标题/标签
     */
    private String lable;

    /**
     *是否为预热活动
     */
    private boolean isPreheat;

    /**
     * 活动图片列表
     */
    private List<String> urls;

    /**
     * 活动开始时间
     */
    private long activityBeginTime;

    /**
     * 活动结束时间
     */
    private long activityEndTime;

    /**
     * 注意注意：判断营销的真秒杀活动，需要用值为 SEC_KILL 且 活动扩展Key RainbowSecKillSource 的值为 true ；
     * 单独的值为 SEC_KILL ，代表的意思是 A 级大促！！！！
     * 货架活动类型，0：普通活动，1：完美季大促
     * 来源于营销活动类型：{@link ExposureActivityTypeEnum}
     */
    private Integer shelfActivityType;

    /**
     * 活动价格文案
     */
    private String activityPriceText;

    /**
     * 活动价格颜色
     */
    private String activityPriceColor;

    /**
     * 活动的跳转链接
     */
    private String jumpLink;

    /**
     * 活动页ID
     */
    private int pageId;

    /**
     * 活动关联的skuId
     */
    private int skuId;

    /**
     * 活动类型（团购场景没有使用到） 含义与shelfActivityType相同，该字段后期废弃
     */
    @Deprecated
    private int activityType;

    /**
     * 活动主题背景图片url，有券
     */
    private String bgPicUrlWithCoupon;

    /**
     * 活动主题背景图片url，无券
     */
    private String bgPicUrlNoCoupon;

    /**
     * 色变值列表
     */
    private List<String> colors;

    /**
     * 倒计时背景色
     */
    private String bgColor;

    /**
     * 倒计时文字颜色
     */
    private String textColor;

    /**
     * 优惠列表
     */
    private List<ProductPromoPriceM> promos;

    /**
     * 图片地址  key见
     * 营销枚举 @see {@link ExposurePicUrlKeyEnum}
     */
    private Map<String, String> picUrlMap;

    /**
     * 展示类型
     */
    private int displayType;

    /**
     * 营销的活动场景，参考营销枚举：ExposureActivitySceneEnum
     */
    private int activityScene;

    /**
     * 价格力时间配置
     * 营销枚举：com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum
     */
    private int priceStrengthTime;

    /**
     *
     * pp商品查询的打标信息
     */
    private PpEffectiveTagInfo ppEffectiveTagInfo;

    /**
     *
     * 分场景需要处理的个性化属性MAP，按需使用
     */
    private Map<String, Object> activityExtraAttrs;

    /**
     * key为"picUrlKey", description = "图片跳转链接，参考 com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum"
     */
    private Map<String, ActivityPicUrlDTO> activityPicUrlMap;

    /**
     * 曝光大促活动类型, 参考com.dianping.gmkt.activ.api.enums.ExposurePromotionTypeEnum
     */
    private int exposurePromotionType;

    /**
     * 查询指定类型的优惠价格
     * @param type
     * @return
     */
    public ProductPromoPriceM getPromoPrice(int type) {
        if (CollectionUtils.isEmpty(this.getPromos())) {
            return null;
        }
        return this.getPromos().stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == type).findFirst().orElse(null);
    }

    public boolean ifMatchActivityType(DealActivityTypeEnum activityTypeEnum) {
        if (getShelfActivityType() == null || activityTypeEnum == null) {
            return false;
        }
        return activityTypeEnum.getType() == getShelfActivityType();
    }
}
