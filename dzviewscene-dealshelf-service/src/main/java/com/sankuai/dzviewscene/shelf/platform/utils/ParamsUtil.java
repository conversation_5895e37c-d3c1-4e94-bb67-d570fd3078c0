package com.sankuai.dzviewscene.shelf.platform.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.net.URLDecoder;
import java.util.*;


/**
 * 参数读取工具
 *
 * Created by float.lu on 2020/9/17.
 */
@Slf4j
public class ParamsUtil {


    /**
     * 判断params是否为空（empty、null）、map是否包含name、value是否为空
     * @param params 参数的map
     * @param name 参数的key
     * @return true：params为空（empty、null）、map不包含name、value是空；否则返回false；
     */
    public static boolean isEmpty(Map<String, Object> params, String name) {
        return MapUtils.isEmpty(params) || params.get(name) == null;
    }

    /**
     * 从params里获取value；
     * 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     * @param params 参数的map
     * @param name 参数的key
     * @param defaultValue 默认值
     * @return 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     */
    public static Object getValue(Map<String, Object> params, String name, Object defaultValue) {
        if (isEmpty(params, name)) {
            return defaultValue;
        }
        return params.get(name);
    }

    /**
     * 从params里获取value；
     * 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     * @param activityContext 参数的ctx
     * @param name 参数的key
     * @param defaultValue 默认值
     * @return 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     */
    public static <T> T getValue(ActivityContext activityContext, String name, T defaultValue) {
        if (activityContext==null || activityContext.isParamNull(name)) {
            return defaultValue;
        }
        return activityContext.getParam(name);
    }

    /**
     * 从params里获取value
     *
     * @param params 参数的map
     * @param name 参数的key
     * @return 参数的value（可能为null）；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为null。
     */
    public static Object getValueNullable(Map<String, Object> params, String name) {
        return getValue(params, name, null);
    }

    /**
     * 安全低获取int型参数
     * @param activityContext
     * @param name
     * @return
     */
    public static int getIntSafely(ActivityContext activityContext, String name) {
        if (activityContext == null) {
            return 0;
        }
        return getIntSafely(activityContext.getParameters(), name);
    }

    /**
     * 安全低获取int型参数
     */
    public static int getIntSafely(ActivityCxt activityCxt, String name) {
        if (activityCxt == null) {
            return 0;
        }
        return getIntSafely(activityCxt.getParameters(), name);
    }

    /**
     * 安全地获取long类型参数
     * @param activityContext
     * @param name
     * @return
     */
    public static long getLongSafely(ActivityContext activityContext, String name) {
        if (activityContext == null) {
            return 0;
        }
        try {
            Object value = activityContext.getParam(name);
            if (value instanceof Integer) {
                return Long.parseLong(value + StringUtils.EMPTY);
            }
            if (value instanceof Long) {
                return (Long) value;
            }

            return Long.parseLong((String) value);
        } catch (Exception e) {
            // no op
        }
        return 0;
    }

    /**
     * 安全地获取long类型参数
     * @param params
     * @param name
     * @return
     */
    public static long getLongSafely(Map<String, Object> params, String name) {
        if (MapUtils.isEmpty(params)) {
            return 0;
        }
        try {
            Object value = params.get(name);
            return Long.parseLong(value.toString());
        } catch (Exception e) {
            // no op
        }
        return 0;
    }

    public static long getLongSafely(String num) {
        try {
            return Long.parseLong(num);
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s", "getLongSafely"), e);
        }
        return 0;
    }

    /**
     * 安全地获取double类型参数
     * @param activityContext
     * @param name
     * @return
     */
    public static double getDoubleSafely(ActivityContext activityContext, String name) {
        if (activityContext == null) {
            return 0;
        }
        try {
            return Double.parseDouble(activityContext.getParam(name) + StringUtils.EMPTY);
        } catch (Exception e) {
            // no op
        }
        return 0;
    }

    /**
     * 安全地获取double类型参数
     * @param params
     * @param name
     * @return
     */
    public static double getDoubleSafely(Map<String, Object> params, String name) {
        if (MapUtils.isEmpty(params)) {
            return 0;
        }
        try {
            Object value = params.get(name);
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            // no op
        }
        return 0;
    }

    /**
     * 安全地从map中读取int参数
     *
     * @param params
     * @param name
     * @return
     */
    public static int getIntSafely(Map<String, Object> params, String name) {
        if (MapUtils.isEmpty(params)) {
            return 0;
        }
        try {
            Object value = params.get(name);
            if (value instanceof Integer) {
                return (Integer) value;
            }
            if (value instanceof Long) {
                return ((Long)value).intValue();
            }

            return Integer.parseInt((String) value);
        } catch (Exception e) {
            // no op
        }
        return 0;
    }

    public static int getIntValue(Map<String, Object> params, String name, int defaultValue) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntValue(java.util.Map,java.lang.String,int)");
        if (MapUtils.isEmpty(params)) {
            return defaultValue;
        }
        try {
            Object value = params.get(name);
            return Integer.parseInt(value.toString());
        } catch (Exception e) {
            // no op
        }
        return defaultValue;
    }

    /**
     * 安全的获取Date型参数
     * @param activityContext
     * @param name
     * @return
     */
    public static Date getDateSafely(ActivityContext activityContext, String name) {
        if (activityContext == null) {
            return null;
        }
        return getDateSafely(activityContext.getParameters(), name);
    }

    public static Date getDateSafely(Map<String, Object> params, String name) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        return (Date) params.get(name);
    }

    /**
     * 安全获取Boolean类型参数
     * @param activityContext
     * @param name
     * @return
     */
    public static boolean getBooleanSafely(ActivityContext activityContext, String name) {
        if(activityContext == null){
            return false;
        }
        return getBooleanSafely(activityContext.getParameters(), name);
    }

    /**
     * 安全地从map中读取Boolean参数
     * @param params
     * @param name
     * @return
     */
    public static boolean getBooleanSafely(Map<String, Object> params, String name){
        if (MapUtils.isEmpty(params)) {
            return false;
        }
        try {
            Object value = params.get(name);
            if(value == null){
                return false;
            }
            if(value instanceof Boolean){
                return (Boolean)value;
            }
        } catch (Exception e) {
            // no op
        }
        return false;
    }

    public static<T> T getExtraParamFromMap(Map<String, Object> extParams, String key, T defaultValue) {
        Map<String, Object> extraMap = JsonCodec.decode((String) extParams.get(FilterListActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {});
        if (MapUtils.isEmpty(extraMap)) {
            return null;
        }
        return Optional.ofNullable((T) extraMap.get(key)).orElse(defaultValue);
    }

    public static <T> T getParamsFromExtraMap(String extra, String key, T defaultValue) {
        Map<String, Object> extraMap = JsonCodec.decode(extra, Map.class);
        if (Objects.isNull(extraMap)) {
            return defaultValue;
        }
        return Optional.ofNullable((T) extraMap.get(key)).orElse(defaultValue);
    }

    public static <T> T getParamFromExtraMap(Map<String, Object> params, String key, T defaultValue) {
        if (StringUtils.isBlank(key)) {
            return defaultValue;
        }

        Map<String, Object> extraMap = JsonCodec.decode(
                decodeParam(getStringSafely(params, ShelfActivityConstants.Params.extra)),
                new TypeReference<Map<String, Object>>() {});
        return MapUtils.isEmpty(extraMap) ? defaultValue : (T)extraMap.getOrDefault(key, defaultValue);
    }

    private static String decodeParam(String params) {
        if(StringUtils.isBlank(params)){
            return StringUtils.EMPTY;
        }
        try {
            return URLDecoder.decode(params, "utf-8");
        } catch (Exception e) {
            log.error("decodeParam error, params:{}", params, e);
            return StringUtils.EMPTY;
        }
    }

    public static String getStringSafely(ActivityContext activityContext, String name) {
        if (activityContext == null) {
            return Strings.EMPTY;
        }
        return getStringSafely(activityContext.getParameters(), name);
    }

    /**
     * 安全地从map中读取String参数
     *
     * @param params
     * @param name
     * @return
     */
    public static String getStringSafely(Map<String, Object> params, String name) {
        if (MapUtils.isEmpty(params)) {
            return Strings.EMPTY;
        }
        try {
            return params.get(name).toString();
        } catch (Exception e) {
            // no op
        }
        return Strings.EMPTY;
    }

    /**
     * 安全的获取用户 id
     * @param ctx
     * @return
     */
    public static long getUserIdForShelfActivity(ActivityContext ctx) {
        int platform = getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return getLongSafely(ctx, ShelfActivityConstants.Params.mtUserId);
        }
        return getLongSafely(ctx, ShelfActivityConstants.Params.dpUserId);
    }

    public static long getUserId(ActivityCxt ctx) {
        int platform = getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return getLongSafely(ctx.getParameters(), ShelfActivityConstants.Params.mtUserId);
        }
        return getLongSafely(ctx.getParameters(), ShelfActivityConstants.Params.dpUserId);
    }

    public static long getUserIdForFilterList(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getUserIdForFilterList(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return getLongSafely(ctx, FilterListActivityConstants.Params.mtUserId);
        }
        return getLongSafely(ctx, FilterListActivityConstants.Params.dpUserId);
    }

    /**
     * 安全的获取用户 id
     * @param ctx
     * @return
     */
    public static long getCommonUserId(ActivityContext ctx) {
        int platform = getIntSafely(ctx, "platform");
        if (PlatformUtil.isMT(platform)) {
            return getLongSafely(ctx, "mtUserId");
        }
        return getLongSafely(ctx, "dpUserId");
    }

    /**
     * 安全的获取门店 id
     * @param ctx
     * @return
     */
    public static long getShopIdForShelfActivity(ActivityContext ctx) {
        int platform = getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return getLongSafely(ctx, ShelfActivityConstants.Params.mtPoiId);
        }
        return getLongSafely(ctx, ShelfActivityConstants.Params.dpPoiId);
    }

    public static long getLongShopIdForShelfActivity(ActivityContext ctx) {
        int platform = getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    public static List<Integer> extractIdBySummarypids(String summaryStr, String key) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.extractIdBySummarypids(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(summaryStr)) {
            return new ArrayList<>();
        }
        Map<String,String> maps = JsonCodec.decode(summaryStr, new TypeReference<Map<String,String>>() {});
        if (MapUtils.isEmpty(maps)){
            return new ArrayList<>();
        }
        String pidStr = maps.get(key);
        if (StringUtils.isEmpty(pidStr)) {
            return new ArrayList<>();
        }
        String[] pids = pidStr.split(",");
        return NumberUtils.toIntListByStrings(Arrays.asList(pids));
    }

}
