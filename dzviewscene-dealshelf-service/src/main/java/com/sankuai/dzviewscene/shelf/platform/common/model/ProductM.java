package com.sankuai.dzviewscene.shelf.platform.common.model;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.common.utils.DealAttrUtils;
import com.sankuai.dztheme.deal.res.DealProductSpuAttrDTO;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dztheme.deal.res.standardservice.DealStandardServiceProjectDTO;
import com.sankuai.dztheme.deal.res.structured.DealDetailStructuredDTO;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.AdditionalDealUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 货架Item区
 * Created by float.lu on 2020/8/21.
 */
@Data
public class ProductM extends GroupNameM implements RankingItem {

    ///////////////////////////基础信息///////////////////////////

    /**
     * 商品类型 {@link com.sankuai.dzviewscene.product.enums.ProductTypeEnum}
     * 必填字段，默认是团购
     */
    private int productType;

    /**
     * item id, 对于货架来说, 是商品ID, 跟着平台走
     */
    private int productId;

    /**
     * id（框架2.0 使用该id）
     */
    private ProductIdM id;

    /**
     * 商品类目ID
     */
    private int categoryId;

    /**
     * 商品三级ID
     */
    private long serviceTypeId;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 商品类型
     */
    private int spuType;

    /**
     * 标题
     */
    private String title;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 下单链接
     */
    private String orderUrl;

    /**
     * 跳转文案
     */
    private String jumpText;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 是否可以预订
     */
    private Boolean available;

    /**
     * 商品Item列表
     */
    private List<ProductItemM> productItemMList;

    /**
     * 拼团Item列表
     */
    private List<ProductItemM> dealPinItemMList;

    /**
     * 拼场列表
     */
    private List<ProductPinPoolM> pinPools;

    /**
     * SPU
     */
    private DealProductSpuDTO spuM;

    private List<DealProductSpuDTO> spuMList;

    ///////////////////////////销量信息///////////////////////////

    /**
     * 销量
     */
    @Deprecated
    private String saleTag;

    /**
     * 销量
     */
    private ProductSaleM sale;

    /**
     * 库存
     */
    private ProductStockM stock;

    ///////////////////////////价格信息///////////////////////////

    /**
     * 售卖价格, 包含起字等
     */
    private String basePriceTag;

    /**
     * 商品原始售卖价格的描述, 如起、XX节课
     */
    private String basePriceDesc;

    /**
     * 商品原始售卖价格, 不包含任何优惠
     */
    private BigDecimal basePrice;

    /**
     * 市场价格
     */
    private String marketPrice;

    /**
     * 会员价格, 删除, 会员价格统一放在#promoPrices字段中
     */
    @Deprecated
    private String vipPrice;

    /**
     * 优惠信息, 删除, 优惠标签统一放在#promoPrices字段中
     */
    @Deprecated
    private String promoTag;

    /**
     * 融合优惠价格
     */
    private List<ProductPromoPriceM> promoPrices;

    /**
     * 最优promoPrice
     */
    private ProductPromoPriceM bestPromoPrice;

    ///////////////////////////升级售卖价格///////////////////////////

    /**
     * 拼团售卖价格
     */
    private ProductPriceM pinPrice;

    /**
     * 次卡售卖价格
     */
    private ProductPriceM cardPrice;

    ///////////////////////////售卖信息///////////////////////////

    /**
     * 购买信息
     */
    private String purchase;

    ///////////////////////////返券///////////////////////////

    /**
     * 返券
     */
    @Deprecated
    private ProductCouponM couponM;

    /**
     * 返券
     */
    private List<ProductCouponM> coupons;

    ///////////////////////////商品参与的活动列表///////////////////////////

    /**
     * 商品活动
     */
    private List<ProductActivityM> activities;

    ///////////////////////////其他信息///////////////////////////

    /**
     * 门店ID
     */
    private List<Integer> shopIds;
    private List<Long> shopLongIds;

    /**
     * 适用门店数量
     */
    private int shopNum;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 产品标签
     */
    private List<String> productTags;

    /**
     * 扩展属性
     */
    private List<AttrM> extAttrs;

    /**
     * 扩展属性(value是对象)
     */
    private List<ObjAttrM> extObjAttrs;

    /**
     * 用户是否订阅
     */
    private boolean userSubscribe;

    /**
     * 是否置顶
     */
    private boolean isTop;
    /**
     * 评价和星级信息
     */
    private ReviewM review;

    /**
     * 标品榜单信息
     */
    private ResourceRankM resourceRank;

    /**
     * 用户维度评价信息
     */
    private UserReviewM userReview;


    /**
     * 综合分数
     */
    private double compositeScore;

    /**
     * 适用门店描述
     */
    private String applyShopsDesc;

    /**
     * 最近门店描述
     */
    private String nearestShopDesc;

    /**
     * 最近门店距离信息
     */
    private double nearestShopDistance;

    /**
     * 商品关联门店
     */
    private List<ShopM> shopMs;

    /**
     * 标签列表
     */
    private List<TagM> productTagList;

    /**
     * 商品本身售卖或者使用的开始时间
     */
    private long beginDate;

    /**
     * 商品本身售卖或者使用的终止时间
     */
    private long endDate;

    /**
     * 订单用户信息列表
     */
    private List<OrderUserM> orderUsers;

    /**
     * 扩展商品图片信息
     */
    private List<ExtendImageM> extendImages;

    /**
     * 是否为太极团单
     */
    private boolean isUnifyProduct;

    /**
     * 团购交易类型
     */
    private Integer tradeType;

    /**
     * 团单使用规则
     */
    private UseRuleM useRuleM;

    /**
     * 到手价
     */
    private BigDecimal salePrice;

    /**
     * 多次卡召回标识
     */
    private boolean timesDealQueryFlag;

    /**
     * 关联标准spuID
     */
    private String sptSpuId;

    private List<String> skuIdList;

    private List<ItemUnitM> itemUnitList;

    private List<DealProductMaterialM> materialList;

    /**
     * 商品的最低价格skuid
     */
    private Long lowPriceSkuId;

    /**
     * 加项列表
     */
    private List<DealAdditionalProjectM> additionalProjectList;

    /**
     * sku列表
     */
    private List<ProductSkuM> productSkuList;

    /**
     * *是否预填充过
     */
    private boolean prePadded;

    /**
     * 结构化内容的服务项目
     */
    private DealDetailStructuredDTO dealDetailStructuredDTO;

    /**
     * 标准服务项目
     */
    private DealStandardServiceProjectDTO standardServiceProjectDTO;

    /**
     * 团购次卡C端表达优化新老样式控制开关
     */
    private boolean expressOptimize;

    /**
     * 团购次卡C端表达优化,营销标签,例如: 性价比高 或者 先用后付 优先级:先用后付 > 性价比高
     */
    private int promoTagType;

    /**
     * EXPOSED用户可先用后付
     * EXPOSED：建议曝光，UNEXPOSED：建议不曝光
     */
    private String exposure;

    /**
     * 根据属性名获取属性值
     *
     * @param attrName
     * @return
     */
    public String getAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> StringUtils.isNotBlank(attr.getName()) && attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null : attrM.getValue();
    }

    public List<String> getAttrList(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> StringUtils.isNotBlank(attr.getName()) && attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null : attrM.getValueList();
    }

    public List<String> getTotalAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        List<AttrM> attrList = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attrList)) {
            return Lists.newArrayList();
        }
        return attrList.stream().map(AttrM::getValue).collect(Collectors.toList());
    }

    /**
     * 填充属性值
     *
     * @param attrName
     * @return
     */
    public void setAttr(String attrName, String attrValue) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            this.setExtAttrs(Lists.newArrayList(new AttrM(attrName, attrValue, null)));
            return;
        }
        this.getExtAttrs().add(new AttrM(attrName, attrValue, null));
    }

    public <T> T getObjAttrWithDefault(String attrName, T defaultValue) {
        if (CollectionUtils.isEmpty(this.getExtObjAttrs())) {
            return defaultValue;
        }
        ObjAttrM attrM = this.getExtObjAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? defaultValue : (T) attrM.getValue();
    }

    public <T> T getObjAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtObjAttrs())) {
            return null;
        }
        ObjAttrM attrM = this.getExtObjAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        if (attrM == null) {
            return null;
        }
        return (T) attrM.getValue();
    }


    public void setObjAttr(String attrName, Object attrValue) {
        if (CollectionUtils.isEmpty(this.getExtObjAttrs())) {
            this.setExtObjAttrs(Lists.newArrayList(new ObjAttrM(attrName, attrValue)));
            return;
        }
        this.getExtObjAttrs().add(new ObjAttrM(attrName, attrValue));
    }

    /**
     * 根据优惠类型获取优惠价格
     *
     * @param promoType
     * @return
     */
    public ProductPromoPriceM getPromo(int promoType) {
        if (CollectionUtils.isEmpty(this.getPromoPrices())) {
            return null;
        }
        return this.getPromoPrices().stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == promoType).findFirst().orElse(null);
    }

    /**
     * 根据活动类型获取活动
     *
     * @param activityType
     * @return
     */
    public ProductActivityM getActivity(int activityType) {
        if (CollectionUtils.isEmpty(this.getActivities())) {
            return null;
        }
        return this.getActivities().stream().filter(productActivityM -> productActivityM.getActivityType() == activityType).findFirst().orElse(null);
    }

    /**
     * 根据货架活动类型获取活动
     *
     * @param activityType
     * @return
     */
    public ProductActivityM getActivityByShelfActivityType(int activityType) {
        if (CollectionUtils.isEmpty(this.getActivities())) {
            return null;
        }
        return this.getActivities().stream().filter(productActivityM -> productActivityM.getShelfActivityType() != null && productActivityM.getShelfActivityType() == activityType).findFirst().orElse(null);
    }

    /**
     * 查询指定类型的优惠价格
     *
     * @param type
     * @return
     */
    public ProductPromoPriceM getPromoPrice(int type) {
        if (CollectionUtils.isEmpty(this.getPromoPrices())) {
            return null;
        }
        return this.promoPrices.stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == type).findFirst().orElse(null);
    }

    public long getActProductId() {
        if (getId() != null) {
            return getId().getId();
        }
        return Long.valueOf(this.productId);
    }

    public int getActProductType() {
        if (getId() != null) {
            return getId().getType();
        }
        return this.productType;
    }

    public long getDealSpuId() {
        if (getSpuM() == null) {
            return 0;
        }
        return getSpuM().getSpuId();
    }


    /**
     * @param attrKey {@link com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum}
     * @return
     */
    public DealProductSpuAttrDTO getSpuAttr(String attrKey) {
        if (Objects.isNull(spuM) || CollectionUtils.isEmpty(spuM.getAttrList())) {
            return null;
        }
        return spuM.getAttrList().stream()
                .filter(item -> Objects.nonNull(item.getName()) && item.getName().equals(attrKey)).findFirst()
                .orElse(null);
    }

    /**
     * 是否为团购次卡
     */
    public boolean isTimesDeal() {
        // 19-团购次卡
        return Objects.equals(tradeType, 19);
    }

    /**
     * 是否为加项团单
     */
    public boolean isAdditionalDeal() {
        return NumberUtils.toInt(getAttr(AdditionalDealUtils.ADDITIONAL_PROJECT_COUNT)) > 0;
    }

    @JsonIgnore // 优化性能，禁止序列化时将此方法作为属性加载
    public List<Integer> getRelatedTimesDeal() {
        String dealRelatedTimesDealAttr = getAttr("dealRelatedTimesDealAttr");
        if (StringUtils.isBlank(dealRelatedTimesDealAttr)) {
            return Lists.newArrayList();
        }
        return JsonCodec.decode(dealRelatedTimesDealAttr, new TypeReference<List<Integer>>() {
        });
    }

    /**
     * 按照以下优先级取值
     * 团单自身的attr > spu的attr > 标准服务项目attr > 结构化服务项目attr
     * @param attrName
     * @return 可能是String，或者List<String>
     */
    public Object getAttrValueFromAllAttrs(String attrName) {
        if (StringUtils.isBlank(attrName)) {
            return null;
        }
        Object result = getAttrAsObject(attrName);
        // 团单自身的attr
        if (result != null) {
            return getRealAttrValue(result);
        }
        // spu的attr
        DealProductSpuAttrDTO spuAttr = getSpuAttr(attrName);
        if (spuAttr != null) {
            return getRealAttrValue(spuAttr.getValueStr());
        }
        // 标准服务项目
        String standardAttrResult = DealAttrUtils.getAttrFromStandardServiceProject(standardServiceProjectDTO,attrName);
        if (StringUtils.isNotBlank(standardAttrResult)) {
            return getRealAttrValue(standardAttrResult);
        }
        // 结构化服务项目
        return getRealAttrValue(DealAttrUtils.getAttrFromDealDetailStructured(dealDetailStructuredDTO,attrName));
    }

    private Object getRealAttrValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            // 部分情况下，读取到的值是List的Json，反转回List<String>
            if (DealAttrUtils.isListJson((String)value)) {
                return JsonCodec.decode((String)value, new TypeReference<List<String>>(){});
            }
            return value;
        }
        return value;
    }

    private Object getAttrAsObject(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> StringUtils.isNotBlank(attr.getName()) && attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        if (attrM == null) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(attrM.getValueList())) {
            return attrM.getValueList();
        }
        return attrM.getValue();
    }

}
