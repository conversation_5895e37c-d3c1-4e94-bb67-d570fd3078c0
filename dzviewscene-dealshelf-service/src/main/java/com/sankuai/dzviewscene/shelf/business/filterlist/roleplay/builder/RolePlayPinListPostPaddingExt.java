package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/12 5:24 下午
 */
@ExtPointInstance(name = "剧本杀-在拼场次列表页和在拼场次浮层商品填充后扩展点")
public class RolePlayPinListPostPaddingExt extends PaddingFetcherExtAdapter {

    @Override
    public void postPadding(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.RolePlayPinListPostPaddingExt.postPadding(ActivityContext,String,ProductGroupM)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return;
        }

        filterProductCanNotJoinPinPool(productGroupM.getProducts());
    }

    private void filterProductCanNotJoinPinPool(List<ProductM> products) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.builder.RolePlayPinListPostPaddingExt.filterProductCanNotJoinPinPool(java.util.List)");
        for (ProductM productM: products) {
            List<ProductPinPoolM> canJoinProductPinPools = productM.getPinPools().stream().filter(productPinPoolM -> productPinPoolM.isCanJoin()).collect(Collectors.toList());
            productM.setPinPools(canJoinProductPinPools);
        }
    }
}
