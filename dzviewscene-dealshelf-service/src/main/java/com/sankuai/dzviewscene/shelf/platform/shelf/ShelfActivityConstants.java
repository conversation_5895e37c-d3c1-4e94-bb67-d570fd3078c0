package com.sankuai.dzviewscene.shelf.platform.shelf;

import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfSubScene;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;

/**
 * 注：整个应用上下文的常量，而非限定货架活动，因为存在能力间的复用
 * Created by float on 2020/8/22.
 */
public interface ShelfActivityConstants {

    /**
     * 参数名定义列表
     */
    interface Params {

        /**
         * 1. 平台
         */
        String platform = "platform";

        /**
         * 2. 城市ID
         */
        String dpCityId = "dpCityId";

        /**
         * 3. 点评城市
         */
        String mtCityId = "mtCityId";

        /**
         * 4. 点评侧门店ID
         */
        @Deprecated
        String dpPoiId = "dpPoiId";

        /**
         * 5. 美团侧门店ID
         */
        @Deprecated
        String mtPoiId = "mtPoiId";

        /**
         * 6. 客户端类型
         */
        String clientType = "clientType";

        /**
         * 7.1 点评用户ID
         */
        String dpUserId = "dpUserId";

        /**
         * 7.2 美团用户实ID
         */
        String mtUserId = "mtUserId";

        /**
         * 7.3 美团用户虚ID，用于点评侧，美团侧为空
         */
        String mtVirtualUserId = "mtVirtualUserId";

        /**
         * 8. 当前选中的筛选标签ID
         */
        String selectedFilterId = "selectedFilterId";

        /**
         * 9. 设备ID
         */
        String deviceId = "deviceId";

        /**
         * 10. unionID
         */
        String unionId = "unionId";

        /**
         * 11. 搜索关键词
         */
        String keyword = "keyword";

        /**
         * 12. 页大小
         */
        String pageSize = "pageSize";

        /**
         * 13. 第几页
         */
        String pageNo = "pageNo";

        /**
         * 14. shopuuid
         */
        String shopUuid = "shopuuid";

        /*
         * 15. 场景ID
         */ String sceneCode = "sceneCode";

        /**
         * 16. UA
         */
        String userAgent = "userAgent";

        /**
         * 17. trace参数
         */
        String traceMark = "_activity_trace";

        /**
         * 18. trace参数
         */
        String appVersion = "appVersion";

        /**
         * 19. 经纬度
         */
        String lat = "lat";
        String lng = "lng";

        /**
         * 20. 请求来源参数字段, 用于区分是筛选接口还是商品列表接口
         */
        String channel = "channel";

        /**
         * 21. 密室sku型货架，需要传递spuId
         */
        String productId = "productId";

        /**
         * 22. 传递productItemId
         */
        String productItemId = "productItemId";

        /**
         * 22. 密室sku型货架，需要传递日期
         */
        String selectDate = "selectDate";

        /**
         * 23. 模块名
         */
        String moduleName = "moduleName";

        /**
         * 24.【召回参数，可选】扩展信息
         */
        String extra = "extra";

        /**
         * 选择的筛选条件参数，逗号分隔
         */
        String selectedFilterParams = "selectedFilterParams";

        /**
         * 召回商品列表关联的筛选标签ID
         */
        String recalledFilterId = "recalledFilterId";

        /**
         * 经纬度坐标类型，如GCJ02
         */
        String coordType = "coordType";

        /**
         * 当前平台用户定位城市id
         */
        String locationCityId = "locationCityId";

        /**
         * 用户定位城市id
         */
        String mtLocationCityId = "mtLocationCityId";

        /**
         * 立减的商品类型
         */
        String promoProductType = "promoProductType";

        /**
         * 3.通用销量统计平台 1-点评单平台，2-美团单平台，其它默认双平台
         */
        String salePlatforms = "salePlatforms";
        /**
         * 点评指定策略ID名
         */
        String DP_EXP_ID = "DP_EXP_ID";

        /**
         * 美团指定策略ID名
         */
        String MT_EXP_ID = "MT_EXP_ID";

        /**
         * 标品ID，String，逗号分隔
         */
        String spuIds = "spuIds";

        /**
         * 货架类型列表
         */
        String shelfType = "shelfType";

        /**
         * 实体ID，比如：检测报告ID，标品Id，等召回/路由所需要的主体识别 Id
         */
        String entityId = "entityId";
        /**
         * 商品ID列表
         */
        String productIdList = "productIdList";
        /**
         * 案例id
         */
        String nailexhibitid = "nailexhibitid";

        /**
         * 算法召回业务标识
         */
        String algorithmQueryBizId = "algorithmQueryBizId";

        /**
         * 购买日期
         */
        String purchaseDate = "purchasedate";

        /**
         * 门店点评城市id
         */
        String shopDpCityId = "shopDpCityId";

        /**
         * 门店美团城市id
         */
        String shopMtCityId = "shopMtCityId";

        /**
         * 置顶商品ID列表，格式：id列表，逗号分隔
         */
        String topProductIds = "topProductIds";

        /**
         * 下挂商品Id 【新】
         * Ex：{"deal":"1,2,3","spu":"4,5,6"}
         * deal - 团单， spu - 泛商品
         * 解析工具如下：
         * {@link ParamUtil#getSummaryDealIds(java.lang.String, java.lang.String)}
         */
        String summaryProductIds = "summarypids";

        /**
         * 用户选中/飘红的商品，需要强制的置顶
         */
        String anchorGoodId = "anchorgoodid";

        /**
         * 上游商品类型
         */
        String bizType = "biztype";

        /**
         * tab锚定关键字
         */
        String searchKeyword = "searchkeyword";

        /**
         * 搜索词是否品牌词
         */
        String isBrandKeyWord = "isbrandKeyword";

        /**
         * 能力Code-class 映射
         * key - code
         * value - class
         */
        String abilityClassMap = "abilityClassMap";

        /**
         * key - code
         * value - class
         * 扩展点 Code-Class 映射
         */
        String extPointClassMap = "extPointClassMap";

        /**
         * 斗斛实验结果
         * value：List<DouHuM>
         */
        String douHus = "douHus";

        /**
         * 斗斛请求构造（无实验id）用于给新框架场景识别使用
         * value：DouHuRequest 对象
         */
        String douHuRequest = "douHuRequest";

        /**
         * matrix实验结果
         * value：List<MatrixExperimentM>
         */
        String matrixExps = "matrixExps";

        /**
         * 子场景{@link DealShelfSubScene}
         */
        String subScene = "subScene";

        /**
         * 展位 Code
         */
        String spaceKey = "spaceKey";
        /**
         * 用户搜索的关键词
         */
        String searchterm = "searchterm";

        /**
         * 货架模块版本，由前端维护
         */
        String shelfVersion = "shelfVersion";

        /**
         * 点评侧long类型门店ID
         */
        String dpPoiIdL = "dpPoiIdL";

        /**
         * 美团侧long类型门店ID
         */
        String mtPoiIdL = "mtPoiIdL";

        /**
         * MTSI反爬标识
         */
        String mtSIFlag = "mtSIFlag";

        /**
         * MTSI反爬标识
         */
        String mtgsig = "mtgsig";

        /**
         * 风控参数json
         */
        String riskParam = "riskParam";

        /**
         * 页面来源，用于跳转链接额外信息拼接，前端透传
         */
        String pageSource = "pageSource";

        String pageSourceNew = "pageSourceNew";

        /**
         * 货架里，商家的全部商品数量（首屏、非首屏的数量应该是一致的）
         */
        String totalProductNum = "totalProductNum";

        /**
         * 商品组默认展示数量动态变更值
         */
        String dynamicsGroupNameShowNum = "dynamicsGroupNameShowNum";

        /**
         * 商品ID和卡ID对象映射
         * 用于适配卡ID对象没有商品ID的情况
         */
        String productId2CardIdMap = "productId2CardIdMap";

        /**
         * 商品与对应商户映射
         */
        String product2ShopIds = "product2ShopIds";

        /**
         * 卡ID和商品ID对象映射
         * 用于适配卡ID对象没有商品ID的情况
         */
        String cardId2ProductIdMap = "cardId2ProductIdMap";

        /**
         * 货架上方是否有直播模块
         */
        String isLiveShowExist = "isLiveShowExist";

        /**
         * 门店类型
         */
        String useType = "useType";

        /**
         * 团单id和商户id映射关系
         */
        String dealId2ShopId = "dealId2ShopId";
        String dealId2ShopIdForLong = "dealId2ShopIdForLong";

        /**
         * 猜喜界面传来的商品，可能是到综商品、到餐商品
         */
        String recommendinfo = "recommendinfo";

        /**
         * 价格一致率透传加密字符串
         */
        String pricecipher = "pricecipher";
        /**
         * 是否匹配zdc标签
         */
        String matchZdcTag = "matchZdcTag";

        /**
         *  门店参与“春不”+当天营业
         */
        String springFestivalNoClose = "springFestivalNoClose";

        /**
         * 点位
         */
        String position = "position";

        /**
         * 刷新标志
         */
        String refreshTag = "refreshTag";

        /**
         * 自定义参数信息
         * Map<String, String>的JSON字符串
         */
        String customInfo = "customInfo";

        /**
         * 活动时间过滤:1 根据当前时间进行过滤
         */
        String activityFilterBizType = "activityFilterBizType";

        /**
         * 传入的筛选id原始值，selectedFilterId在逻辑处理中会被替换
         */
        String selectedOriginFilterId = "selectedOriginFilterId";

        /**
         * 货架是否分页
         */
        String pagination = "pagination";

        /**
         * 到家regionId
         */
        String wttRegionId = "wttRegionId";

        /**
         * 是否拦截货架返回
         */
        String interceptShelf = "interceptShelf";

        /**
         * 跳过筛选
         */
        String skipFilter = "skipFilter";

        /**
         * 执行开始时间
         */
        String startTime = "startTime";

        /**
         * 请求类型
         */
        String requestType = "requestType";

        /**
         * 是否命中优惠减负
         */
        String promoSimplify = "promoSimplify";

        /**
         * 屏幕高度，用于RCF
         */
        String deviceHeight = "deviceHeight";
        /**
         * 优惠码额外信息
         */
        String promoCodeExtraInfo = "promoCodeExtraInfo";

        /**
         * 门店是否有在线商品
         */
        String hasShopDeal = "hasShopDeal";

        /**
         * appId https://km.sankuai.com/collabpage/2104641030
         */
        String appId = "appId";

        /**
         * 微信的OpenId
         * 因为下游不确定复用deviceId会有什么问题，所以独立出来字段
         */
        String openId = "openId";

        /**
         * 运营配置预览标识
         */
        String operatorPreviewConfigTags = "operatorPreviewConfigTags";

        /**
         * 模拟的斗斛实验结果，一般用于AI配置预览
         */
        String mockDouHuResult = "mockDouHuResult";

        /**
         * 是否双列货架，用于货架单双列共存场景, 1表示是
         */
        String doubleColumnShelf = "doubleColumnShelf";

        /**
         * 是否F型货架
         */
        String isFShelf = "isFShelf";

        /**
         * 渠道来源
         */
        String channelType = "channelType";
        /**
         * 提单页参数 订单流量标识
         */
        String orderTrafficFlag = "orderTrafficFlag";
        /**
         * 门店是否支持自动核销
         */
        String shopAutoVerify = "shopAutoVerify";
        /**
         * 职人货架是否降级
         */
        String staffShelfDegrade = "staffShelfDegrade";
    }

    interface Style {

        /**
         * 1. 货架样式
         */
        String showType = "showType";

        /**
         * 1. 货架交互类型
         */
        String interactType = "interactType";
    }

    interface Ctx {
        /**
         * 上下文中门店
         */
        String ctxShop = "ctxShop";

        /**
         * 上下文中旗舰店信息
         */
        String ctxFlagshipStore = "ctxFlagshipStore";

        /**
         * 上下文中推荐流程唯一id
         */
        String ctxRecommendFlowId = "ctxRecommendFlowId";

        /**
         * 上下文中旗舰店对应的跳转链接
         */
        String ctxFlagshipStoreUrl = "ctxFlagshipStoreUrl";

        /**
         * 上下文中的标记，本次请求需要刷新运营配置
         */
        String needFlashOperatorConfigFlag = "needFlashOperatorConfigFlag";
    }

    /**
     * 暂存结果key定义列表
     */
    interface Attachments {
        /**
         * 召回结果暂存KEY
         */
        String productGroups = "groupNames";

        /**
         * 团详页-多产品属性key
         */
        String products = "products";
        /**
         * 优惠码货架配置
         */
        String promoCodeShelfConfig = "promoCodeShelfConfig";
    }

    /**
     * 来源类型
     */
    interface ChannelType {
        /**
         * 预订筛选来源
         */
        String bookingFilter = "bookingFilter";

        /**
         * 预订商品列表来源
         */
        String bookingProducts = "bookingProducts";

        /**
         * 泛商品筛选来源
         */
        String productFilter = "productFilter";

        /**
         * 泛商品列表来源
         */
        String productProducts = "productProducts";
        /**
         * 泛商品列表来源
         */
        String filterListRPC = "filterListRPC";

        /**
         * 团购货架首次加载
         */
        String dealShelfFirstLoad = "dealShelfFirstLoad";

        /**
         * 团购货架点击Tab获取List
         */
        String dealShelfListForTab = "dealShelfListForTab";

        /**
         * 团购筛选列表首次加载
         */
        String dealFilterList = "dealFilterList";

        /**
         * 团购列表点击筛选获取列表
         */
        String dealList = "dealList";


    }
}
