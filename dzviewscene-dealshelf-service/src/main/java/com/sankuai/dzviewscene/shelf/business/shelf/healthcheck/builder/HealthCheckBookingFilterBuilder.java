package com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.springframework.stereotype.Component;

@Component
public class HealthCheckBookingFilterBuilder extends FilterBuilderExtAdapter {

    private static final int MIN_SHOW_NUM = 2;


    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFilterBuilder.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return MIN_SHOW_NUM;
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.healthcheck.builder.HealthCheckBookingFilterBuilder.titleButton(ActivityContext,String,FilterBtnM)");
        return new RichLabelVO(13, ColorUtils.color111111, filterBtnM.getTitle());
    }
}
