package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@VPoint(name = "聚合商卡特色标签", description = "聚合商卡特色标签", code = AggregateSpecialTagVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class AggregateSpecialTagVP<T> extends PmfVPoint<ItemSpecialTagVO, AggregateSpecialTagVP.Param, T> {

    public static final String CODE = "AggregateSpecialTagVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductHierarchyNodeM nodeM;

        private Map<String, ProductM> productMMap;
    }
}