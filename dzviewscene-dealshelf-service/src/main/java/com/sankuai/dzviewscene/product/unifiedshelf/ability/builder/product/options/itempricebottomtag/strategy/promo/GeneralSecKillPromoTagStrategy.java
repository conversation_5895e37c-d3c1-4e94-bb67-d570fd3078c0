package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 限时秒杀策略，来源商家自助秒杀
 * 优惠减负秒杀收敛
 */
@Component
public class GeneralSecKillPromoTagStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private static final String PROMO_NAME = "限时秒杀";

    @Override
    public String getName() {
        return "限时秒杀";
    }

    @Override
    public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
        //未命中优惠减负二期实验组，直接返回空，全量后去掉
        if(!PromoSimplifyUtils.hitPromoSimplifyV2(req.getContext())){
            return null;
        }
        if(!DealSecKillUtils.hasSecKillPromo(req.getProductM())){
            return null;
        }
        ProductPromoPriceM promoPriceM = req.getProductM().getBestPromoPrice();
        ShelfTagVO secKillTag = new ShelfTagVO();
        secKillTag.setName(PROMO_NAME);
        secKillTag.setText(buildCommonTagText(getPromoText(promoPriceM, req.getProductM(), req.getSalePrice()), "限时秒杀", req.isHiddenPreText()));
        secKillTag.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(promoPriceM));
        addAfterPic(secKillTag, getCommonAfterPic(req.getPlatform()));
        return secKillTag;
    }

    private String getPromoText(ProductPromoPriceM promoPriceM, ProductM productM, String salePrice){
        //计算优惠金额
        BigDecimal promoPrice;
        if (StringUtils.isNotEmpty(productM.getMarketPrice()) && StringUtils.isNotEmpty(salePrice)) {
            promoPrice = new BigDecimal(productM.getMarketPrice()).subtract(new BigDecimal(salePrice));
        } else {
            promoPrice = promoPriceM.getTotalPromoPrice();
        }
        return "共省" + promoPrice.stripTrailingZeros().toPlainString();
    }
}
