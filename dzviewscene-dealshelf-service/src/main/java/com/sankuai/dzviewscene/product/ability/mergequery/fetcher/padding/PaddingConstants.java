package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding;

import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;

public class PaddingConstants {
    /**
     * 能力接收的参数名
     */
    public interface Params {
        /**
         * 1.【二级商品组参数, 必填】方案ID
         */
        String planId = "planId";

        /**
         * 2.【二级商品组参数, 可选】当前商品组的填充类型, 默认为泛商品填充
         * {@link PaddingType}
         */
        String paddingType = "paddingType";

        /**
         * 2.【二级商品组参数, 可选】当前商品组的后置填充类型, 默认为泛商品填充
         * {@link PaddingType}
         */
        String postPaddingType = "postPaddingType";

        /**
         * 2.【二级商品组参数, 可选】置顶一次填充数量，默认10
         */
        String paddingPartitionSize = "paddingPartitionSize";

        /**
         * 3.【二级商品组参数, 可选】当前商品组销量标识, 从泛商品渠道查询销量的时候需要使用
         */
        String statisticSaleBizType = "statisticSaleBizType";

        /**
         * 4.【商品二级类目ID，可选】优惠模版ID
         */
        String promoTemplateId = "promoTemplateId";

        /**
         * 5.【商品二级类目ID，可选】用于部分团单填充场景
         */
        String shopMainCategoryId = "shopMainCategoryId";

        /**
         * 6.【二级商品组参数, 可选】用于泛商品渠道查询优惠立减
         */
        String promoProductType = "promoProductType";

        /**
         * 7.【二级商品组参数, 可选】密室拼场结束提前量
         */
        String poolClosingTime = "poolClosingTime";
        /**
         * 8.【二级商品组参数, 可选】用于搜索商品渠道查询区间库存
         */
        String showTimeInterval = "showTimeInterval";
        /**
         * 9.【二级商品组参数, 可选】用于搜索商品渠道查询区间库存
         */
        String beginTime = "beginTime";
        /**
         * 10.【二级商品组参数, 可选】用于搜索商品渠道查询区间库存
         */
        String endTime = "endTime";
        /**
         * 11.【商品属性，可选】需要透传的商品的attr属性
         */
        String productAttributeKeys = "productAttributeKeys";
        /**
         * 12.【商品Item属性，可选】需要透传的ItemAttr属性
         */
        String itemAttributeKeys = "itemAttributeKeys";
        /**
         * 13.【可选】查询用户收藏信息时，传入的商品类型
         */
        String dpCollectBizType = "dpCollectBizType";
        /**
         * 14.【可选】分片填充参数
         */
        String batchSize = "batchSize";
        /**
         * 15.【可选】商户Category
         */
        String shopCategory = "shopCategory";
        /**
         * 16.【二级商品组参数，可选】商品组缓存刷新方案ID，仅用于需要查询缓存的商品组
         */
        String refreshPlanId = "refreshPlanId";
        /**
         * 17.【二级商品组参数，可选】商品组缓存刷新填充方式，仅用于需要查询缓存的商品组
         */
        String refreshPaddingType = "refreshPaddingType";

        /**
         * 18.【商品二级类目ID，可选】价格场景信息，@see com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum
         */
        String pricePromoScene = "pricePromoScene";

        /**
         * 19. 【次卡返回类型，可选】1:优先返回正常次卡 2：优先返回升级卡，默认1
         * @see com.sankuai.dzcard.navigation.api.enums.ExtraParamsEnum
         */
        String timesCardReturnType = "timesCardReturnType";

        /**
         * 20. 【剧本杀预订价格周期，可选】
         */
        String calPriceDays = "calPriceDays";

        /**
         * 21. 【剧本杀预订价格选择时间，可选】
         */
        String selectDate = "selectDate";

        /**
         * 22. 【剧本杀密室预订商家推荐标签，可选】
         */
        String shopRecommendScene = "shopRecommendScene";

        /**
         * 23. 【算价服务商品类型，可选】
         */
        String priceProductType = "priceProductType";

        /**
         * 24. 【标品填充信息列表，可选】
         */
        String resourceRelatedInfoList = "resourceRelatedInfoList";

        /**
         * 1.【二级商品组参数】到综内容场景id
         */
        String dzContentSceneId = "dzContentSceneId";

        /**
         * 1.【二级商品组参数】标品信息填充数据源
         */
        String spuSaleSource = "spuSaleSource";

        /**
         * 1.【二级商品组参数】需要查询的标品类型
         */
        String resourceType = "resourceType";

        /**
         * 1.【二级商品组参数】标品总销量缓存过期时间
         */
        String spuTotalSaleExpiredTime = "spuTotalSaleExpiredTime";

        /**
         *【二级商品组参数】标品评分评论数量缓存过期时间
         */
        String spuReviewDataExpiredTime = "spuReviewDataExpiredTime";

        /**
         *【二级商品组参数】查询价格开始时间
         */
        String queryPriceStartTime = "queryPriceStartTime";

        /**
         * 1.【二级商品组参数】查询标品&商品榜单信息场景code
         */
        String rankSceneCode = "rankSceneCode";
        /**
         * 24. 推荐召回业务参数，适用于推荐召回
         */
        String recommendBizParam = "recommendBizParam";

        /**
         *【二级商品组参数】查询价格开始时间
         * @see com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum
         */
        String queryPriceScene = "scene";

        /**
         * 2.【二级商品组参数, 可选】当前商品组的填充的商品类型, 默认为团购填充
         * {@link PaddingType.type}
         */
        String paddingProductType = "paddingProductType";
    }

    /**
     * 填充能力支持的填充类型枚举
     */
    public enum PaddingType {
        spuThemePadding(1001,"标品主题信息填充", ProductTypeEnum.GENERAL_SPU.getType()),
        noPaddingHandler(1002, "无填充", 0),
        dealThemePadding(1003, "团购主题信息填充", ProductTypeEnum.DEAL.getType()),
        combinationProductPadding(1004, "打包商品信息填充", ProductTypeEnum.PACK.getType()),
        timeCardProductPadding(1005, "次卡商品信息填充", ProductTypeEnum.TIME_CARD.getType()),
        generalProductPadding(1006, "泛商品信息填充", ProductTypeEnum.SPU.getType());

        public int code;
        public String name;
        /**
         * 商品类型 {@link ProductTypeEnum}
         * 注：必填，会影响填充
         */
        public int type;

        PaddingType(int code, String name, int type) {
            this.code = code;
            this.name = name;
            this.type = type;
        }

    }

    /**
     * 后置填充能力支持的填充类型枚举
     */
    public enum PostPaddingType {
        fixedPriceSpuPostPadding(2001, "一口价标品通用后置处理");

        public int code;
        public String name;

        PostPaddingType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    public enum ContextPaddingType {
        shopGuaranteeTagPaddingHandler(3001,"门店买贵必赔信息查询"),

        shopReservationCountPaddingHandler(3002,"门店预订订单量查询"),

        shopCustomerRecommendWordPaddingHandler(3003, "门店回头客推荐语查询"),

        shopQueryAnalyzeWordPaddingHandler(3004, "搜索Query意图解析"),

        lifeWashShopTimelinessPaddingHandler(3006,"家政保洁门店商家时效信息查询"),

        userAfterPayPaddingHandler(3007,"用户是否开启先用后付查询");

        public int code;
        public String name;

        ContextPaddingType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
