package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options;

import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding.ContextHandlerAbility;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.vp.UnifiedShelfMainTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.UnifiedShelfMainTitleBuilder.getTotalCount;

@VPointOption(name = "家政保洁预订货架主标题",
        description = "家政保洁预订货架主标题配置能力",
        code = "LifeWashReserveShelfMainTitleOpt")
public class LifeWashReserveShelfMainTitleOpt extends UnifiedShelfMainTitleVP<LifeWashReserveShelfMainTitleOpt.Config> {

    @Override
    public ShelfMainTitleVO compute(ActivityCxt context, Param param, Config config) {
        ShelfMainTitleVO shelfMainTitleVO = new ShelfMainTitleVO();
        if (config == null) {// 没有配置的时候默认走兜底逻辑
            return null;
        }
        shelfMainTitleVO.setTitle(config.getTitle());
        shelfMainTitleVO.setTotalProductQty(getTotalCount(context));
        String timeliness = getTimeliness(context, config.getTimelinessText());
        shelfMainTitleVO.setTags(buildTagList(config, timeliness));
        SupernatantVO supernatantVO = new SupernatantVO();
        supernatantVO.setTitle(config.getSupernatantTitle());
        supernatantVO.setContentList(buildSupernatantList(config, context));
        shelfMainTitleVO.setSupernatantVO(supernatantVO);
        return shelfMainTitleVO;
    }

    /**
     * 主标题构造 预订必上门·上门前3小时随时退·{商家确认时效}
     *
     * @param config
     * @param timeliness
     * @return
     */
    private List<IconRichLabelModel> buildTagList(Config config, String timeliness) {
        List<IconRichLabelModel> tagList = Lists.newArrayList();
        String tagText = config.getTagText1();
        if (StringUtils.isNotEmpty(timeliness)) {
            tagText = tagText + "·" + timeliness;
        }
        tagList.add(buildTag(config.getTagIcon1(), tagText, IconRichLabelTypeEnum.ICON_TEXT.getType()));
        tagList.add(buildTag(config.getTagIcon2(), "", IconRichLabelTypeEnum.ICON.getType()));
        return tagList;
    }

    private IconRichLabelModel buildTag(String tagIcon, String tagText, int type) {
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        iconRichLabelModel.setIcon(buildPictureLabel(tagIcon));
        iconRichLabelModel.setText(buildRichLabel(tagText));
        iconRichLabelModel.setType(type);
        return iconRichLabelModel;
    }

    private PictureModel buildPictureLabel(String tagIcon) {
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(tagIcon);
        return pictureModel;
    }

    private RichLabelModel buildRichLabel(String tagText) {
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(tagText);
        return richLabelModel;
    }

    /**
     * 构造浮层文案
     *
     * @param config
     * @param
     * @return
     */
    private List<SupernatantAreaVO> buildSupernatantList(Config config, ActivityCxt context) {
        List<SupernatantAreaVO> tagList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(config.getAreaTitle1())){
            tagList.add(buildAreaTag(config.getAreaTagIcon(), config.getAreaTitle1(), IconRichLabelTypeEnum.TEXT.getType(), config.getAreaText1()));
        }
        if (CollectionUtils.isNotEmpty(config.getAreaText2())) {
            tagList.add(buildAreaTag(config.getAreaTagIcon(), config.getAreaTitle2(), IconRichLabelTypeEnum.TEXT.getType(), config.getAreaText2()));
        }
        String timeliness = getTimeliness(context, config.getAreaText3());
        if (StringUtils.isNotEmpty(timeliness)) {
            tagList.add(buildAreaTag(config.getAreaTagIcon(), config.getAreaTitle3(), IconRichLabelTypeEnum.TEXT.getType(), Lists.newArrayList(timeliness)));
        }
        return tagList;
    }

    private SupernatantAreaVO buildAreaTag(String tagIcon, String tagText, int type, List<String> areaText) {
        SupernatantAreaVO supernatantAreaVO = new SupernatantAreaVO();
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        iconRichLabelModel.setIcon(buildPictureLabel(tagIcon));
        iconRichLabelModel.setText(buildRichLabel(tagText));
        iconRichLabelModel.setType(type);
        supernatantAreaVO.setHeadLabelModel(iconRichLabelModel);
        supernatantAreaVO.setAreaTextList(areaText);
        return supernatantAreaVO;
    }

    private String getTimeliness(ActivityCxt context, List<String> stringList) {
        ContextHandlerResult contextHandlerResult = context.getSource(ContextHandlerAbility.CODE);
        if (contextHandlerResult == null || CollectionUtils.isEmpty(stringList) || stringList.size() < 3) {
            return null;
        }
        long avgAcceptOderTime = contextHandlerResult.getAvgAcceptOderTime();
        if (avgAcceptOderTime <= 0) {
            return null;
        } else if (avgAcceptOderTime <= 300) {
            return stringList.get(0);
        } else if (avgAcceptOderTime <= 900) {
            return stringList.get(1);
        } else {
            return stringList.get(2);
        }
    }


    @VPointCfg
    @Data
    public static class Config {

        private String title = "预订";

        private String tagIcon1 = "https://p0.meituan.net/ingee/532058a3a9762dbde44c34155d5b18b61269.png";
        private String tagText1 = "预订必上门·上门前3小时随时退";
        private String tagIcon2 = "https://p0.meituan.net/ingee/b9c69b35a9af93532b84146ea00e1e1a481.png";

        private List<String> timelinessText = Lists.newArrayList("较快确认", "预计15分钟接单", "预计30分钟接单");

        private String supernatantTitle = "预订说明";

        private String areaTitle1 = "预订必上门";

        private String areaTagIcon = "https://p0.meituan.net/ingee/03eeb9ab4ca0a3ad9b1ea63600c953261435.png";

        private List<String> areaText1 = Lists.newArrayList("预订后会安排工作人员上门服务，上门时会主动出示工作证件。");

        private String areaTitle2 = "上门前3小时随时退";

        private List<String> areaText2 = Lists.newArrayList("预订后服务前3小时可随时退款，3小时后需联系商户取消并协商退款。");

        private String areaTitle3 = "商家确认时效";

        private List<String> areaText3 = Lists.newArrayList("近期大多数订单可在5分钟内确认是否预订成功。", "近期大多数订单可在15分钟内确认是否预订成功。", "近期大多数订单可在30分钟内确认是否预订成功。");

    }
}
