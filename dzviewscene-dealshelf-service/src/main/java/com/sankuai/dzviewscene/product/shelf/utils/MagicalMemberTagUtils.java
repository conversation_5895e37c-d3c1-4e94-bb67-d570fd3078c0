package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.tpfun.product.api.sku.common.utils.GsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class MagicalMemberTagUtils {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.style.config", defaultValue = "{}")
    public static MagicalMemberStyleConfig magicalMemberStyleConfig;

    //普通神券pic
    public static String NORMAL_MAGICAL_PIC = "https://p0.meituan.net/ingee/297a8cd8c216c82636becd6552484f852335.png";
    //点评-普通神券pic
    public static String DP_NORMAL_MAGICAL_PIC = "https://p0.meituan.net/ingee/1f3c4c37d77155eb6f626005651772f32479.png";

    //带膨胀箭头神券pic
    public static String INFLATE_MAGICAL_PIC = "https://p0.meituan.net/ingee/ac3212d4f5f4c9bea474748518881f9d2912.png";
    //点评-带膨胀箭头神券pic
    public static String DP_INFLATE_MAGICAL_PIC = "https://p0.meituan.net/ingee/930cf8d0475112ed7ed39cf78a9a8c333177.png";

    //有背景普通神券pic
    public static String NORMAL_MAGICAL_PIC_WITH_BG = "https://p0.meituan.net/ingee/4149352c7cdbf9a25f2b6caef4af3d307925.png";
    //点评有背景普通神券pic
    public static String DP_NORMAL_MAGICAL_PIC_WITH_BG = "https://p0.meituan.net/ingee/23dd56c82d8c7526848f2ab7160562ac3037.png";
    //点评有背景无边框普通神券pic
    public static String DP_NORMAL_MAGICAL_PIC_WITH_BG_NO_BORDER = "https://p0.meituan.net/ingee/905aefe297968930bd426151647982c02990.png";

    //有背景带膨胀箭头神券pic
    public static String INFLATE_MAGICAL_PIC_WITH_BG = "https://p0.meituan.net/ingee/50ab86cddbaa88c7b1318b4f03c1f8df9886.png";
    //点评有背景带膨胀箭头神券pic
    public static String DP_INFLATE_MAGICAL_PIC_WITH_BG = "https://p0.meituan.net/ingee/e64c63863a53708d946af7c968ca36b23649.png";
    //点评有背景无边框带膨胀箭头神券pic
    public static String DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_BORDER = "https://p0.meituan.net/ingee/e9f141ac8130b6efdb945b5da2d5ee693583.png";
    //点评有背景无白边带膨胀箭头神券pic
    public static String DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER = "https://p0.meituan.net/ingee/6d95e2a9cfa461f495a6df7b6966430b3497.png";

    //神券红色后缀icon
    public static String NORMAL_MAGICAL_MEMBER_AFTER_ICON = "https://p0.meituan.net/travelcube/8c7df855074c9de13de683399abf69ae331.png";

    //点评神券红色后缀icon
    public static String DP_NORMAL_MAGICAL_MEMBER_AFTER_ICON = "https://p0.meituan.net/ingee/a225ff8989e4f5f5eca0922a8cecf10b372.png";

    //神券白色后缀icon
    public static String INFLATE_MAGICAL_MEMBER_AFTER_ICON = "https://p0.meituan.net/ingee/9cadedb5dbc83c3c1933493bc8869e9e198.png";

    //神券pic与共省间隔
    public static String MAGICAL_MEMBER_GAP_PIC = "https://p0.meituan.net/travelcube/07fc74d319ed1121692dc1b92923a57b671.png";
    //点评神券pic与共省间隔
    public static String DP_MAGICAL_MEMBER_GAP_PIC = "https://p0.meituan.net/ingee/448f2832b0de207bf0a16e065e5a4740518.png";

    public static int MAGICAL_PIC_HEIGHT = 16;

    public static int DP_MAGICAL_PIC_HEIGHT = 14;

    public static int DP_MAGICAL_PIC_HEIGHT_NEW = 15;

    public static int MAGICAL_AFTER_PIC_HEIGHT = 10;

    public static int DP_MAGICAL_AFTER_PIC_HEIGHT = 9;

    public static int MAGICAL_AFTER_PIC_RADIO = 1;

    public static double NORMAL_MAGICAL_PIC_RADIO = 1.5;

    public static double DP_NORMAL_MAGICAL_PIC_RADIO = 1.7143;

    public static double INFLATE_MAGICAL_PIC_RADIO = 2;

    public static double DP_INFLATE_MAGICAL_PIC_RADIO = 2.4643;

    public static double NORMAL_MAGICAL_PIC_WITH_BG_RADIO = 1.9375;

    public static double DP_NORMAL_MAGICAL_PIC_WITH_BG_RADIO = 2;

    public static double INFLATE_MAGICAL_PIC_WITH_BG_RADIO = 2.5;

    public static double DP_INFLATE_MAGICAL_PIC_WITH_BG_RADIO = 2.5938;

    public static double DP_INFLATE_MAGICAL_PIC_WITH_BG_NO_WHITE_BORDER_RADIO = 2.34375;

    public static double MAGICAL_GAP_PIC_RADIO = 0.5;

    public static String CANT_INFLATE_FORMAT = "已减%s";

    public static String INFLATE_STYLE_FORMAT = "最高膨胀至%s";

    public static String INFLATE_STYLE_APPEND = "膨胀";

    //PromotionPropertyEnum.MAGICAL_MEMBER_COUPON_LABEL
    public static String MAGICAL_MEMBER_COUPON_LABEL = "magicalMemberCouponLabel";

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.config1", defaultValue = "{}")
    public static MagicalMemberConfig magicalMemberConfig;

    public static List<String> getInflateText(ActivityCxt context, PromoItemM magicalItemM){
        if(magicalMemberStyleConfig == null || magicalItemM == null || MapUtils.isEmpty(magicalItemM.getPromotionOtherInfoMap())){
            return null;
        }
        //分
        int inflateAmount = NumberUtils.toInt(magicalItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue()));
        if(inflateAmount <= 0){
            return null;
        }
        BigDecimal inflateAmountDecimal = new BigDecimal(inflateAmount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return Lists.newArrayList(String.format(INFLATE_STYLE_FORMAT, inflateAmountDecimal.stripTrailingZeros().toPlainString()), INFLATE_STYLE_APPEND);
    }

    public static String getCantInflateText(PromoItemM magicalItemM){
        return String.format(CANT_INFLATE_FORMAT, magicalItemM.getAmount().stripTrailingZeros().toPlainString());
    }

    public static boolean hitMagicalMemberNewStyle(ActivityCxt context){
        if(magicalMemberStyleConfig == null){
            return false;
        }
        //VCClientTypeEnum，端限制
        int clientType = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
        if(CollectionUtils.isNotEmpty(magicalMemberStyleConfig.getClientType())
            && !magicalMemberStyleConfig.getClientType().contains(clientType)){
            return false;
        }
        return true;
    }

    public static boolean getCanInflateAndCanInflateMore(ActivityCxt context,Map<String, String> extendDisplayInfo) {
        if (MapUtils.isEmpty(extendDisplayInfo)){
            return true;
        }
        if (Objects.isNull(magicalMemberConfig)) {
            return true;
        }
        // 没有命中客户端保持愿逻辑
        if (!hitClientType(context)) {
            return true;
        }
        // 命中黑名单城市保持愿逻辑
        if (hitBlackCityId(context)) {
            return true;
        }
        //神券还可膨胀，但膨胀后能不能形成更优算价组合
        String magicalMemberCouponLabel = extendDisplayInfo.get(MAGICAL_MEMBER_COUPON_LABEL);
        if (StringUtils.isBlank(magicalMemberCouponLabel)) {
            return true;
        }
        MagicalMemberTagTextDTO magicalMemberTagTextDTO = GsonUtils.fromJson(magicalMemberCouponLabel, MagicalMemberTagTextDTO.class);
        if (Objects.isNull(magicalMemberTagTextDTO)) {
            return true;
        }
        return Objects.equals(magicalMemberTagTextDTO.getShowType(), MagicalMemberTagShowTypeEnum.INFLATED_IS_BETTER_MMC.getValue());
    }

    public static boolean hitExp(ActivityCxt context) {
        //以后下掉实验需要排出类目[主] 购物(379)-电子数码(649)//[主] 家居(600)-家用电器(604)见ShelfDouHuFetcher
        if (Objects.isNull(magicalMemberConfig)) {
            return false;
        }
        List<String> expSkWhitelist = magicalMemberConfig.getExpSkWhitelist();
        //实验限制
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, expSkWhitelist)) {
            return true;
        }
        return false;
    }

    public static boolean hitExp(ActivityContext context) {
        //以后下掉实验需要排出类目[主] 购物(379)-电子数码(649)//[主] 家居(600)-家用电器(604)见ShelfDouHuFetcher
        if (Objects.isNull(magicalMemberConfig)) {
            return false;
        }
        List<String> expSkWhitelist = magicalMemberConfig.getExpSkWhitelist();
        //实验限制
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, expSkWhitelist)) {
            return true;
        }
        return false;
    }

    public static boolean hitClientType(ActivityCxt context) {
        if (Objects.isNull(magicalMemberConfig)) {
            return false;
        }
        List<Integer> clientType = magicalMemberConfig.getClientType();
        // clientType限制
        int userAgent = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
        if (CollectionUtils.isNotEmpty(clientType) && clientType.contains(userAgent)) {
            return true;
        }
        return false;
    }

    public static boolean hitBlackCityId(ActivityCxt context) {
        int dpCityId = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.dpCityId);
        if (Objects.isNull(magicalMemberConfig)) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(magicalMemberConfig.getBlackDpCityIds()) && magicalMemberConfig.getBlackDpCityIds().contains(dpCityId)) {
            return true;
        }
        //配置-1，全量下掉
        if (CollectionUtils.isNotEmpty(magicalMemberConfig.getBlackDpCityIds()) && magicalMemberConfig.getBlackDpCityIds().contains(-1L)) {
            return true;
        }
        return false;
    }

    @Data
    public static class MagicalMemberStyleConfig{

        /**
         * 端限制
         */
        private List<Integer> clientType;
    }

    @Data
    public static class MagicalMemberConfig {

        /**
         * 价格前缀神会员样式文案
         */
        private String magicalMember;
        /**
         * 神会员最低货架版本
         */
        private int lowestShelfVersion;
        /**
         * 端限制 :200美团app
         */
        private List<Integer> clientType;
        /**
         * 实验白名单
         */
        private List<String> expSkWhitelist;
        /**
         * 黑名单城市
         */
        private List<Integer> blackDpCityIds;
    }

}
