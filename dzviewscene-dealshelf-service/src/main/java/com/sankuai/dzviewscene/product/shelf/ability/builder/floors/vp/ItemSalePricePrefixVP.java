package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @author: created by hang.yu on 2024/5/29 16:53
 */
@VPoint(name = "价格前缀", description = "价格前缀", code = ItemSalePricePrefixVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemSalePricePrefixVP<T> extends PmfVPoint<String, ItemSalePricePrefixVP.Param, T> {

    public static final String CODE = "ItemSalePricePrefixVP";

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.config1", defaultValue = "{}")
    public static MagicalMemberTagUtils.MagicalMemberConfig magicalMemberConfig;

    public static String getPricePrefix(ActivityCxt context, Param param) {
        //没有命中实验不展示
        if (!MagicalMemberTagUtils.hitExp(context)) {
            return null;
        }
        // 没有命中客户端不展示
        if (!MagicalMemberTagUtils.hitClientType(context)) {
            return null;
        }
        // 命中黑名单城市不展示
        if (MagicalMemberTagUtils.hitBlackCityId(context)) {
            return null;
        }
        int lowestShelfVersion = magicalMemberConfig.getLowestShelfVersion();
        String magicalMember = magicalMemberConfig.getMagicalMember();
        //shelfVersion限制
        if (!MagicMemberUtil.satisfiedShelfVersion(ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.shelfVersion), lowestShelfVersion)) {
            return null;
        }
        CardM cardM = context.getSource(CardFetcher.CODE);
        ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(param.getProductM(), cardM);
        //优惠组合无神券
        if (!DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)) {
            return null;
        }
        // 双列货架orF型不展示
        if (isDoubleColumnShelf(context) || ParamsUtil.getBooleanSafely(context.getParameters(), ShelfActivityConstants.Params.isFShelf)) {
            return null;
        }
        return magicalMember;
    }

    public static boolean isDoubleColumnShelf(ActivityCxt context) {
        int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
        //双列
        if (doubleColumnShelf == 1) {
            return true;
        }
        return false;
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private String timesDealSk;
    }

}
