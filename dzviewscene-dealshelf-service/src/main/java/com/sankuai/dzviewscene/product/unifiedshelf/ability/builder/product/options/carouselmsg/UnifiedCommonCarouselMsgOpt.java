/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-13
 * Project        :
 * File Name      : CommonItemCarouselMsgOpt.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.carouselmsg;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.CarouselBuilderContext;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemCarouselMsgVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-13
 * @since dzviewscene-dealshelf-home 1.0
 */
@VPointOption(
        name = "商品卡片轮播信息通用OPT",
        description = "",
        code = UnifiedCommonCarouselMsgOpt.CODE,
        isDefault = true
)
public class UnifiedCommonCarouselMsgOpt extends UnifiedShelfItemCarouselMsgVP<UnifiedCommonCarouselMsgOpt.Config> {

    public static final String CODE = "CommonItemCarouselMsgOpt";

    @Autowired
    private ButtonCarouselMsgStrategyFactory carouselMsgStrategyFactory;

    @Override
    public List<CarouselMsg> compute(ActivityCxt context, Param param, Config config) {
        if(Objects.isNull(param) || Objects.isNull(param.getProductM())
                || CollectionUtils.isEmpty(config.getCarouselStrategy())){
            return null;
        }
        // F型货架不展示轮播信息
        if (ParamsUtil.getBooleanSafely(context.getParameters(), ShelfActivityConstants.Params.isFShelf)) {
            return null;
        }
        List<CarouselMsg> results = Lists.newArrayList();
        CarouselBuilderContext builderContext = new CarouselBuilderContext();
        builderContext.setProductM(param.getProductM());
        builderContext.setEnableRecycleModification(config.isEnableRecycleModification());
        builderContext.setRecycleTextSaleUnit(config.getRecycleTextSaleUnit());
        builderContext.setRecycleTextSale(config.getRecycleTextSale());
        for(String strategyName : config.carouselStrategy){
            ButtonCarouselMsgStrategy carouselMsgStrategy = carouselMsgStrategyFactory.getStrategy(strategyName);
            if(Objects.isNull(carouselMsgStrategy)){
                continue;
            }
            CarouselMsg carouselMsg = carouselMsgStrategy.unifiedBuild(builderContext);
            if(Objects.nonNull(carouselMsg)){
                results.add(carouselMsg);
            }
        }
        return results;
    }


    @VPointCfg
    @Data
    public static class Config {

        //默认是购买信息及销量
        private List<String> carouselStrategy = Lists.newArrayList("SaleMsgStrategy","PurchaseMsgStrategy");

        private boolean enableRecycleModification = false;

        private String recycleTextSaleUnit = "已";

        private String recycleTextSale = "预约";
    }
}
