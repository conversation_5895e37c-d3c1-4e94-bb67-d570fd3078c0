package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ShelfResponseConvertUtils {

    private static final List<String> NEED_CLEAN_FILTER_IDS = Lists.newArrayList("100002");

    public static UnifiedShelfResponse cleanDegradeData(UnifiedShelfResponse response) {
        // 结果数据校验,只处理首屏接口
        if (invalidUnifiedShelfResponse(response)) {
            return null;
        }
        // 敏感信息清除
        UnifiedShelfResponse shelfDegradeData = new UnifiedShelfResponse();
        shelfDegradeData.setShowType(response.getShowType());
        shelfDegradeData.setSceneCode(response.getSceneCode());
        shelfDegradeData.setOcean(buildOcean());
        // 导航数据清洗
        shelfDegradeData.setFilter(copyShelfFilterNodeVO(response.getFilter()));
        // 商卡清洗
        shelfDegradeData.setFilterIdAndProductAreas(copyFilterProductAreaVOs(response.getFilterIdAndProductAreas()));
        shelfDegradeData.setMainTitle(copyShelfMainTitleVO(response.getMainTitle()));
        shelfDegradeData.setRetainMsg("降级数据");
        return shelfDegradeData;
    }

    private static ShelfOceanVO buildOcean() {
        ShelfOceanEntryVO dzShelfOceanEntryVO = new ShelfOceanEntryVO();
        dzShelfOceanEntryVO.setBidView("degrade_mv");
        dzShelfOceanEntryVO.setBidClick("degrade_mc");
        ShelfOceanVO shelfOceanVO = new ShelfOceanVO();
        shelfOceanVO.setProductItem(dzShelfOceanEntryVO);
        return shelfOceanVO;
    }

    private static List<ShelfFilterProductAreaVO> copyFilterProductAreaVOs(List<ShelfFilterProductAreaVO> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return Lists.newArrayList();
        }
        return productList.stream()
                .map(ShelfResponseConvertUtils::copyFilterProductAreaVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ShelfFilterProductAreaVO copyFilterProductAreaVO(ShelfFilterProductAreaVO orig) {
        if (invalidFilterProductAreasVO(orig)) {
            return null;
        }
        ShelfFilterProductAreaVO dest = new ShelfFilterProductAreaVO();
        dest.setProductAreas(copyShelfProductAreaVOs(orig.getProductAreas()));
        dest.setFilterId(orig.getFilterId());
        dest.setRetainMsg("降级数据");
        return dest;
    }

    private static List<ShelfProductAreaVO> copyShelfProductAreaVOs(List<ShelfProductAreaVO> origList) {
        if (CollectionUtils.isEmpty(origList)) {
            return Lists.newArrayList();
        }
        List<ShelfProductAreaVO> destList = Lists.newArrayList();
        for (ShelfProductAreaVO orig : origList) {
            if (orig == null) {
                continue;
            }
            ShelfProductAreaVO dest = new ShelfProductAreaVO();
            dest.setItems(copyShelfProductItemVOs(orig.getItems()));
            dest.setMoreJumpUrl(orig.getMoreJumpUrl());
            dest.setMoreText(orig.getMoreText());
            dest.setDefaultShowNum(orig.getDefaultShowNum());
            dest.setShowType(orig.getShowType());
            destList.add(dest);
        }
        return destList;
    }

    /**
     * 对标 {@link com.sankuai.dzviewscene.shelf.faulttolerance.utils.BeanConvertUtils#buildProductItems(java.util.List)}
     */
    private static List<ShelfItemVO> copyShelfProductItemVOs(List<ShelfItemVO> origList) {
        if (CollectionUtils.isEmpty(origList)) {
            return Lists.newArrayList();
        }
        List<ShelfItemVO> destList = Lists.newArrayList();
        for (ShelfItemVO orig : origList) {
            if (orig == null) {
                continue;
            }
            ShelfItemVO dest = new ShelfItemVO();
            dest.setItemId(orig.getItemId());
            dest.setJumpUrl(orig.getJumpUrl());
            dest.setProductTags(orig.getProductTags());
            dest.setTitle(orig.getTitle());
            dest.setHeadPic(getPic(orig.getHeadPic()));
            dest.setMarketPrice(orig.getMarketPrice());
            dest.setSalePrice(getSalePrice(orig.getBasePrice(), orig.getSalePrice()));
            dest.setSalePriceSuffix(orig.getSalePricePrefix());
            dest.setSalePricePrefix(orig.getSalePriceSuffix());
            dest.setBasePrice(orig.getBasePrice());
            dest.setButtonCarouselMsg(orig.getButtonCarouselMsg());
            dest.setAvailable(orig.isAvailable());
            dest.setButton(getSimpleButton(orig.getButton()));
            dest.setShowType(orig.getShowType());
            dest.setSubItems(copyShelfProductItemVOs(orig.getSubItems()));
            // dest.setSpecialTags();
            // dest.setPriceBottomTags();
            // dest.setPromoTags();
            // dest.setExtra();
            // dest.setLabs();
            destList.add(dest);
        }
        return destList;
    }

    private static PicAreaVO getPic(PicAreaVO pic) {
        if (pic == null) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(pic.getPic());
        return picAreaVO;
    }

    private static ShelfButtonVO getSimpleButton(ShelfButtonVO button) {
        if(button == null){
            return null;
        }
        ShelfButtonVO shelfButtonVO = new ShelfButtonVO();
        shelfButtonVO.setJumpUrl(button.getJumpUrl());
        shelfButtonVO.setName("抢购");
        return shelfButtonVO;
    }

    private static ShelfFilterVO copyShelfFilterNodeVO(ShelfFilterVO filterVO) {
        ShelfFilterVO shelfFilterVO = new ShelfFilterVO();
        ShelfFilterNodeVO shelfFilterNodeVO = copyShelfFilterNodeVO(filterVO.getFilterRoot(), NEED_CLEAN_FILTER_IDS);
        shelfFilterVO.setFilterRoot(shelfFilterNodeVO);
        return shelfFilterVO;
    }

    private static ShelfMainTitleVO copyShelfMainTitleVO(ShelfMainTitleVO mainTitleVO) {
        if(mainTitleVO == null){
            return null;
        }
        ShelfMainTitleVO shelfMainTitleVO = new ShelfMainTitleVO();
        shelfMainTitleVO.setTitle(mainTitleVO.getTitle());
        shelfMainTitleVO.setTotalProductQty(mainTitleVO.getTotalProductQty());
        return shelfMainTitleVO;
    }

    private static ShelfFilterNodeVO copyShelfFilterNodeVO(ShelfFilterNodeVO orig, List<String> needCleanFilterIds) {
        if (needCleanFilterIds.contains(orig.getFilterId())) {
            return null;
        }
        ShelfFilterNodeVO dest = new ShelfFilterNodeVO();
        dest.setFilterId(orig.getFilterId());
        dest.setLabs(orig.getLabs());
        dest.setMinShowNum(orig.getMinShowNum());
        dest.setTitle(orig.getTitle());
        dest.setSelected(orig.isSelected());
        dest.setMultiSelect(orig.isMultiSelect());
        if (CollectionUtils.isNotEmpty(orig.getChildren())) {
            dest.setChildren(orig.getChildren().stream().map(item -> copyShelfFilterNodeVO(item, needCleanFilterIds))
                    .filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return dest;
    }

    private static String getSelectedFilterId(ShelfFilterNodeVO filterNodeVO) {
        if (Objects.isNull(filterNodeVO)) {
            return StringUtils.EMPTY;
        }
        if (filterNodeVO.isSelected()) {
            return filterNodeVO.getFilterId();
        }
        if (CollectionUtils.isNotEmpty(filterNodeVO.getChildren())) {
            for (ShelfFilterNodeVO childFilterNodeVO : filterNodeVO.getChildren()) {
                String childResult = getSelectedFilterId(childFilterNodeVO);
                if (StringUtils.isNotBlank(childResult)) {
                    return childResult;
                }
            }
        }
        return StringUtils.EMPTY;
    }

    private static String getSalePrice(String basePrice, String salePrice) {
        // 原来的这个售卖价没有数据，默认也不塞数据
        if (StringUtils.isEmpty(salePrice)) {
            return StringUtils.EMPTY;
        }
        // 基础价格无效，售卖价兜底
        if (StringUtils.isEmpty(basePrice)) {
            return salePrice;
        }
        // 展示基础价格（优惠之前的价格）
        return basePrice;
    }

    private static boolean invalidFilterProductAreasVO(ShelfFilterProductAreaVO productAreaVO) {
        return productAreaVO == null
                || CollectionUtils.isEmpty(productAreaVO.getProductAreas())
                || productAreaVO.getProductAreas().get(0) == null
                || productAreaVO.getProductAreas().get(0).getItems() == null
                || CollectionUtils.isEmpty(productAreaVO.getProductAreas().get(0).getItems());
    }

    private static boolean invalidUnifiedShelfResponse(UnifiedShelfResponse shelfResponse) {
        return Objects.isNull(shelfResponse)
                || Objects.isNull(shelfResponse.getFilter())
                || CollectionUtils.isEmpty(shelfResponse.getFilterIdAndProductAreas())
                || Objects.isNull(shelfResponse.getFilter().getFilterRoot())
                || NEED_CLEAN_FILTER_IDS.contains(getSelectedFilterId(shelfResponse.getFilter().getFilterRoot()));
    }

}
