package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.searchBoxJumpUrl;


import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.SearchBoxJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;

@VPointOption(name = "优惠码团购货架搜索框跳转链接选项", description = "搜索框跳转链接", code = PromotionCodeFilterSearchBoxJumpUrlOpt.CODE)
public class PromotionCodeFilterSearchBoxJumpUrlOpt extends SearchBoxJumpUrlVP<PromotionCodeFilterSearchBoxJumpUrlOpt.Config> {
    public static final String CODE = "PromotionCodeFilterSearchBoxJumpUrlOpt";
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return buildJumpUrl(config, context, param);
    }

    private String buildJumpUrl(Config config, ActivityCxt context, Param param){
        int platform = param.getPlatform();
        int category = param.getShopCategory();
        if(PlatformUtil.isMT(platform)){
            long mtPoiId = PoiIdUtil.getMtPoiIdL(param);
            return String.format(config.getMtJumpUrl(), mtPoiId, category);
        }
        long dpPoiId = PoiIdUtil.getDpPoiIdL(param);
        return String.format(config.getDpJumpUrl(), dpPoiId, category);
    }


    @Data
    @VPointCfg
    public static class Config {
        private String mtJumpUrl;
        private String dpJumpUrl;
    }
}
