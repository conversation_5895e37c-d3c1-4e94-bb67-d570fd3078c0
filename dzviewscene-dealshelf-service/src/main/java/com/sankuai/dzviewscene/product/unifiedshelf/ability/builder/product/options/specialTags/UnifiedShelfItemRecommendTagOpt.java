package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@VPointOption(name = "团单推荐标签",
        description = "基于推荐的团单标签",
        code = "UnifiedShelfItemRecommendTagOpt")
public class UnifiedShelfItemRecommendTagOpt extends UnifiedShelfItemSpecialTagVP<UnifiedShelfItemRecommendTagOpt.Config> {

    @Override
    public ItemSpecialTagVO compute(ActivityCxt context, Param param, Config config) {
        // 若价格下方有标签，不展示推荐标签
        if (CollectionUtils.isNotEmpty(param.getShelfItemVO().getPriceBottomTags())) {
            return null;
        }
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())) {
            return null;
        }
        List<ShelfTagVO> recommendTags = getRecommendTags(param);
        if (CollectionUtils.isEmpty(recommendTags)) {
            return null;
        }
        ItemSpecialTagVO itemSpecialTagVO = new ItemSpecialTagVO();
        itemSpecialTagVO.setTags(recommendTags);
        return itemSpecialTagVO;
    }

    private List<ShelfTagVO> getRecommendTags(Param param) {
        // 竞争圈标签
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(param.getProductM());
        if (StringUtils.isNotBlank(tradeRateTag)) {
            return Lists.newArrayList(SpecialTagsUtils.buildShelfTagVO(tradeRateTag, ColorUtils.colorFFF1EC, ColorUtils.colorFF4B10));
        }
        // UGC标签
        List<String> recommendTags = PriceAboveTagsUtils.getRecommendTag(param.getProductM());
        if (CollectionUtils.isNotEmpty(recommendTags)) {
            return Lists.newArrayList(SpecialTagsUtils.buildShelfTagVO("“" + recommendTags.get(0) + "”", ColorUtils.colorF4F4F4, ColorUtils.color666666));
        }
        return Lists.newArrayList();
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 足疗新标签样式+有大促调整+副标题信息调整方案
         * 新标签样式+无大促调整+副标题信息调整方案
         */
        private List<String> massageTagExpSks;
    }
}
