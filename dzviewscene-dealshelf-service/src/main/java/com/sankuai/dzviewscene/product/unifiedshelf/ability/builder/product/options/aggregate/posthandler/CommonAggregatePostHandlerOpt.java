package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process.AggregateHandlerContext;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process.AggregatePostHandler;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process.AggregatePostHandlerFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregatePostHandlerVP;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@VPointOption(name = "聚合卡片后置处理",
        description = "聚合卡片后置处理，配置后置处理策略",
        code = CommonAggregatePostHandlerOpt.CODE)
public class CommonAggregatePostHandlerOpt extends AggregatePostHandlerVP<CommonAggregatePostHandlerOpt.Config> {

    public static final String CODE = "CommonAggregatePostHandleOpt";

    @Autowired
    private AggregatePostHandlerFactory aggregatePostHandlerFactory;

    @Override
    public ShelfItemVO compute(ActivityCxt activityCxt, Param param, Config config) {
        ShelfItemVO result = param.getShelfItemVO();
        if(CollectionUtils.isEmpty(config.getHandlerConfig())){
            return result;
        }
        // 执行配置后置处理过滤器
        for (String filterName : config.getHandlerConfig()) {
            AggregatePostHandler filter = aggregatePostHandlerFactory.getStrategy(filterName);
            if (filter == null) {
                continue;
            }
            AggregateHandlerContext context = AggregateHandlerContext.builder()
                    .productMMap(param.getProductMMap()).shelfItemVO(result).build();
            result = filter.process(activityCxt, context);
        }
        return result;
    }

    @VPointCfg
    @Data
    public static class Config {

        //配置策略按序执行
        public List<String> handlerConfig;
    }
}
