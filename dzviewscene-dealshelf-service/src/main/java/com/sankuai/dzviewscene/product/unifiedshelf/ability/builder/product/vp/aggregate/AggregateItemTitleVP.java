package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@VPoint(name = "聚合商卡标题", description = "聚合商卡标题-title", code = AggregateItemTitleVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class AggregateItemTitleVP<T> extends PmfVPoint<List<StyleTextModel>, AggregateItemTitleVP.Param, T> {

    public static final String CODE = "AggregateItemTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductHierarchyNodeM nodeM;

        private Map<String, ProductM> productMMap;
    }
}
