package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UnifiedShelfProductMPromoInfoUtils {

    /**
     * 获取秒杀优惠信息，包括：秒杀+新客、秒杀
     */
    public static ShelfTagVO getSecKillPromoPriceM(List<ProductPromoPriceM> promoPriceMS, String marketPrice, String salePrice) {
        Map<Integer, ProductPromoPriceM> promoTypeAndPriceMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(promoPriceMS);
        if (CollectionUtils.isEmpty(promoPriceMS) || MapUtils.isEmpty(promoTypeAndPriceMap)) {
            return null;
        }
        if (StringUtils.isEmpty(marketPrice)) {
            return null;
        }
        // 计算优惠金额
        BigDecimal promoPrice = new BigDecimal(marketPrice).subtract(getSalePrice(salePrice));
        ShelfTagVO dzTagVO = null;
        if (MapUtils.isNotEmpty(promoTypeAndPriceMap) && promoTypeAndPriceMap.containsKey(PromoTagTypeEnum.NewUser.getCode())) {
            // 秒杀+新客共省
            dzTagVO = buildBasicTagVO("新客共省" + promoPrice);
            dzTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(promoTypeAndPriceMap.get(PromoTagTypeEnum.NewUser.getCode())));
        } else if (MapUtils.isNotEmpty(promoTypeAndPriceMap) && promoTypeAndPriceMap.containsKey(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode())) {
            // 秒杀+新客共省
            dzTagVO = buildBasicTagVO("新客共省" + promoPrice);
            dzTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(promoTypeAndPriceMap.get(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode())));
        } else if (MapUtils.isNotEmpty(promoTypeAndPriceMap)) {
            // 秒杀
            dzTagVO = buildBasicTagVO("共省" + promoPrice);
            dzTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(promoPriceMS.get(0)));
        }
        return dzTagVO;
    }

    /**
     * 构建会员标签
     */
    public static ShelfTagVO buildMemberTag(ProductM productM, String salePrice, ProductPromoPriceM promoPriceM) {
        if (StringUtils.isEmpty(productM.getMarketPrice())) {
            return null;
        }
        String text = "共省" + new BigDecimal(productM.getMarketPrice()).subtract(getSalePrice(salePrice));
        ShelfTagVO memberDzTagVO = buildBasicTagVO(text);
        ProductPromoPriceM hasDefaultPromoPriceM = productM.getPromoPrices().stream().filter(item -> CollectionUtils.isNotEmpty(item.getPromoItemList())).findFirst().orElse(null);
        memberDzTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(Objects.nonNull(promoPriceM) ? promoPriceM : hasDefaultPromoPriceM));
        return memberDzTagVO;
    }

    public static ShelfTagVO buildBasicTagVO(String promoTag) {
        ShelfTagVO dzTagVO = new ShelfTagVO();
        dzTagVO.setName(promoTag);
        return dzTagVO;
    }

    public static BigDecimal getSalePrice(String salePrice) {
        if (StringUtils.isEmpty(salePrice)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(salePrice);
    }
}
