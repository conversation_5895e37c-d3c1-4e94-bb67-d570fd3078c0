package com.sankuai.dzviewscene.product.filterlist.acitivity;

import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.gmkt.event.api.enums.QRCodeType;
import com.dianping.gmkt.event.api.promoqrcode.enums.PromoCodeType;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRClientType;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.athena.viewscene.framework.annotation.ContextBuilder;
import com.sankuai.athena.viewscene.framework.core.IContextBuilder;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealProductSaleChannelAggregationDTO;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.it.iam.common_base.utils.IntegerUtil;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySceneEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingInfoRequest;
import com.sankuai.mtstore.aggregate.common.enums.JumpUrlPlatformEnum;
import com.sankuai.mtstore.aggregate.common.enums.ModuleEnum;
import com.sankuai.mtstore.aggregate.common.enums.SourceEnum;
import com.sankuai.mtstore.aggregate.thrift.dto.request.GetJumpUrlReqDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.mapstruct.ap.internal.util.Collections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils.*;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@ContextBuilder(activityCode = DealFilterListActivity.CODE, name = "团购筛选列表上下文构造，会区分展位构造")
public class DealFilterListActivityCtxBuilder implements IContextBuilder {

    public static final ExecutorService convertIdExecutor = Rhino.newThreadPool("filterConvertIdExecutor",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(50).withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())).getExecutor();

    private static final Logger logger = LoggerFactory.getLogger(DealFilterListActivityCtxBuilder.class);
    @Resource
    private CompositeAtomService compositeAtomService;
    @Resource
    private AtomFacadeService facadeService;

    private static final List<String> specialSpaceKeys = Lists.newArrayList(DealFilterListActivity.SpaceKey.SHOP_DEAL_SHELF_LANDING, DealFilterListActivity.SpaceKey.DEAL_DETAIL_LIST);

    //团详场景识别所需的团单属性Map，该map的key是团单属性标识，该map的value是团单属性名
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.detail.scene.identifier.required.deal.attrs.config", defaultValue = "{}")
    private Map<String, String> dealDetailSceneIdentifierRequiredDealAttrMap;


    @Override
    public ActivityCxt build(ActivityRequest activityRequest) {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.setParameters(activityRequest.getParams());
        activityContext.setActivityCode(activityRequest.getActivityCode());
        //构造落地页货架的上下文
        if (specialSpaceKeys.contains(ParamsUtil.getStringSafely(activityContext, PmfConstants.Params.spaceKey)) || defaultListNeedShopRoute(activityContext)) {
            // poiMigrate
            if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.DealFilterListActivityCtxBuilder.getScene())) {
                addPlatformParamPoiMigrate(activityContext);
                // 添加交易连续包月团单属性参数
                long dealId  = getDealId(activityContext);
                int platform = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.platform);
                String categoryId = ParamsUtil.getStringSafely(activityContext,ProductDetailActivityConstants.Params.dealCategoryId);
                List<String> serviceTypes = LionConfigHelper.getMonthlySubscriptionCategoryList(String.valueOf(categoryId));
                if (!CollectionUtils.isEmpty(serviceTypes)) {
                    // 添加交易连续包月团单属性参数
                    addDealAttrParams(activityContext,dealId, platform, serviceTypes);
                }

            } else {
                // 原逻辑
                addPlatformParam(activityContext);
            }
            addLaunchParam(activityContext);
            LogUtils.addDiffShopIdLogToRequest(activityContext,activityRequest);
            //添加优惠码参数
            if (defaultListNeedShopRoute(activityContext)) {
                addPromoShelfParams(activityContext);
            }
            // 销售渠道
            addSaleChannel(activityContext);
        }
        //旗舰店货架
        if (Objects.equals(DealFilterListActivity.SpaceKey.FLAGSHIP_STORE_SHELF, activityContext.getParam(ShelfActivityConstants.Params.spaceKey))) {
            addFlagshipStoreShelfLaunchParam(activityContext);
        }
        activityContext.addParam(CtxUtils.SHELF_DEFAULT_CONVERT,
                CompletableFuture.supplyAsync(() -> CtxUtils.addCityIdAndUserIdForMagicMember(activityContext.getParameters()), convertIdExecutor));
        return activityContext;
    }

    public void addPromoShelfExtraParams(ActivityCxt activityContext) {
        if (StringUtils.isBlank(activityContext.getParam(ShelfActivityConstants.Params.extra))) {
            return;
        }
        // 增加优惠码提单页orderTrafficFlag
        activityContext.addParam(ShelfActivityConstants.Params.orderTrafficFlag, "newYouhuimaMini");
        // 解析extra
        String extra = decodeExtraParam(activityContext.getParam(ShelfActivityConstants.Params.extra));
        Map<String, Object> extraMap = JsonCodec.decode(extra, new TypeReference<Map<String, Object>>() {});
        if (MapUtils.isEmpty(extraMap)) {
            return;
        }
        Map<String, Object> promoCodeExtraInfo = new HashMap<>();
        promoCodeExtraInfo.put("source", extraMap.get("source"));
        promoCodeExtraInfo.put("pass_param", buildPassParam(activityContext, extraMap));
        if (Lion.getBoolean(Environment.getAppName(), "offlinecode.magic.member.coupon.switch", false)) {
            Long dpShopId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL);
            activityContext.attach("magicMemberCouponSwitch", compositeAtomService.queryMagicMemberCouponSwitch(dpShopId));
        } else {
            if (extraMap.containsKey("mmcinflate")) promoCodeExtraInfo.put("mmcinflate", extraMap.get("mmcinflate"));
            if (extraMap.containsKey("mmcuse")) promoCodeExtraInfo.put("mmcuse", extraMap.get("mmcuse"));
            if (extraMap.containsKey("mmcbuy")) promoCodeExtraInfo.put("mmcbuy", extraMap.get("mmcbuy"));
            if (extraMap.containsKey("mmcfree")) promoCodeExtraInfo.put("mmcfree", extraMap.get("mmcfree"));
        }
        promoCodeExtraInfo.put("codeKey", extraMap.getOrDefault("codeKey", ""));
        activityContext.addParam(ShelfActivityConstants.Params.promoCodeExtraInfo, promoCodeExtraInfo);
        int userAgent = activityContext.getParam(ShelfActivityConstants.Params.userAgent);
        if (VCClientTypeEnum.MT_XCX.getCode() == userAgent && extraMap.containsKey("version")) {
            String version = (String) extraMap.get("version");
            activityContext.addParam(ShelfActivityConstants.Params.appVersion, version);
        }
    }

    /**
     * 优惠码场景增加风控参数*
     * @param activityContext
     */
    public void addRiskParam(ActivityCxt activityContext) {
        if (StringUtils.isBlank(activityContext.getParam(ShelfActivityConstants.Params.extra))) {
            return;
        }
        // 解析extra
        Map<String, Object> extraMap = JsonCodec.decode((String)activityContext.getParam(ShelfActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {});
        if(MapUtils.isEmpty(extraMap)) {
            return;
        }
        if (StringUtils.isNotBlank(activityContext.getParam(ShelfActivityConstants.Params.mtgsig))) {
            extraMap.put("mtgsig", activityContext.getParam(ShelfActivityConstants.Params.mtgsig));
        }
        activityContext.addParam(ShelfActivityConstants.Params.riskParam, JsonCodec.encode(extraMap));
    }

    @Nullable
    private String buildPassParam(ActivityCxt activityContext, Map<String, Object> extraMap) {
        Map<String, Object> distributionBasicInfo = new HashMap<>();
        distributionBasicInfo.put("distributionType", "promotion_code");
        distributionBasicInfo.put("sceneId", getSceneId(getScanCodeType(extraMap, activityContext)));
        if(StringUtils.isNotBlank((String) extraMap.get("codeKey"))){
            Map<String, String> bizExtend = new HashMap<>();
            bizExtend.put("distributionCode", (String) extraMap.get("codeKey"));
            distributionBasicInfo.put("bizExtend", new Gson().toJson(bizExtend));
        }
        List<Map<String, Object>> distributionBasicInfoList = new ArrayList<>();
        distributionBasicInfoList.add(distributionBasicInfo);
        Map<String, Object> distribution = new HashMap<>();
        distribution.put("distributionInfoList", distributionBasicInfoList);
        Map<String, Object> passParam = new HashMap<>();
        passParam.put("DISTRIBUTION_BASIC_INFO", new Gson().toJson(distribution));
        passParam.put("commissionFree", getCommissionFree(getScanCodeType(extraMap, activityContext)));
        return encodePassParam(passParam);
    }

    private String encodePassParam(Map<String, Object> params) {
        try {
            return URLEncoder.encode(new Gson().toJson(params), "utf-8");
        } catch (Exception e) {
            logger.error("getPassParam error, pass_param:{}", params, e);
            return null;
        }
    }
    private String decodeExtraParam(String extraParams) {
        try {
            return URLDecoder.decode(extraParams, "utf-8");
        } catch (Exception e) {
            logger.error("decodeExtraParam error, extraParams:{}", extraParams, e);
            return "";
        }
    }
    private void addPromoShelfParams(ActivityCxt activityContext) {
        // 货架融合优化-获取真实的sceneCode
        alterPromoCodeSceneCode(activityContext);
        // 添加优惠码神券链路参数
        addPromoShelfExtraParams(activityContext);
        // 添加优惠码子场景参数
        addSubSceneParams(activityContext);
        // 优惠码货架增加风控参数 https://km.sankuai.com/collabpage/2128162791
        addRiskParam(activityContext);
    }

    private void alterPromoCodeSceneCode(ActivityCxt activityContext) {
        String requestSceneCode = activityContext.getParam(ShelfActivityConstants.Params.sceneCode);
        if (!isUnifiedPromoCodeShelf(requestSceneCode)) {
            return;
        }
        Long mtShopId = fillMtShopId(activityContext);
        String sceneCode = activityContext.getParam(ShelfActivityConstants.Params.sceneCode);
        if (Objects.equals(sceneCode, PROMO_CODE_UNIFIED_SHOP_SCENE)) {
            LandingInfoRequest shopReq = buildShelfLandInfoReq(activityContext, mtShopId, PromoCodeType.SHOP_CODE.code, null);
            ShelfLoadConfig shelfLoadConfig = compositeAtomService.queryPromoCodeShopShelfConfig(shopReq).join();
            activityContext.addParam(ShelfActivityConstants.Params.sceneCode, PromoCodeUtils.judgePromoShopSceneCode(shelfLoadConfig));
        } else if (Objects.equals(sceneCode, PROMO_CODE_UNIFIED_STAFF_SCENE)) {
            String codeId = activityContext.getParam(ShelfActivityConstants.Params.entityId);
            LandingInfoRequest request = buildShelfLandInfoReq(activityContext, mtShopId, PromoCodeType.STAFF_CODE.code, codeId);
            activityContext.attach(ShelfActivityConstants.Attachments.promoCodeShelfConfig, compositeAtomService.queryPromoCodeShopShelfConfig(request));
            activityContext.addParam(ShelfActivityConstants.Params.sceneCode, PROMO_CODE_STAFF_SCENE);
        }
    }

    private static @NotNull LandingInfoRequest buildShelfLandInfoReq(ActivityCxt activityContext, long mtShopId, Integer codeType, String sourceId) {
        LandingInfoRequest request = new LandingInfoRequest();
        request.setShopId(mtShopId);
        request.setCodeType(codeType);
        request.setSourceIdentifier(sourceId);
        request.setQrClientType(!PlatformUtil.isApp(activityContext.getParam(ShelfActivityConstants.Params.userAgent)) ?
                QRClientType.MT_WE_CHAT_APPLET.code : PlatformUtil.isMT(activityContext.getParam(ShelfActivityConstants.Params.userAgent)) ?
                QRClientType.MT_APP.code : QRClientType.DP_APP.code);
        return request;
    }

    private static Long fillMtShopId(ActivityCxt activityContext) {
        long mtPoiId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtPoiIdL);
        if (mtPoiId > 0) {
            return mtPoiId;
        }
        CacheCompositeAtomService cacheCompositeAtomService = AthenaBeanFactory.getBean(CacheCompositeAtomService.class);
        Long mtShopId = cacheCompositeAtomService.getMtByDpPoiIdL(ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL)).join();
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiIdL, mtShopId);
        return mtShopId;
    }

    private boolean isUnifiedPromoCodeShelf(String requestSceneCode) {
        return Objects.equals(requestSceneCode, PROMO_CODE_UNIFIED_SHOP_SCENE)
                || Objects.equals(requestSceneCode, PROMO_CODE_UNIFIED_STAFF_SCENE);
    }

    private void addSubSceneParams(ActivityCxt activityContext) {
        if (!hasPromoSubScene(activityContext)) {
            return;
        }
        // 职人分享赚改价场景添加参数
        if (Objects.equals(activityContext.getParam(ShelfActivityConstants.Params.subScene), "staff_share_workbench_change_price_shelf")) {
            addChangePriceShelfParams(activityContext, ShopAutoVerifySceneEnum.STAFF_BARGAIN.getCode());
        } else if (Objects.equals(activityContext.getParam(ShelfActivityConstants.Params.subScene), "medical_change_price_shelf")) {
            addChangePriceShelfParams(activityContext, ShopAutoVerifySceneEnum.MEDICAL_PLACE_ORDER_CODE.getCode());
        }
    }

    private void addChangePriceShelfParams(ActivityCxt activityContext, String shopVerifyScene) {
        activityContext.attach(ShelfActivityConstants.Params.shopAutoVerify,
                compositeAtomService.queryShopIsAutoVerify(buildShopAutoVerifyQueryRequest(activityContext, shopVerifyScene)));
        activityContext.addParam("bpDealGroupTypes", Lists.newArrayList(1));
    }

    private ShopAutoVerifyQueryRequest buildShopAutoVerifyQueryRequest(ActivityCxt activityContext, String scene) {
        ShopAutoVerifyQueryRequest shopAutoVerifyQueryRequest = new ShopAutoVerifyQueryRequest();
        shopAutoVerifyQueryRequest.setPlatform(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.platform));
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.platform))) {
            shopAutoVerifyQueryRequest.setShopId(ParamsUtil.getLongSafely(activityContext.getParameters(), ShelfActivityConstants.Params.mtPoiIdL));
        } else {
            shopAutoVerifyQueryRequest.setShopId(ParamsUtil.getLongSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiIdL));
        }
        shopAutoVerifyQueryRequest.setScene(scene);
        return shopAutoVerifyQueryRequest;
    }

    private boolean hasPromoSubScene(ActivityCxt activityContext) {
        return StringUtils.isNotBlank(activityContext.getParam(ShelfActivityConstants.Params.subScene));
    }

    private String getSceneId(Integer codeType) {
        PromoCodeType byCode = PromoCodeType.getByCode(codeType);
        if (byCode == null) {
            return "";
        }
        switch (byCode) {
            case SHOP_CODE:
            case BRAND_CODE:
                return "ShopCode";
            case STAFF_CODE:
                return "StaffCode";
            default:
                return "";
        }
    }

    private String getCommissionFree(Integer codeType) {
        PromoCodeType byCode = PromoCodeType.getByCode(codeType);
        if (byCode == null) {
            return "";
        }
        switch (byCode) {
            case SHOP_CODE:
            case BRAND_CODE:
                return "10002";
            case STAFF_CODE:
                return "10003";
            default:
                return "";
        }
    }

    private Integer getScanCodeType(Map<String, Object> extraMap, ActivityCxt activityContext) {
        if (org.apache.commons.collections.MapUtils.isNotEmpty(extraMap) && extraMap.containsKey("codeType")) { //优先从如参数获取
            return IntegerUtil.parseInt(extraMap.get("codeType"), 0);
        } else if (PROMO_CODE_STAFF_SCENE.equals(activityContext.getSceneCode())) {
            return QRCodeType.PROMO_CODE_EMPLOYEE.getCode();
        }
        return 0; //其他
    }

    private void addLaunchParam(ActivityCxt activityContext) {
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM != null) {
            activityContext.addParam("shopType", shopM.getShopType());
            activityContext.addParam("category", shopM.getCategory());
        }
    }

    private void addPlatformParam(ActivityCxt activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityCtxBuilder.addPlatformParam(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.platform))) {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpCityId));
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShop(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiId)));
            int dealId = getDealId(activityContext);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getDpDealCategoryId(dealId)).orElse(0));
            return;
        }
        // 美团平台
        CompletableFuture<List<Integer>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtId(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.mtPoiId));
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.mtCityId));
        CompletableFuture.allOf(dpShopIdsFuture, dpCityIdFuture).thenAccept(v -> {
            List<Integer> dpShopIds = dpShopIdsFuture.join();
            Integer dpCityId = dpCityIdFuture.join();
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShop(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiId)));
            int dealId = getDealId(activityContext);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getMtDealCategoryId(dealId)).orElse(0));
        }).join();
    }

    private boolean needAddSaleChannel(ActivityCxt activityContext, Map<String, String> cate2PlanIdMap) {
        int categoryId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.dealCategoryId);
        String spaceKey = ParamsUtil.getStringSafely(activityContext, PmfConstants.Params.spaceKey);
        return Objects.equals(spaceKey, DealFilterListActivity.SpaceKey.DEAL_DETAIL_LIST)
                && cate2PlanIdMap.containsKey(String.valueOf(categoryId));
    }

    private void addSaleChannel(ActivityCxt cxt) {
        Map<String, String> cate2PlanIdMap = Lion.getMap(LionKeys.APP_KEY, LionKeys.SALE_CHANNEL_CATE_TO_PLAN_ID_CONFIG, String.class, Maps.newHashMap());
        // 保洁清洁409类目 and 团详展位
        if (needAddSaleChannel(cxt, cate2PlanIdMap)) {
            int categoryId = ParamsUtil.getIntSafely(cxt, ProductDetailActivityConstants.Params.dealCategoryId);
            String planId = cate2PlanIdMap.get(String.valueOf(categoryId));
            DealProductSaleChannelAggregationDTO dealSaleChannel = getDealSaleChannel(cxt, planId);
            cxt.addParam(ProductDetailActivityConstants.Params.dealSaleChannel, dealSaleChannel);
        }
    }

    private void addPlatformParamPoiMigrate(ActivityCxt activityContext) {
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.platform))) {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, PoiIdUtil.getDpPoiIdL(activityContext.getParameters(), ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.dpCityId));
            int dealId = getDealId(activityContext);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getDpDealCategoryId(dealId)).orElse(0));
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShopPoiMigrateWithCxt(activityContext));
            return;
        }
        // 美团平台
        CompletableFuture<List<Long>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIdL(PoiIdUtil.getMtPoiIdL(activityContext.getParameters(), ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId));
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext.getParameters(), ShelfActivityConstants.Params.mtCityId));
        CompletableFuture.allOf(dpShopIdsFuture, dpCityIdFuture).thenAccept(v -> {
            List<Long> dpShopIds = dpShopIdsFuture.join();
            Integer dpCityId = dpCityIdFuture.join();
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds).intValue());
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            int dealId = getDealId(activityContext);
            activityContext.addParam(ProductDetailActivityConstants.Params.dealCategoryId, Optional.ofNullable(getMtDealCategoryId(dealId)).orElse(0));
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShopPoiMigrateWithCxt(activityContext));
        }).join();
    }

    private int getDealId(ActivityCxt activityContext) {
        int dealId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntSafely(activityContext, PmfConstants.Params.productId);
        if (dealId <= 0) {
            dealId = com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil.getIntSafely(activityContext, PmfConstants.Params.entityId);
        }
        return dealId;
    }

    private ShopM loadShop(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityCtxBuilder.loadShop(int)");
        return compositeAtomService.loadShop(dpShopId).thenApply(this::buildShopM).join();
    }

    private ShopM buildShopM(ShopDTO shopDTO) {
        if (Objects.isNull(shopDTO)) {
            return null;
        }
        ShopM shopM = new ShopM();
        shopM.setShopId(shopDTO.getShopId() == null ? 0 : shopDTO.getShopId());
        shopM.setShopUuid(shopDTO.getShopUuid() == null ? "" : shopDTO.getShopUuid());
        shopM.setShopName(shopDTO.getShopName());
        shopM.setShopType(shopDTO.getShopType() == null ? 0 : shopDTO.getShopType());
        shopM.setCategory(shopDTO.getMainCategoryId() == null ? 0 : shopDTO.getMainCategoryId());
        shopM.setLat(shopDTO.getGlat() == null ? 0 : shopDTO.getGlat());
        shopM.setLng(shopDTO.getGlng() == null ? 0 : shopDTO.getGlng());
        shopM.setCityId(shopDTO.getCityId() == null ? 0 : shopDTO.getCityId());
        shopM.setStatus(shopDTO.getPower() == null ? -1 : shopDTO.getPower());
        return shopM;
    }

    private ShopM buildShopM(DpPoiDTO dpPoiDTO, boolean selfOperatedCleaningShop) {
        if (Objects.isNull(dpPoiDTO)) {
            return null;
        }
        ShopM shopM = new ShopM();
        shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
        shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
        shopM.setShopGroupId(dpPoiDTO.getShopGroupId() == null ? 0 : dpPoiDTO.getShopGroupId());
        shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
        shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
        shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
        shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
        shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        shopM.setStatus(dpPoiDTO.getPower() == null ? -1 : dpPoiDTO.getPower());
        if (shopM.getExtAttrs() == null) {
            shopM.setExtAttrs(Lists.newArrayList());
        }
        // 添加是否自营保洁商户的扩展属性
        AttrM attrM = new AttrM();
        attrM.setName("selfOperatedCleaningShop");
        attrM.setValue(String.valueOf(selfOperatedCleaningShop));
        shopM.getExtAttrs().add(attrM);
        return shopM;
    }

    private ShopM loadShopPoiMigrateWithCxt(ActivityCxt cxt) {
        Long dpShopId = PoiIdUtil.getDpPoiIdL(cxt, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        Map<String, String> cate2PlanIdMap = Lion.getMap(LionKeys.APP_KEY, LionKeys.SALE_CHANNEL_CATE_TO_PLAN_ID_CONFIG, String.class, Maps.newHashMap());
        CompletableFuture<NewReserveSubmissionPageWhiteShopCheckRespDTO> selfOperatedCleaningShopCf = CompletableFuture.completedFuture(null);
        if (needAddSaleChannel(cxt, cate2PlanIdMap)) {
            selfOperatedCleaningShopCf = compositeAtomService.querySelfOperatedCleaningShopInfo(dpShopId);
        }
        CompletableFuture<List<DpPoiDTO>> dpShopCf = compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId)));
        CompletableFuture<NewReserveSubmissionPageWhiteShopCheckRespDTO> finalSelfOperatedCleaningShopCf = selfOperatedCleaningShopCf;
        return CompletableFuture.allOf(dpShopCf, selfOperatedCleaningShopCf)
                .thenApply(v -> {
                    List<DpPoiDTO> dpPoiDTOS = dpShopCf.join();
                    if (CollectionUtils.isEmpty(dpPoiDTOS)) {
                        return null;
                    }
                    NewReserveSubmissionPageWhiteShopCheckRespDTO shopCheckRespDTO = finalSelfOperatedCleaningShopCf.join();
                    boolean isSelfOperatedShop = Objects.nonNull(shopCheckRespDTO) && shopCheckRespDTO.getCheckRes();
                    return buildShopM(Collections.first(dpPoiDTOS), isSelfOperatedShop);
                }).join();
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }

    /**
     *  优惠码场景*
     * @param activityContext
     * @return
     */
    private boolean defaultListNeedShopRoute(ActivityCxt activityContext) {
        String requestSceneCode = activityContext.getParam(ShelfActivityConstants.Params.sceneCode);
        return isUnifiedPromoCodeShelf(requestSceneCode)
                || Objects.equals(requestSceneCode, "activity_beauty_medical_coupon_shelf")
                || Objects.equals(requestSceneCode, "activity_beauty_medical_coupon_search_shelf")
                || Objects.equals(requestSceneCode, "activity_beauty_medical_customize_coupon_shelf")
                || Objects.equals(requestSceneCode, "activity_le_trade_guarantee_shelf")
                || Objects.equals(requestSceneCode, PROMO_CODE_STAFF_SCENE);
    }

    public void addFlagshipStoreShelfLaunchParam(ActivityCxt activityCxt) {
        activityCxt.addParam(ShelfActivityConstants.Ctx.ctxFlagshipStore, loadFlagshipStoreM(activityCxt));
        activityCxt.addParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl, loadFlagshipStoreUrl(activityCxt));
    }

    private String loadFlagshipStoreUrl(ActivityCxt activityCxt) {
        GetJumpUrlReqDTO getJumpUrlReqDTO = new GetJumpUrlReqDTO();
        getJumpUrlReqDTO.setStoreUuid(activityCxt.getParam(ShelfActivityConstants.Params.entityId));
        getJumpUrlReqDTO.setModule(ModuleEnum.RECOMMEND_GROUP.getCode());
        getJumpUrlReqDTO.setSource(SourceEnum.SHELVES.getCode());
        int platform = ParamsUtil.getIntSafely(activityCxt.getParameters(), ShelfActivityConstants.Params.platform);
        getJumpUrlReqDTO.setPlatform(PlatformUtil.isMT(platform)?JumpUrlPlatformEnum.MT_APP.getCode():JumpUrlPlatformEnum.DP_APP.getCode());
        return facadeService.getJumpUrlSuffixByComponents(getJumpUrlReqDTO)
                .thenApply(jumpUrlResp -> {
                    if (jumpUrlResp == null) {
                        return null;
                    }
                    return jumpUrlResp.getJumpUrl();
                }).join();
    }

    private FlagshipStoreM loadFlagshipStoreM(ActivityCxt activityCxt) {
        return facadeService.getStoreMainRecommendConfig(activityCxt.getParam(ShelfActivityConstants.Params.entityId))
                .thenApply(storeRaw -> {
                    if (storeRaw == null || storeRaw.getStoreId() == null) {
                        return null;
                    }
                    FlagshipStoreM flagshipStoreM = new FlagshipStoreM();
                    flagshipStoreM.setStoreId(storeRaw.getStoreId());
                    if (storeRaw.getRecommendConfig() != null && storeRaw.getRecommendConfig().getShowType() != null) {
                        flagshipStoreM.setModuleType(storeRaw.getRecommendConfig().getShowType());
                        flagshipStoreM.setProjectList(storeRaw.getRecommendConfig().getProjectList());
                    }
                    return flagshipStoreM;
                }).join();
    }

    private Integer getDpDealCategoryId(int dealId) {
        if(dealId <= 0) {
            return null;
        }
        CompletableFuture<Map<Integer, Integer>> dealIdCategoryIdMapFuture = compositeAtomService.batchGetDealIdCategoryIdMap(Lists.newArrayList(dealId));
        return dealIdCategoryIdMapFuture.thenApply(dealIdCategoryIdMap -> {
            if(MapUtils.isEmpty(dealIdCategoryIdMap)) {
                return null;
            }
            return dealIdCategoryIdMap.get(dealId);
        }).join();
    }

    private Integer getMtDealCategoryId(int dealId) {
        if(dealId <= 0) {
            return null;
        }
        CompletableFuture<List<IdMapper>> idMappersCompletableFuture = compositeAtomService.batchGetDealIdByMtId(Lists.newArrayList(dealId));
        Map<Integer, Integer> dealId2CategoryIdMap = idMappersCompletableFuture.thenCompose(idMappers -> {
            if(CollectionUtils.isEmpty(idMappers)) {
                return CompletableFuture.completedFuture(null);
            }
            List<Integer> dpDealIds = idMappers.stream().map(IdMapper::getDpDealGroupID).collect(Collectors.toList());
            return compositeAtomService.batchGetDealIdCategoryIdMap(dpDealIds);
        }).join();
        return CollectUtils.firstValue(dealId2CategoryIdMap);
    }

    private DealProductSaleChannelAggregationDTO getDealSaleChannel(ActivityCxt cxt, String planId) {
        DealProductRequest request = buildSaleChannelRequest(cxt, planId);
        CompletableFuture<DealProductResult> productCf = compositeAtomService.queryDealProductTheme(request);
        DealProductDTO dealProductDTO = productCf.thenApply(productResult -> {
            if (productResult == null || CollectionUtils.isEmpty(productResult.getDeals())) {
                return new DealProductDTO();
            }
            return productResult.getDeals().get(0);
        }).join();
        return dealProductDTO.getSaleChannelAggregation();
    }

    private DealProductRequest buildSaleChannelRequest(ActivityCxt cxt, String planId) {
        boolean isMt = PlatformUtil.isMT(ParamsUtil.getIntSafely(cxt.getParameters(), ShelfActivityConstants.Params.platform));
        int dealId = getDealId(cxt);
        long shopId = isMt ? ParamsUtil.getIntSafely(cxt.getParameters(), ShelfActivityConstants.Params.mtPoiIdL)
                : ParamsUtil.getIntSafely(cxt.getParameters(), ShelfActivityConstants.Params.dpPoiIdL);
        DealProductRequest request = new DealProductRequest();
        request.setPlanId(planId);
        request.setProductIds(Lists.newArrayList(dealId));
        request.setExtParams(buildExtParams(dealId, shopId));
        return request;
    }

    private Map<String, Object> buildExtParams(int dealId, long shopId) {
        Map<String, Object> extParams = new HashMap<>();
        extParams.put("dealIds", Lists.newArrayList(dealId));
        extParams.put("shopIdForLong", shopId);
        // 售卖渠道
        extParams.put("bpDealGroupTypes", Lists.newArrayList(12));
        return extParams;
    }

    private void addDealAttrParams(ActivityCxt activityContext, long dealId, int platform,List<String> serviceTypes) {
        if (dealId <= 0) {
            return;
        }
        CompletableFuture<DealGroupAttributeDTO> dealIdAttrDTOMapFuture = PlatformUtil.isMT(platform) ? getMtDealAttrValues(dealId) : getDealAttrValuesFuture(dealId);
        if (dealIdAttrDTOMapFuture == null) {
            return;
        }
        dealIdAttrDTOMapFuture.thenAccept(dealIdAttrDTO -> {
            if (dealIdAttrDTO != null) {
                String continuousMonthlySubscription = getAttrValue(dealIdAttrDTO, ProductDetailActivityConstants.Params.continuousMonthlySubscription);
                activityContext.addParam(ProductDetailActivityConstants.Params.continuousMonthlySubscription, Optional.ofNullable(continuousMonthlySubscription).orElse("否"));
                addSceneIdentifierRequiredDealAttrs(activityContext, dealIdAttrDTO);
            }
            return;
        }).join();
    }

    /**
     * 在上下文中加入属性名配置在lion（key为com.sankuai.dzviewscene.dealshelf.deal.detail.scene.identifier.required.deal.attrs.config）上的团详场景识别所需要的团单属性
     *
     * @param
     * @return
     */
    private void addSceneIdentifierRequiredDealAttrs(ActivityCxt activityContext, DealGroupAttributeDTO dealGroupAttributeDTO) {
        if (MapUtils.isEmpty(dealDetailSceneIdentifierRequiredDealAttrMap)) {
            return;
        }
        for (Map.Entry<String, String> entry : dealDetailSceneIdentifierRequiredDealAttrMap.entrySet()) {
            String dealAttrValue = getAttrValue(dealGroupAttributeDTO, entry.getValue());
            activityContext.addParam(entry.getKey(), Optional.ofNullable(dealAttrValue).orElse(""));
        }
    }

    private String getAttrValue(DealGroupAttributeDTO dealGroupAttributeDTO, String attrKey) {
        if (dealGroupAttributeDTO == null || CollectionUtils.isEmpty(dealGroupAttributeDTO.getAttributes())) {
            return null;
        }
        AttributeDTO attributeDTO = dealGroupAttributeDTO.getAttributes().stream().filter(attr -> attrKey.equals(attr.getName())).findFirst().orElse(null);
        if (attributeDTO == null) {
            return null;
        }
        return CollectUtils.firstValue(attributeDTO.getValue());
    }

    private CompletableFuture<DealGroupAttributeDTO> getDealAttrValuesFuture(long dealId) {
        List<Long> dealIds = Lists.newArrayList(dealId);
        List<Integer> intDealIds = dealIds.stream().map(Long::intValue).collect(Collectors.toList());
        List<String> allDealAttrNames = getDealDetailSceneIdentifierRequiredDealAttrNames();
        return compositeAtomService.batchGetDealAttribute(intDealIds, allDealAttrNames).thenApply(dealAttrValues -> {
            if (MapUtils.isEmpty(dealAttrValues)) {
                return null;
            }
            return CollectUtils.firstValue(dealAttrValues);
        });
    }

    /**
     * 获取团详场景识别所需要的团单属性名列表
     *
     * @param
     * @return
     */
    private List<String> getDealDetailSceneIdentifierRequiredDealAttrNames() {
        List<String> dealAttrNameList = new ArrayList<>();
        if (MapUtils.isEmpty(dealDetailSceneIdentifierRequiredDealAttrMap)) {
            return dealAttrNameList;
        }
        dealAttrNameList.addAll(dealDetailSceneIdentifierRequiredDealAttrMap.values());
        return dealAttrNameList;
    }

    private CompletableFuture<DealGroupAttributeDTO> getMtDealAttrValues(long dealId) {
        List<Long> dealIds = Lists.newArrayList(dealId);
        List<Integer> intDealIds = dealIds.stream().map(Long::intValue).collect(Collectors.toList());
        CompletableFuture<List<IdMapper>> idMappersCompletableFuture = compositeAtomService.batchGetDealIdByMtId(intDealIds);
        return idMappersCompletableFuture.thenCompose(idMappers -> {
            if (CollectionUtils.isEmpty(idMappers)) {
                return CompletableFuture.completedFuture(null);
            }
            return getMtAttrValuesFuture(idMappers);
        });
    }

    private CompletableFuture<DealGroupAttributeDTO> getMtAttrValuesFuture(List<IdMapper> idMappers) {
        if (CollectionUtils.isEmpty(idMappers)) {
            return CompletableFuture.completedFuture(null);
        }
        List<Integer> dpDealIds = idMappers.stream().map(idMapper -> idMapper.getDpDealGroupID()).collect(Collectors.toList());
        List<String> allDealAttrNames = getDealDetailSceneIdentifierRequiredDealAttrNames();
        CompletableFuture<Map<Integer, DealGroupAttributeDTO>> dealIdAttrDTOMapFuture = compositeAtomService.batchGetDealAttribute(dpDealIds, allDealAttrNames);
        if (dealIdAttrDTOMapFuture == null) {
            return CompletableFuture.completedFuture(null);
        }
        return dealIdAttrDTOMapFuture.thenCompose(dealIdAttrDTOMap -> {
            if (MapUtils.isEmpty(dealIdAttrDTOMap)) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(CollectUtils.firstValue(dealIdAttrDTOMap));
        });
    }
}
