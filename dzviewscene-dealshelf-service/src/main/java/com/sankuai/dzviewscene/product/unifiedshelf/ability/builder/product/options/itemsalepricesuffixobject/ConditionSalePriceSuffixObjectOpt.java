package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffixobject;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfSalePriceSuffixVO;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffix.ConditionSalePriceSuffixOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixObjectVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixVP;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "价格后缀-根据不同条件返回",
        description = "根据不同条件返回价格后缀，如/3次",
        code = ConditionSalePriceSuffixObjectOpt.CODE)
public class ConditionSalePriceSuffixObjectOpt extends UnifiedShelfSalePriceSuffixObjectVP<ConditionSalePriceSuffixObjectOpt.Config> {

    public static final String CODE = "ConditionSalePriceSuffixObjectOpt";

    private static final String TIMES_KEY = "sys_multi_sale_number";

    private static final String CAMPAIGN_PRICE_DESC = "campaginPriceDesc";

    private static final String IS_MULTI_SKU_KEY = "dealMultiSkuFieldAttr";

    private static final String RENT_DURATION_KEY = "rent_duration_1512";

    private static final String PRICE_UNIT = "priceunit";

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    private static final Integer PRODUCT_CATEGORY = 2200073;

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    private static final Map<String, String> START_WITH_ONE_UNIT = new HashMap<>();

    static {
        START_WITH_ONE_UNIT.put("1小时", "小时");
        START_WITH_ONE_UNIT.put("1天", "天");
        START_WITH_ONE_UNIT.put("1周", "周");
        START_WITH_ONE_UNIT.put("1月", "月");
        START_WITH_ONE_UNIT.put("1年", "年");
    }

    private static final Map<String, SuffixTextBuilder> builderMap = Maps.newHashMap();

    static {
        builderMap.put(SuffixType.TIMES.getCode(), getTimesDesc());
        builderMap.put(SuffixType.ADDITIONAL.getCode(), getAdditionalDesc());
        builderMap.put(SuffixType.CAMPAIGN_DESC.getCode(), getCampaignPriceDesc());
        builderMap.put(SuffixType.MULTI_SKU.getCode(), getMultiSkuDesc());
        builderMap.put(SuffixType.MINIPROGRAM.getCode(), getMiniprogramDesc());
        builderMap.put(SuffixType.RECYCLE_MINIPROGRAM.getCode(), getRecycleMiniprogramDesc());
        builderMap.put(SuffixType.RENT_CAR.getCode(), getRentCarDesc());
        builderMap.put(SuffixType.FANGXIN_CHANGE.getCode(), getFangxinChangeDesc());
    }

    @Override
    public ShelfSalePriceSuffixVO compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getSuffixTypes())) {
            return null;
        }
        for (String suffixType : config.getSuffixTypes()) {
            SuffixTextBuilder suffixTextBuilder = builderMap.get(suffixType);
            if (suffixTextBuilder == null) {
                continue;
            }
            ShelfSalePriceSuffixVO suffix = suffixTextBuilder.build(context, param);
            if (Objects.nonNull(suffix)) {
                return suffix;
            }
        }
        return null;
    }

    /**
     * 加项后缀
     */
    private static SuffixTextBuilder getAdditionalDesc(){
        return (context, param) -> {
            return param.getProductM().isAdditionalDeal() ? new ShelfSalePriceSuffixVO("起") : null;
        };
    }

    /**
     * 次数
     */
    private static SuffixTextBuilder getTimesDesc(){
        return (context, param) -> {
            String attr = param.getProductM().getAttr(TIMES_KEY);
            if (StringUtils.isBlank(attr) || !NumberUtils.isDigits(attr)) {
                return null;
            }
            return new ShelfSalePriceSuffixVO(String.format("/%s次", attr));
        };
    }

    /**
     * 团建后缀
     */
    private static SuffixTextBuilder getCampaignPriceDesc() {
        return (context, param) -> {
            return new ShelfSalePriceSuffixVO(param.getProductM().getAttr(CAMPAIGN_PRICE_DESC));
        };
    }

    /**
     * 多SKU价格后缀
     */
    private static SuffixTextBuilder getMultiSkuDesc() {
        return (context, param) -> {
            String isMultiSku = param.getProductM().getAttr(IS_MULTI_SKU_KEY);
            return "true".equals(isMultiSku) ? new ShelfSalePriceSuffixVO("起") : null;
        };
    }

    /**
     * 家电小程序商品价格后缀
     * @return
     */
    private static SuffixTextBuilder getMiniprogramDesc() {
        return (context, param) -> {
            return ApplianceShelfUtils.isMiniprogramLinked(param.getProductM()) ? new ShelfSalePriceSuffixVO("起") : null;
        };
    }

    /**
     * 回收小程序商品价格后缀
     * @return
     */
    private static SuffixTextBuilder getRecycleMiniprogramDesc() {
        return (context, param) -> {
            return new ShelfSalePriceSuffixVO("免费估价", 14);
        };
    }

    /**
     *  租车价格后缀
     * @return
     */
    private static SuffixTextBuilder getRentCarDesc() {
        return (context, param) -> {
            String duration = param.getProductM().getAttr(RENT_DURATION_KEY);
            if (StringUtils.isEmpty(duration)) {
                return null;
            }
            if (START_WITH_ONE_UNIT.containsKey(duration)) {
                duration = START_WITH_ONE_UNIT.get(duration);
            }
            return new ShelfSalePriceSuffixVO(String.format("/%s", duration));
        };
    }

    /**
     * 放心改后缀
     * @return
     */
    private static SuffixTextBuilder getFangxinChangeDesc() {
        return (context, param) -> {
            if ("true".equals(param.getProductM().getAttr(IS_FANGXIN_CHANGE))) {
                return new ShelfSalePriceSuffixVO(getSkuPriceUnit(param));
            }
            return null;
        };
    }

    private static String getSkuPriceUnit(Param param) {
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(param.getProductM().getAttr(ATTR_DEAL_STRUCT_DETAIL));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        return allSkus.stream()
                .filter(sku -> PRODUCT_CATEGORY == sku.getProductCategory())
                .findFirst()
                .map(ConditionSalePriceSuffixObjectOpt::extractPriceUnit)
                .orElse(null);
    }

    private static String extractPriceUnit(SkuItemDto skuItemDto) {
        if (skuItemDto == null || skuItemDto.getAttrItems() == null) {
            return null;
        }
        return skuItemDto.getAttrItems().stream()
                .filter(attr -> PRICE_UNIT.equals(attr.getAttrName()))
                .findFirst()
                .map(attrItem -> "/" + attrItem.getAttrValue()) //添加"/"
                .orElse(null);
    }

    @FunctionalInterface
    public interface SuffixTextBuilder {
        ShelfSalePriceSuffixVO build(ActivityCxt context, Param param);
    }

    public enum SuffixType {
        ADDITIONAL("additional", "加项团单"),
        TIMES("times",  "多次卡次数"),
        CAMPAIGN_DESC("campaign", "团建后缀"),
        MULTI_SKU("multiSku", "多SKU后缀"),
        MINIPROGRAM("miniprogram", "家电小程序后缀"),
        RECYCLE_MINIPROGRAM("recycleMiniprogram", "回收小程序后缀"),
        RENT_CAR("rentCar", "租车后缀"),
        FANGXIN_CHANGE("fangxinChange", "放心改后缀")
        ;

        @Getter
        private String code;
        private String desc;

        SuffixType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 后缀类型，按顺序配置优先级，配置值见SuffixType枚举
         */
        private List<String> suffixTypes;
    }
}
