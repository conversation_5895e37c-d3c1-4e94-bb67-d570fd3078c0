package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils.getSalePrice;

@VPointOption(name = "到综商家会员团购列表页优惠感知版本",
        description = "",
        code = "ShopMerchantMemberPriceBottomTagOpt")
public class MerchantMemberPriceBottomTagOpt extends ProductPriceBottomTagVP<MerchantMemberPriceBottomTagOpt.Config> {
    private static final String ATTR_NAME = "MEMBER_EXCLUSIVE";

    @Override
    public List<DzTagVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (Objects.isNull(config)) {
            return new ArrayList<>();
        }

        DzTagVO promoDzTagVO = buildPriceBottomTag(param, config);
        if (Objects.isNull(promoDzTagVO)) {
            return new ArrayList<>();
        }

        DzTagStyleWrapUtils.overridePromoStyle(promoDzTagVO);
        return Lists.newArrayList(promoDzTagVO);
    }

    private DzTagVO buildPriceBottomTag(ProductPriceBottomTagVP.Param param, MerchantMemberPriceBottomTagOpt.Config config) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());

        // 预售优先级最高
        DzTagVO preSaleDzTag = buildPreSaleTag( promoPriceMMap, config);
        if (Objects.nonNull(preSaleDzTag)) {
            return preSaleDzTag;
        }

        //会员价优惠
        //包含商家会员优惠
        boolean hasMerchantMemberPromo = MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(param.getProductM().getPromoPrices());
        if(MapUtils.isNotEmpty(promoPriceMMap) && hasMerchantMemberPromo){
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberPromo(param.getProductM());
            if(merchantMemberPromo.getProductPromoPrice() != null){
                return ProductMPromoInfoUtils.buildMerchantMemberPromoTag(param.getProductM(), param.getPlatform(), param.getSalePrice(), merchantMemberPromo, config.getPopType());
            }
        }

        // 非会员标签
        // 这里的会员标签主要是次卡、折扣卡带来的现金优惠，和商场会员专属团购的"会员专属"不一样
        ProductPromoPriceM noMemberPromoM = this.getNoMemberPromoM(param);
        // 判断是否为会员专属
        boolean isMemberExclusive = Objects.nonNull(param.getProductM().getAttr(ATTR_NAME)) && Boolean.parseBoolean(param.getProductM().getAttr(ATTR_NAME));
        if (isMemberExclusive) {
            return buildMemberExclusiveTag(param, noMemberPromoM, config);
        }

        if (Objects.nonNull(noMemberPromoM) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            // 有秒杀，返回秒杀标签
            return buildSecKillTag(param, noMemberPromoM, config);
        } else if (Objects.nonNull(noMemberPromoM)) {
            // 非会员、非秒杀
            return buildNoMemberTag(noMemberPromoM, config);
        }
        return null;
    }

    private ProductPromoPriceM getNoMemberPromoM(ProductPriceBottomTagVP.Param param) {
        if (CollectionUtils.isEmpty(param.getProductM().getPromoPrices())) {
            return null;
        }
        List<ProductPromoPriceM> noMemberPriceM = param.getProductM().getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        //过滤商家会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Merchant_Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    /**
     * 判断并构建秒杀标签
     *
     * @param param
     * @param noMemberPromoM
     * @param config
     * @return
     */
    private DzTagVO buildSecKillTag(ProductPriceBottomTagVP.Param param, ProductPromoPriceM noMemberPromoM, MerchantMemberPriceBottomTagOpt.Config config) {
        Cat.logEvent("INVALID_METHOD_3", "c.s.d.product.filterlist.option.builder.product.pricebottomtag.MerchantMemberPriceBottomTagOpt.buildSecKillTag(ProductPriceBottomTagVP$Param,ProductPromoPriceM,MerchantMemberPriceBottomTagOpt$Config)");
        // 秒杀单独实现
        // 商场会员团购列表页样式，只有一种样式
        DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(noMemberPromoM), param.getProductM().getMarketPrice(), param.getSalePrice(), VCPlatformEnum.MT.getType(), config.getPopType());
        if (Objects.nonNull(secKillDzTag)) {
            secKillDzTag.setPrePic(new DzPictureComponentVO(ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL, config.getMtPriceBottomTagPrePicAspectRadio()));
            secKillDzTag.setAfterPic(this.buildAfterPic(false));
            secKillDzTag.setBorderRadius(3);
            return secKillDzTag;
        }
        return null;
    }


    /**
     * 判断并构建预售标签
     *
     * @param promoPriceMMap
     * @param config
     * @return
     */
    private DzTagVO buildPreSaleTag( Map<Integer, ProductPromoPriceM> promoPriceMMap, MerchantMemberPriceBottomTagOpt.Config config) {
        if (MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }
        // 预售优先级最高
        ProductPromoPriceM preSalePromoPrice = null;
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if(promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_MerchantMember.getCode())){
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_MerchantMember.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }
        if (Objects.nonNull(preSalePromoPrice)) {
            return this.buildNoMemberTag(preSalePromoPrice, config);
        }
        return null;
    }

    private DzTagVO buildNoMemberTag(ProductPromoPriceM productPromoPriceM, MerchantMemberPriceBottomTagOpt.Config config) {
        DzTagVO dzTagVO = this.buildPromoTagVo(productPromoPriceM);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(productPromoPriceM.getIcon(), config.getMtPriceBottomTagPrePicAspectRadio()));
        dzTagVO.setAfterPic(this.buildAfterPic(false));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, config.getPopType()));
        dzTagVO.setBorderRadius(3);
        return dzTagVO;
    }

    private DzTagVO buildMemberExclusiveTag(ProductPriceBottomTagVP.Param param, ProductPromoPriceM promoPriceM, MerchantMemberPriceBottomTagOpt.Config config) {
        String text = "共省¥" + new BigDecimal(param.getProductM().getMarketPrice()).subtract(getSalePrice(param.getSalePrice()));
        //会员团购列表页样式，只有一种样式
        DzTagVO memberDzTagVO = DzPromoUtils.buildBasicDzTagVOWithColor(VCPlatformEnum.MT.getType(), text,
                ColorUtils.color222222, "#EAA37C", null, "#8E3C12", "#FFEDDE");
        if (Objects.isNull(memberDzTagVO)) {
            return null;
        }
        String prePic = "https://p0.meituan.net/travelcube/dab2626832fe4afdc3da732eab1b66398113.png";
        memberDzTagVO.setPrePic(new DzPictureComponentVO(prePic, config.getMtMemberPriceBottomTagPrePicAspectRadio()));
        memberDzTagVO.setAfterPic(this.buildAfterPic(true));
        ProductPromoPriceM hasDefaultPromoPriceM = CollectionUtils.isNotEmpty(param.getProductM().getPromoPrices()) ? param.getProductM().getPromoPrices().get(0) : null;
        memberDzTagVO.setPromoDetail(Objects.nonNull(promoPriceM) ? DzPromoUtils.buildPromoDetail(promoPriceM, config.getPopType()) : DzPromoUtils.buildPromoDetail(hasDefaultPromoPriceM, config.getPopType()));
        memberDzTagVO.setNoGapBetweenPicText(true);
        memberDzTagVO.setBorderRadius(3);
        return memberDzTagVO;
    }


    /**
     * 构建 AfterPic
     */
    private DzPictureComponentVO buildAfterPic(boolean isMemberExclusive) {
        if (isMemberExclusive) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/225b0857cf113c81778252c89a735b76449.png", 0.875);

        }
        return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private DzTagVO buildPromoTagVo(ProductPromoPriceM productPromoPriceM) {
        //商场会员团购列表页样式，只有一种样式
        int platform = VCPlatformEnum.MT.getType();
        DzTagVO basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);

        return basicTagVo;
    }


    @VPointCfg
    @Data
    public static class Config {
        private int popType = 3;

        /**
         * 美团侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtPriceBottomTagPrePicAspectRadio = 3.25;
        /**
         * 点评侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpPriceBottomTagPrePicAspectRadio = 3.25;

        /**
         * 美团侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtMemberPriceBottomTagPrePicAspectRadio = 3.0;
        /**
         * 点评侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpMemberPriceBottomTagPrePicAspectRadio = 3.0;
    }
}
