package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreText;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.LeUnCoopShopUniverseInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreTextVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-06-18
 * @description: LE非合作POI推荐列表查看更多商品文案
 */
@VPointOption(name = "LE非合作POI推荐列表查看更多商品文案", description = "LE非合作POI推荐列表查看更多商品文案", code = LEUncoopShopMoreJumpTextOpt.CODE)
public class LEUncoopShopMoreJumpTextOpt extends DealFilterListMoreTextVP<LEUncoopShopMoreJumpTextOpt.Config> {

    public static final String CODE = "LEUncoopShopMoreJumpTextOpt";

    private static final String DEFAULT_TEMPLATE = "查看更多%s个商品";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        LeUncoopShopShelfAttrM leUncoopShopShelfAttrM = activityCxt.getParam(LeUnCoopShopUniverseInfoOpt.CODE);
        if (leUncoopShopShelfAttrM == null || leUncoopShopShelfAttrM.getDefaultShowNum() == null) {
            return null;
        }
        int defaultShowNum = leUncoopShopShelfAttrM.getDefaultShowNum();
        List<DzProductVO> products = activityCxt.getSource(DealListBuilder.CODE);
        int totalProductNum = products.size();
        int moreDealCount = totalProductNum - defaultShowNum;
        if (moreDealCount <= 0) {
            return null;
        }
        String configTemplate = leUncoopShopShelfAttrM.getMoreShowText();
        return formatMoreShowText(configTemplate, moreDealCount);
    }

    private String formatMoreShowText(String configTemplate, int moreDealCount) {
        if (StringUtils.isBlank(configTemplate)) {
            return String.format(DEFAULT_TEMPLATE, moreDealCount);
        }
        String template = configTemplate.replaceAll("[xX]", "%s");
        if (template.contains("%s")) {
            return String.format(template, moreDealCount);
        }
        return String.format(DEFAULT_TEMPLATE, moreDealCount);
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
