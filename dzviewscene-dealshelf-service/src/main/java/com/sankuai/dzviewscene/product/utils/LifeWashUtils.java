package com.sankuai.dzviewscene.product.utils;

import com.dianping.appkit.utils.VersionUtil;
import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemShowTypeVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemshowtype.LifeWashItemShowTypeOpt;
import com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.stream.Collectors;

public class LifeWashUtils {

    public static final String APPKEY = "com.sankuai.dzviewscene.dealshelf";

    public static final String SHOWTYPE_SWITCH = "com.sankuai.dzviewscene.dealshelf.wash.huanxi.newshow.switch";

    public static boolean hitWashNewShowType(ProductM productM) {
        if (productM == null || !Lion.getBoolean(LifeWashUtils.APPKEY, LifeWashUtils.SHOWTYPE_SWITCH, false)) {
            return false;
        }
        return CollectionUtils.isNotEmpty(productM.getActivities()) &&
                CollectionUtils.isNotEmpty(productM.getActivities().stream().filter(m -> m.getShelfActivityType() != null && m.getShelfActivityType() == ExposureActivityTypeEnum.CLASS_S.getType()).collect(Collectors.toList()));
    }

    public static Integer defItemShowTypeOptCompute(ActivityCxt activityCxt, ItemShowTypeVP.Param param, LifeWashItemShowTypeOpt.Config config) {
        //1. check version
        if (isLowVersion(param, config)) {
            return DzItemShowTypeEnums.DEFAULT.getType();
        }
        //2. ab
        if (MapUtils.isNotEmpty(config.getDouHuSk2ShowType())) {
            for (DouHuM douHuM : param.getDouHuList()) {
                Integer configDouHuShowType = config.getDouHuSk2ShowType().get(douHuM.getSk());
                if (configDouHuShowType != null) {
                    return configDouHuShowType;
                }
            }
        }
        //3. def
        return config.getItemShowType();
    }

    public static boolean isLowVersion(ItemShowTypeVP.Param param, LifeWashItemShowTypeOpt.Config config) {
        String appVersion = param.getAppVersion();
        int platform = param.getPlatform();
        String limitVersion = config.getDpLimitVersion();
        if (PlatformUtil.isMT(platform)) {
            limitVersion = config.getMtLimitVersion();
        }
        return StringUtils.isEmpty(limitVersion) || VersionUtil.compare(appVersion, limitVersion) <= 0;
    }
}
