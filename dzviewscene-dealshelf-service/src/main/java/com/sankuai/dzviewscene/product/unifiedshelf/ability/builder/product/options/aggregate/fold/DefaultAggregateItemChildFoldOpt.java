package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.fold;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemChildFoldVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;


@VPointOption(name = "聚合卡片-子项折叠",
        description = "聚合卡片-子项折叠",
        code = DefaultAggregateItemChildFoldOpt.CODE,
        isDefault  = true)
public class DefaultAggregateItemChildFoldOpt extends AggregateItemChildFoldVP<DefaultAggregateItemChildFoldOpt.Config> {

    public static final String CODE = "DefaultAggregateItemChildFoldOpt";

    private static final String DEFAULT_TEXT_FORMAT = "全部%d个套餐";

    @Override
    public Void compute(ActivityCxt activityCxt, Param param, Config config) {
        //未配置不折叠
        if(config.getDefaultShowNum() <= 0 || CollectionUtils.isEmpty(param.getShelfItemVO().getSubItems())) {
            return null;
        }
        ShelfItemVO shelfItemVO = param.getShelfItemVO();
        shelfItemVO.setDefaultShowNum(config.getDefaultShowNum());
        shelfItemVO.setMoreText(buildChildrenMoreText(config, shelfItemVO));
        return null;
    }

    private String buildChildrenMoreText(Config config, ShelfItemVO shelfItemVO) {
        if (shelfItemVO.getSubItems().size() <= config.getDefaultShowNum()) {
            return null;
        }
        String textFormat = StringUtils.isNotEmpty(config.getTextFormat()) ? config.getTextFormat() : DEFAULT_TEXT_FORMAT;
        return String.format(textFormat, shelfItemVO.getSubItems().size());
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 子项默认展示数
         */
        private int defaultShowNum = 2;

        /**
         * 子项更多文案，%d表示个数
         */
        private String textFormat;
    }
}
