/*
 * Create Author  : liyanmin
 * Create Date    : 2023-10-24
 * Project        :
 * File Name      : ItemFloatTagPicVP.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.FloatTagGroup;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2023-10-24
 * @since dzviewscene-dealshelf-home 1.0
 */
@VPoint(name = "商品图片标签",
        description = "商品-图片标签",
        code = ItemFloatTagPicVP.CODE,
        ability = FloorsBuilder.CODE)
public abstract class ItemFloatTagPicVP<T> extends PmfVPoint<FloatTagGroup, ItemFloatTagPicVP.Param, T> {

    public static final String CODE = "ItemFloatTagPicVP";
    
    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private List<DouHuM> douHuList;
        
        private long filterId;

        private int platform;

        private int index;
    }

}
