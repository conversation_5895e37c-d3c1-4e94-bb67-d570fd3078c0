package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils;

import com.dianping.vc.sdk.dp.config.LionObject;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.OceanCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.RegExCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.SceneCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.ShelfOceanCfgBean;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * @see com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils.OceanConfigUtils
 */
public class UnifiedOceanConfigUtils {

    private static LionObject<OceanCfgBean> oceanConstantConfigObject = LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.constant.config", OceanCfgBean.class);

    private static LionObject<UnifiedShelfOceanCfgBean> shelfOceanConfigConfigObject = LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.config", UnifiedShelfOceanCfgBean.class);

    private static LionObject<Boolean> switchOffLionObject = LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.switch.off.config", Boolean.class);

    private static LionObject<RegExCfgBean> regExConstantLionObject = LionObject.create("com.sankuai.dzviewscene.dealshelf.poi.shelf.ocean.reg.ex.config", RegExCfgBean.class);

    /**
     * 是否关闭开关
     */
    public static boolean switchOff() {
        return switchOffLionObject == null || switchOffLionObject.getObject() == null || switchOffLionObject.getObject();
    }

    /**
     * 获取正则表达式
     */
    public static RegExCfgBean getRegExConstant() {
        if (regExConstantLionObject.getObject() == null) {
            return null;
        }
        return regExConstantLionObject.getObject();
    }

    /**
     * 根据平台获取货架配置
     */
    public static ShelfOceanVO getShelfOceanByPlatform(int platform) {
        if (shelfOceanConfigConfigObject == null || shelfOceanConfigConfigObject.getObject() == null ) {
            return null;
        }
        if (PlatformUtil.isMT(platform)) {
            return shelfOceanConfigConfigObject.getObject().getMtOcean();
        }
        return shelfOceanConfigConfigObject.getObject().getDpOcean();
    }

    /**
     * 根据场景获取类型
     */
    public static String getTypeByConfig(String sceneCode) {
        SceneCfgBean oceanConfig = getOceanConfigBySceneCode(sceneCode);
        if (oceanConfig == null) {
            return null;
        }
        return oceanConfig.getType();
    }

    /**
     * 根据场景获取配置
     */
    public static SceneCfgBean getOceanConfigBySceneCode(String sceneCode) {
        if (StringUtils.isEmpty(sceneCode) || !validOceanConstantConfigObject()) {
            return null;
        }
        return oceanConstantConfigObject.getObject().getOceanConfigList().stream()
                .filter(config -> sceneCode.equals(config.getSceneCode()))
                .findFirst()
                .orElse(null);
    }

    private static boolean validOceanConstantConfigObject() {
        return oceanConstantConfigObject != null
                && oceanConstantConfigObject.getObject() != null
                && CollectionUtils.isNotEmpty(oceanConstantConfigObject.getObject().getOceanConfigList());
    }

}
