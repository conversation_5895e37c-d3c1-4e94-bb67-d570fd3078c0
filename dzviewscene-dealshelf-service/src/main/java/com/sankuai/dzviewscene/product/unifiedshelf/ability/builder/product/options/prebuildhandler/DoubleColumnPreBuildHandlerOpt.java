package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.prebuildhandler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaPreBuildHandlerVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;


@VPointOption(name = "双列货架前置处理",
        description = "双列货架前置处理, 用于货架单双列共存场景",
        code = DoubleColumnPreBuildHandlerOpt.CODE)
public class DoubleColumnPreBuildHandlerOpt extends ProductAreaPreBuildHandlerVP<DoubleColumnPreBuildHandlerOpt.Config> {

    public static final String CODE = "DoubleColumnPreBuildHandlerOpt";

    @Override
    public Void compute(ActivityCxt context, Param param, Config config) {
        //端
        int userAgent = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
        if (BooleanUtils.isTrue(config.isLimitApp()) && !PlatformUtil.isApp(userAgent)) {
            return null;
        }
        int totalCount = context.getParam(ShelfActivityConstants.Params.totalProductNum);
        if(totalCount >= config.getDoubleColumnMinNum()){
            context.addParam(ShelfActivityConstants.Params.doubleColumnShelf, 1);
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config{

        /**
         * 双列货架最小商品数
         */
        private int doubleColumnMinNum;

        /**
         * 是否仅限制app端
         */
        private boolean limitApp;
    }
}
