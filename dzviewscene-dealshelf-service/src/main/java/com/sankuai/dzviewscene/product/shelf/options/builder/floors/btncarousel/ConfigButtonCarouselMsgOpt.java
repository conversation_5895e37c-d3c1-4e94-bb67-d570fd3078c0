/*
 * Create Author  : liyanmin
 * Create Date    : 2024-04-22
 * Project        :
 * File Name      : ConfigButtonCarouselMsgOpt.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-04-22
 * @since dzviewscene-dealshelf-home 1.0
 */
@VPointOption(name = "按钮轮播信息",
        description = "",
        code = "ConfigButtonCarouselMsgOpt")
public class ConfigButtonCarouselMsgOpt extends ItemButtonCarouselMsgVP<ConfigButtonCarouselMsgOpt.Config> {

    @Autowired
    private ButtonCarouselMsgStrategyFactory carouselMsgStrategyFactory;


    @Override
    public List<RichLabelVO> compute(ActivityCxt context, Param param, Config config) {
        if(Objects.isNull(param) || Objects.isNull(param.getProductM())
                || CollectionUtils.isEmpty(config.getCarouselStrategy())){
            return null;
        }
        //
        List<RichLabelVO> results = Lists.newArrayList();
        CarouselBuilderContext builderContext = new CarouselBuilderContext();
        builderContext.setProductM(param.getProductM());
        for(String strategyName : config.carouselStrategy){
            ButtonCarouselMsgStrategy carouselMsgStrategy = carouselMsgStrategyFactory.getStrategy(strategyName);
            if(Objects.isNull(carouselMsgStrategy)){
                continue;
            }
            RichLabelVO carouselMsg = carouselMsgStrategy.build(builderContext);
            if(Objects.nonNull(carouselMsg)){
                results.add(carouselMsg);
            }
        }
        return results;
    }

    @VPointCfg
    @Data
    public static class Config {
        
        private List<String> carouselStrategy;

    }
    
}
