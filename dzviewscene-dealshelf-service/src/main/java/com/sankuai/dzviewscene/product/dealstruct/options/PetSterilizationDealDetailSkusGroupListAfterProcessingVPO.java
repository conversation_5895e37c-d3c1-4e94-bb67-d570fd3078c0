package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.cpv.wiki.service.AIGenerateContentQueryService;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:43 上午
 */
@VPointOption(name = "宠物绝育团购详情sku货列表组列表后置处理变化点", description = "宠物绝育团购详情sku货列表组列表后置处理变化点",code = PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.CODE, isDefault = false)
public class PetSterilizationDealDetailSkusGroupListAfterProcessingVPO extends DealDetailSkusGroupListAfterProcessingVP<PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.Config> {

    public static final String CODE = "PetSterilizationDealDetailSkusGroupListAfterProcessingVPO";

    private static final String SKU_CATEGORY_ATTR_NAME = "货类别";

    private static final String CONTAINING_ITEMS = "包含项目";

    private static final String OPTIONAL_ITEMS = "可选项目";

    @RpcClient(url = "com.sankuai.dzshoppingguide.product.insight.service.api.cpv.wiki.service.AIGenerateContentQueryService")
    private AIGenerateContentQueryService aiGenerateContentQueryService;

    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.Config config) {
        //1.获取需要处理的skus列表组
        List<DealSkuGroupModuleVO> skuGroups = param.getSkuGroups();
        //2.对skus列表组做后续处理
        return processSkuGroups(skuGroups, config, param.getDealTitle(), param.getMarketPrice(), context);
    }

    private List<DealSkuGroupModuleVO> processSkuGroups(List<DealSkuGroupModuleVO> skuGroups, Config config, String dealTitle, String marketPrice, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        //1.利用skuGroups构造dealSkuVO
        DealSkuVO dealSkuVO = buildDealSkuVOBySkuGroups(skuGroups, config, dealTitle, marketPrice, context);
        if (dealSkuVO == null) {
            return null;
        }
        //2.利用dealSkuVO构造DealSkuGroupModuleVO列表
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(Lists.newArrayList(dealSkuVO));
        return Lists.newArrayList(dealSkuGroupModuleVO);
    }

    private DealSkuVO buildDealSkuVOBySkuGroups(List<DealSkuGroupModuleVO> skuGroups, Config config, String dealTitle, String marketPrice, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //1.设置sku标题为团单标题
        dealSkuVO.setTitle(dealTitle);
        //2.设置sku价格为团单市场价
        String price = StringUtils.isEmpty(config.getMarketPriceFormat()) ? marketPrice : String.format(config.getMarketPriceFormat(), marketPrice);
        dealSkuVO.setPrice(price);
        //3.构造sku属性列表
        dealSkuVO.setItems(buildDealSkuAttrList(skuGroups, config, context));
        return dealSkuVO;
    }

    private List<DealSkuItemVO> buildDealSkuAttrList(List<DealSkuGroupModuleVO> skuGroups, Config config, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        return skuGroups.stream().map(skuGroup -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            //1.sku属性标题为原sku组标题（服务流程、X选择X项目等）
            dealSkuItemVO.setName(skuGroup.getTitle());
            //2.利用原suk列表构造sku属性值列表
            boolean isMustGroup = StringUtils.isEmpty(skuGroup.getTitle()) || skuGroup.getTitle().equals(config.getMustSkuGroupName());
            List<SkuAttrAttrItemVO> skuAttrs = buildSkuAttrList(skuGroup.getDealSkuList(), isMustGroup, config, context);
            dealSkuItemVO.setValueAttrs(skuAttrs);
            //3.设置展示样式
            dealSkuItemVO.setType(isMustGroup ? config.getMustSkuAttrType() : config.getOptionalSkuAttrType());
            return dealSkuItemVO;
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private List<SkuAttrAttrItemVO> buildSkuAttrList(List<DealSkuVO> dealSkuList, boolean isMustSkuGroup, Config config, ActivityCxt context) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        //按照sku category对sku列表进行分类
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = getSkuCategory2SkuListMap(dealSkuList);
        if (MapUtils.isEmpty(skuCategory2SkuListMap)) {
            return null;
        }
        // todo 获取宠物服务项目中的AI生成的内容
        CompletableFuture<Object> singleValueWikiCF = CompletableFuture.supplyAsync(() -> getSingleValueWiki(skuCategory2SkuListMap));
        CompletableFuture.supplyAsync(()-> getMultiValueWiki());
        aiGenerateContentQueryService.

        // todo 判断宠物服务项目中的值是否有AI生成的内容，若有则通过AI生成的内容展示AI弹窗，否则展示原弹窗
        boolean isPetCpvWithAiExplain = isPetCpvWithAiExplain(skuCategory2SkuListMap);

        return skuCategory2SkuListMap.entrySet().stream().map(entry -> {
            if (entry == null) {
                return null;
            }
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            //原sku分类名作为二级属性名
            skuAttrAttrItemVO.setName(entry.getKey());
            //利用原sku列表构造二级属性值
            List<CommonAttrVO> skuSecondAttrValueList = buildSkuSecondAttrValues(entry.getValue(), isMustSkuGroup, config.doGetSkuTitleCfgByCategory(entry.getKey()));
            skuAttrAttrItemVO.setValues(skuSecondAttrValueList);
            //利用原sku属性，构造弹窗


            return skuAttrAttrItemVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ContentQueryBaseResponse getSingleValueWiki(Map<String, List<DealSkuVO>> skuCategory2SkuListMap) {
        CPVSingleValueWikiQueryRequest request = new CPVSingleValueWikiQueryRequest();
        request.setKeys(skuCategory2SkuListMap.entrySet().stream().map(entry -> {
            if (entry == null) {
                return null;
            }
            return entry.getValue().stream().map(DealSkuVO::getTitle).collect(Collectors.toSet());
        }).collect(Collectors.toSet()));
        return aiGenerateContentQueryService.batchQuery(request);
    }

    private

    private boolean isPetCpvWithAiExplain(Map<String, List<DealSkuVO>> skuCategory2SkuListMap) {
        return false;
    }

    private void buildPopup(boolean isPetCpvWithAiExplain, Map.Entry<String, List<DealSkuVO>> entry, Config config,
            ActivityCxt context, SkuAttrAttrItemVO skuAttrAttrItemVO) {
        // 判断宠物服务流程中的步骤是否有AI生成的解释，若有则通过AI生成的内容展示AI弹窗，否则展示原弹窗。
        if (isPetCpvWithAiExplain) {

        } else {
            PopUpWindowVO popUpWindowVO = buildPopupWindowVO(context, config, entry.getKey(), entry.getValue());
            skuAttrAttrItemVO.setPopup(popUpWindowVO);
        }
    }

    private void buildPopupWindowWikiVO(String value) {
        // todo 服务流程中分为多个步骤，每个步骤有多个内容，某个步骤下每条内容都有ai生成，则只需要在该步骤构建弹窗，否则只在每条内容旁构建弹窗
        PopUpWindowWikiVO popupWindowWikiVO = new PopUpWindowWikiVO();


    }



    private

    private PopUpWindowVO buildPopupWindowVO(ActivityCxt context, Config config, String title, List<DealSkuVO> value) {
        if (MapUtils.isEmpty(config.getPopUpTileSubTitleMap())){
            return null;
        }
        String subTitle = config.getPopUpTileSubTitleMap().get(title);
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        List<CommonAttrVO> popInfos = buildPopupInfos(value);
        if (CollectionUtils.isEmpty(popInfos)){
            return null;
        }
        popUpWindowVO.setInfos(popInfos);
        popUpWindowVO.setContent(subTitle);
        popUpWindowVO.setTitle(title);
        popUpWindowVO.setIcon(config.getPopUpDefaultIcon());
        return popUpWindowVO;
    }

    private List<CommonAttrVO> buildPopupInfos(List<DealSkuVO> skuVOS) {
        List<CommonAttrVO> details = Lists.newArrayList();
        for (DealSkuVO skuVO : skuVOS){
            if (skuVO == null || CollectionUtils.isEmpty(skuVO.getItems())){
                continue;
            }
            Optional<DealSkuItemVO> descOpt = skuVO.getItems().stream().filter(entity -> entity.getName().equals("projectDesc")).findAny();
            if (descOpt.isPresent()){
                CommonAttrVO commonAttrVO = new CommonAttrVO();
                commonAttrVO.setValue(descOpt.get().getValue());
                commonAttrVO.setName(skuVO.getTitle());
                details.add(commonAttrVO);
            }
        }
        return details;
    }

    private List<CommonAttrVO> buildSkuSecondAttrValues(List<DealSkuVO> dealSkuVOS, boolean isMustSkuGroup, SkuTitleCfg skuTitleCfg) {
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        //构造sku二级属性名，原sku组为必选组时展示"包含项目"，否则展示"X选X项目"
        String name = isMustSkuGroup ? CONTAINING_ITEMS : OPTIONAL_ITEMS;
        commonAttrVO.setName(name);
        //构造sku二级属性值
        List<CommonAttrsVO> commonAttrsVOS = dealSkuVOS.stream().map(sku -> {
            CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
            //sku三级属性名为sku名
            commonAttrsVO.setName(skuTitleCfg == null ? sku.getTitle() : skuTitleCfg.buildSkuTitleByCfg(sku));
            if (StringUtils.isNotEmpty(sku.getCopies())) {
                //sku三级属性值为sku份数；因为展示层的因素，需要手动插入一个 null 值，使得份数能放在最右侧的位置
                commonAttrsVO.setValues(Lists.newArrayList(null, sku.getCopies()));
            }
            return commonAttrsVO;
        }).collect(Collectors.toList());
        commonAttrVO.setCommonAttrs(commonAttrsVOS);
        return Lists.newArrayList(commonAttrVO);
    }

    private Map<String, List<DealSkuVO>> getSkuCategory2SkuListMap(List<DealSkuVO> dealSkuVOS) {
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = new LinkedHashMap<>();
        for (DealSkuVO dealSkuVO : dealSkuVOS) {
            String skuCategory = getSkuCategory(dealSkuVO);
            //按照sku类别进行分类
            if (!skuCategory2SkuListMap.containsKey(skuCategory) || skuCategory2SkuListMap.get(skuCategory) == null) {
                List<DealSkuVO> dealSkuVOList = new ArrayList<>();
                dealSkuVOList.add(dealSkuVO);
                skuCategory2SkuListMap.put(skuCategory, dealSkuVOList);
                continue;
            }
            skuCategory2SkuListMap.get(skuCategory).add(dealSkuVO);
        }
        return skuCategory2SkuListMap;
    }

    private String getSkuCategory(DealSkuVO dealSkuVO) {
        if (dealSkuVO == null || CollectionUtils.isEmpty(dealSkuVO.getItems())) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = dealSkuVO.getItems().stream().filter(item -> SKU_CATEGORY_ATTR_NAME.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private static String getSkuItemValueBySkuItemName(List<DealSkuItemVO> items, String name) {
        if (CollectionUtils.isEmpty(items) || StringUtils.isEmpty(name)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = items.stream().filter(item -> name.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    @Data
    @VPointCfg
    public static class Config {
        //必选sku组名，用来判断当前sku组是否是必选sku组
        private String mustSkuGroupName;
        //必选sku前端样式
        private int mustSkuAttrType;
        //可选sku前端样式
        private int optionalSkuAttrType;
        //市场价format
        private String marketPriceFormat;
        //skuTitle 的二次拼接规则
        private Map<String, SkuTitleCfg> skuCategory2TitleCfg;
        /**
         * 弹窗实验AB配置，实验完可拿掉
         */
        private List<String> douhuExpIds;;
        /**
         * 弹窗文案副标题映射
         */
        private Map<String,String> popUpTileSubTitleMap;
        /**
         * 弹窗icon
         */
        private String popUpDefaultIcon = "https://p0.meituan.net/ingee/d97872ac6f4f0a1396eede65dee27e9e1265.png";

        public SkuTitleCfg doGetSkuTitleCfgByCategory(String skuCategory) {
            if (MapUtils.isEmpty(this.skuCategory2TitleCfg)) {
                return null;
            }
            return this.skuCategory2TitleCfg.get(skuCategory);
        }
    }

    @Data
    private static class SkuTitleCfg {
        /**
         * 拼接格式（固定 title 是第一个参数，第二个参数是 attrValue）
         */
        private String format;

        /**
         * 参与拼接的属性key
         */
        private List<String> attrKeys;

        /**
         * 连词符
         */
        private String separator;

        public String buildSkuTitleByCfg(DealSkuVO dealSkuVO) {
            String defTitle = dealSkuVO.getTitle();
            if (StringUtils.isEmpty(this.format) || CollectionUtils.isEmpty(this.attrKeys)) {
                return defTitle;
            }
            List<String> values = this.attrKeys.stream()
                    .map(key -> getSkuItemValueBySkuItemName(dealSkuVO.getItems(), key))
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(values)) {
                return defTitle;
            }
            //拼接标题和属性
            return String.format(this.format, defTitle, StringUtils.join(values, this.separator));
        }
    }
}
