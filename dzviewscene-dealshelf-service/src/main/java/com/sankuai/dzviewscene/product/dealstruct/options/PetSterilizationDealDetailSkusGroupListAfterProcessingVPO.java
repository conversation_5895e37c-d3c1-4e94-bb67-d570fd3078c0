package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVWikiContentDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.cpv.wiki.service.AIGenerateContentQueryService;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:43 上午
 */
@Slf4j
@VPointOption(name = "宠物绝育团购详情sku货列表组列表后置处理变化点", description = "宠物绝育团购详情sku货列表组列表后置处理变化点",code = PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.CODE, isDefault = false)
public class PetSterilizationDealDetailSkusGroupListAfterProcessingVPO extends DealDetailSkusGroupListAfterProcessingVP<PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.Config> {

    public static final String CODE = "PetSterilizationDealDetailSkusGroupListAfterProcessingVPO";

    private static final String SKU_CATEGORY_ATTR_NAME = "货类别";

    private static final String CONTAINING_ITEMS = "包含项目";

    private static final String OPTIONAL_ITEMS = "可选项目";

    @RpcClient(url = "com.sankuai.dzshoppingguide.product.insight.service.api.cpv.wiki.service.AIGenerateContentQueryService")
    private AIGenerateContentQueryService aiGenerateContentQueryService;

    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, PetSterilizationDealDetailSkusGroupListAfterProcessingVPO.Config config) {
        //1.获取需要处理的skus列表组
        List<DealSkuGroupModuleVO> skuGroups = param.getSkuGroups();
        //2.对skus列表组做后续处理
        return processSkuGroups(skuGroups, config, param.getDealTitle(), param.getMarketPrice(), context);
    }

    private List<DealSkuGroupModuleVO> processSkuGroups(List<DealSkuGroupModuleVO> skuGroups, Config config, String dealTitle, String marketPrice, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        //1.利用skuGroups构造dealSkuVO
        DealSkuVO dealSkuVO = buildDealSkuVOBySkuGroups(skuGroups, config, dealTitle, marketPrice, context);
        if (dealSkuVO == null) {
            return null;
        }
        //2.利用dealSkuVO构造DealSkuGroupModuleVO列表
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(Lists.newArrayList(dealSkuVO));
        return Lists.newArrayList(dealSkuGroupModuleVO);
    }

    private DealSkuVO buildDealSkuVOBySkuGroups(List<DealSkuGroupModuleVO> skuGroups, Config config, String dealTitle, String marketPrice, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //1.设置sku标题为团单标题
        dealSkuVO.setTitle(dealTitle);
        //2.设置sku价格为团单市场价
        String price = StringUtils.isEmpty(config.getMarketPriceFormat()) ? marketPrice : String.format(config.getMarketPriceFormat(), marketPrice);
        dealSkuVO.setPrice(price);
        //3.构造sku属性列表
        dealSkuVO.setItems(buildDealSkuAttrList(skuGroups, config, context));
        return dealSkuVO;
    }

    private List<DealSkuItemVO> buildDealSkuAttrList(List<DealSkuGroupModuleVO> skuGroups, Config config, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuGroups)) {
            return null;
        }
        return skuGroups.stream().map(skuGroup -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            //1.sku属性标题为原sku组标题（服务流程、X选择X项目等）
            dealSkuItemVO.setName(skuGroup.getTitle());
            //2.利用原suk列表构造sku属性值列表
            boolean isMustGroup = StringUtils.isEmpty(skuGroup.getTitle()) || skuGroup.getTitle().equals(config.getMustSkuGroupName());
            List<SkuAttrAttrItemVO> skuAttrs = buildSkuAttrList(skuGroup.getDealSkuList(), isMustGroup, config, context);
            dealSkuItemVO.setValueAttrs(skuAttrs);
            //3.设置展示样式
            dealSkuItemVO.setType(isMustGroup ? config.getMustSkuAttrType() : config.getOptionalSkuAttrType());
            return dealSkuItemVO;
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private List<SkuAttrAttrItemVO> buildSkuAttrList(List<DealSkuVO> dealSkuList, boolean isMustSkuGroup, Config config, ActivityCxt context) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        //按照sku category对sku列表进行分类
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = getSkuCategory2SkuListMap(dealSkuList);
        if (MapUtils.isEmpty(skuCategory2SkuListMap)) {
            return null;
        }

        List<SkuAttrAttrItemVO> skuAttrs = skuCategory2SkuListMap.entrySet().stream().map(entry -> {
            if (entry == null) {
                return null;
            }
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            //原sku分类名作为二级属性名
            skuAttrAttrItemVO.setName(entry.getKey());
            //利用原sku列表构造二级属性值
            List<CommonAttrVO> skuSecondAttrValueList = buildSkuSecondAttrValues(entry.getValue(), isMustSkuGroup, config.doGetSkuTitleCfgByCategory(entry.getKey()));
            skuAttrAttrItemVO.setValues(skuSecondAttrValueList);
            return skuAttrAttrItemVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 获取宠物服务项目中AI为 单个V生成详细内容
        CompletableFuture<Map<String, PetCPVWikiContentDTO>> singleValueWikiCF = CompletableFuture
                .supplyAsync(() -> getValueWiki(buildCPVSingleValueWikiQueryRequest(skuAttrs)))
                .exceptionally(e -> {
                    log.error("aiGenerateContentQueryService.batchQuery(CPVSingleValueWikiQueryRequest) error, {}",
                            e.getMessage());
                    return null;
                });
        // 获取宠物服务项目中AI为 PV生成的内容
        CompletableFuture<Map<String, PetCPVWikiContentDTO>> multiValueWikiCF = CompletableFuture
                .supplyAsync(() -> getValueWiki(buildCpvMultiValueWikiQueryRequest(skuAttrs)))
                .exceptionally(e -> {
                    log.error("aiGenerateContentQueryService.batchQuery(CPVMultiValueWikiQueryRequest) error, {}",
                            e.getMessage());
                    return null;
                });
        CompletableFuture.allOf(singleValueWikiCF, multiValueWikiCF).join();
        Map<String, PetCPVWikiContentDTO> singleValueWiki = singleValueWikiCF.join();
        Map<String, PetCPVWikiContentDTO> multiValueWiki = multiValueWikiCF.join();

        skuAttrs.forEach(skuAttrAttrItemVO -> {
            // 构造弹窗
            setPopup(context, config, skuAttrAttrItemVO.getName(), skuCategory2SkuListMap.get(skuAttrAttrItemVO.getName()),
                    skuAttrAttrItemVO, singleValueWiki, multiValueWiki);
        });
        return skuAttrs;
    }

    private Map<String, PetCPVWikiContentDTO> getValueWiki(ContentQueryBaseRequest request) {
        Map<String, PetCPVWikiContentDTO> valueWikiMap = new HashMap<>();
        if (request == null) {
            return valueWikiMap;
        }

        ContentQueryBaseResponse response = aiGenerateContentQueryService.batchQuery(request);
        if (response == null || !response.isSuccess()) {
            return valueWikiMap;
        }
        if (response instanceof CPVSingleValueWikiQueryResponse) {
            CPVSingleValueWikiQueryResponse singleValueWiki = (CPVSingleValueWikiQueryResponse)response;
            valueWikiMap = MapUtils.isEmpty(singleValueWiki.getValueWikiMap()) ? valueWikiMap
                    : singleValueWiki.getValueWikiMap();
        } else if (response instanceof CPVMultiValueWikiQueryResponse) {
            CPVMultiValueWikiQueryResponse multiValueWiki = (CPVMultiValueWikiQueryResponse)response;
            valueWikiMap = MapUtils.isEmpty(multiValueWiki.getValueWikiMap()) ? valueWikiMap
                    : multiValueWiki.getValueWikiMap();
        }
        return valueWikiMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null
                        && StringUtils.isNotBlank(entry.getValue().getTitle()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private CPVSingleValueWikiQueryRequest buildCPVSingleValueWikiQueryRequest(List<SkuAttrAttrItemVO> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs)) {
            return null;
        }
        Set<String> keys = skuAttrs.stream().map(this::getCommonAttrsVOS).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(Objects::nonNull).map(CommonAttrsVO::getName).filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        CPVSingleValueWikiQueryRequest request = new CPVSingleValueWikiQueryRequest();
        request.setKeys(keys);
        return request;
    }

    private CPVMultiValueWikiQueryRequest buildCpvMultiValueWikiQueryRequest(List<SkuAttrAttrItemVO> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs)) {
            return null;
        }
        Map<String, Set<String>> cpvMap = new HashMap<>();
        skuAttrs.forEach(skuAttrAttrItemVO -> {
            if (skuAttrAttrItemVO == null || StringUtils.isBlank(skuAttrAttrItemVO.getName())) {
                return;
            }
            cpvMap.put(skuAttrAttrItemVO.getName(), getCommonAttrsVOS(skuAttrAttrItemVO).stream().map(CommonAttrsVO::getName).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet()));
        });
        CPVMultiValueWikiQueryRequest request = new CPVMultiValueWikiQueryRequest();
        request.setCpvMap(cpvMap);
        return request;
    }

    private void setPopup(ActivityCxt context, Config config, String title, List<DealSkuVO> value, SkuAttrAttrItemVO skuAttrAttrItemVO,
                          Map<String, PetCPVWikiContentDTO> singleValueWiki, Map<String, PetCPVWikiContentDTO> multiValueWiki) {
        // 若没有AI生成的内容，则向 popup属性赋值，否则向 popUpWiki赋值
        if (MapUtils.isEmpty(singleValueWiki) && MapUtils.isEmpty(multiValueWiki)) {
            PopUpWindowVO popUpWindowVO = buildPopupWindowVO(config, title, value);
            skuAttrAttrItemVO.setPopup(popUpWindowVO);
        } else {
            // 若PV有值，则不用对每个V进行赋值
            if (multiValueWiki.containsKey(title)) {
                skuAttrAttrItemVO.setPopUpWiki(buildPopUpWikiVO(context, multiValueWiki.get(title), config));
            } else{// 否则对每个V进行赋值
                List<CommonAttrsVO> commonAttrsVOS = getCommonAttrsVOS(skuAttrAttrItemVO);
                if (CollectionUtils.isNotEmpty(commonAttrsVOS)) {
                    commonAttrsVOS.forEach(commonAttrsVO -> {
                        if (commonAttrsVO != null && StringUtils.isNotBlank(commonAttrsVO.getName()) && singleValueWiki.containsKey(commonAttrsVO.getName())) {
                            commonAttrsVO.setPopupWiki(buildPopUpWikiVO(context, singleValueWiki.get(commonAttrsVO.getName()), config));
                        }
                    });
                }
            }
        }
    }

    private List<CommonAttrsVO> getCommonAttrsVOS(SkuAttrAttrItemVO skuAttrAttrItemVO) {
        return Optional.ofNullable(skuAttrAttrItemVO)
                .map(SkuAttrAttrItemVO::getValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(values -> values.get(0))
                .map(CommonAttrVO::getCommonAttrs).orElse(new ArrayList<>());
    }

    private PopUpWikiVO buildPopUpWikiVO(ActivityCxt context, PetCPVWikiContentDTO petDto, Config config) {
        WikiContentVO content = new WikiContentVO();
        content.setTitle(petDto.getTitle());
        content.setBegin(petDto.getBegin());
        content.setEnd(petDto.getEnd());
        content.setJumpUrl(PlatformUtil.isMT(context.getParam(DealDetailConstants.Params.platform)) ? config.getAiPopUpMtJumpUrl() : config.getAiPopUpDpJumpUrl());
        // 转换 values 列表
        if (CollectionUtils.isNotEmpty(petDto.getValues())) {
            List<CPVValueVO> valueVOs = petDto.getValues().stream()
                    .map(petCPVValueDTO -> new CPVValueVO(petCPVValueDTO.getValue(), petCPVValueDTO.getTitle()))
                    .collect(Collectors.toList());
            content.setValues(valueVOs);
        }
        // 转换 questions 列表
        if (CollectionUtils.isNotEmpty(petDto.getQuestions())) {
            List<CPVQuestionVO> questionVOs = petDto.getQuestions().stream()
                    .map(petCPVQuestionDTO -> new CPVQuestionVO(petCPVQuestionDTO.getQuestion(), petCPVQuestionDTO.getAnswer()))
                    .collect(Collectors.toList());
            content.setQuestions(questionVOs);
        }
        PopUpWikiVO popUpWiki = new PopUpWikiVO();
        popUpWiki.setContent(content);
        popUpWiki.setIcon(config.getPopUpAIDefaultIcon());
        return popUpWiki;
    }

    private PopUpWindowVO buildPopupWindowVO(Config config, String title, List<DealSkuVO> value) {
        if (MapUtils.isEmpty(config.getPopUpTileSubTitleMap())){
            return null;
        }
        String subTitle = config.getPopUpTileSubTitleMap().get(title);
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        List<CommonAttrVO> popInfos = buildPopupInfos(value);
        if (CollectionUtils.isEmpty(popInfos)){
            return null;
        }
        popUpWindowVO.setInfos(popInfos);
        popUpWindowVO.setContent(subTitle);
        popUpWindowVO.setTitle(title);
        popUpWindowVO.setIcon(config.getPopUpDefaultIcon());
        return popUpWindowVO;
    }

    private List<CommonAttrVO> buildPopupInfos(List<DealSkuVO> skuVOS) {
        List<CommonAttrVO> details = Lists.newArrayList();
        for (DealSkuVO skuVO : skuVOS){
            if (skuVO == null || CollectionUtils.isEmpty(skuVO.getItems())){
                continue;
            }
            Optional<DealSkuItemVO> descOpt = skuVO.getItems().stream().filter(entity -> entity.getName().equals("projectDesc")).findAny();
            if (descOpt.isPresent()){
                CommonAttrVO commonAttrVO = new CommonAttrVO();
                commonAttrVO.setValue(descOpt.get().getValue());
                commonAttrVO.setName(skuVO.getTitle());
                details.add(commonAttrVO);
            }
        }
        return details;
    }

    private List<CommonAttrVO> buildSkuSecondAttrValues(List<DealSkuVO> dealSkuVOS, boolean isMustSkuGroup, SkuTitleCfg skuTitleCfg) {
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        //构造sku二级属性名，原sku组为必选组时展示"包含项目"，否则展示"X选X项目"
        String name = isMustSkuGroup ? CONTAINING_ITEMS : OPTIONAL_ITEMS;
        commonAttrVO.setName(name);
        //构造sku二级属性值
        List<CommonAttrsVO> commonAttrsVOS = dealSkuVOS.stream().map(sku -> {
            CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
            //sku三级属性名为sku名
            commonAttrsVO.setName(skuTitleCfg == null ? sku.getTitle() : skuTitleCfg.buildSkuTitleByCfg(sku));
            if (StringUtils.isNotEmpty(sku.getCopies())) {
                //sku三级属性值为sku份数；因为展示层的因素，需要手动插入一个 null 值，使得份数能放在最右侧的位置
                commonAttrsVO.setValues(Lists.newArrayList(null, sku.getCopies()));
            }
            return commonAttrsVO;
        }).collect(Collectors.toList());
        commonAttrVO.setCommonAttrs(commonAttrsVOS);
        return Lists.newArrayList(commonAttrVO);
    }

    private Map<String, List<DealSkuVO>> getSkuCategory2SkuListMap(List<DealSkuVO> dealSkuVOS) {
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = new LinkedHashMap<>();
        for (DealSkuVO dealSkuVO : dealSkuVOS) {
            String skuCategory = getSkuCategory(dealSkuVO);
            //按照sku类别进行分类
            if (!skuCategory2SkuListMap.containsKey(skuCategory) || skuCategory2SkuListMap.get(skuCategory) == null) {
                List<DealSkuVO> dealSkuVOList = new ArrayList<>();
                dealSkuVOList.add(dealSkuVO);
                skuCategory2SkuListMap.put(skuCategory, dealSkuVOList);
                continue;
            }
            skuCategory2SkuListMap.get(skuCategory).add(dealSkuVO);
        }
        return skuCategory2SkuListMap;
    }

    private String getSkuCategory(DealSkuVO dealSkuVO) {
        if (dealSkuVO == null || CollectionUtils.isEmpty(dealSkuVO.getItems())) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = dealSkuVO.getItems().stream().filter(item -> SKU_CATEGORY_ATTR_NAME.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private static String getSkuItemValueBySkuItemName(List<DealSkuItemVO> items, String name) {
        if (CollectionUtils.isEmpty(items) || StringUtils.isEmpty(name)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = items.stream().filter(item -> name.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private boolean isShowAIPopUp(ActivityCxt context, Config config) {
        List<Integer> mtCityIds = Optional.ofNullable(config.getMtCityIds()).orElse(new ArrayList<>());
        Integer mtCityId = context.getParam(DealDetailConstants.Params.mtCityId);
        return mtCityIds.contains(mtCityId) && isHitDouhuExp(context, config);
    }

    private boolean isHitDouhuExp(ActivityCxt context, Config config) {
        List<DouhuResultModel> douhuResultModels = context.getSource(DealDetailDouhuFetcher.CODE);
        List<String> douhuExpIds = config.getDouhuExpIds();
        if () {
            return false;
        }
    }

    @Data
    @VPointCfg
    public static class Config {
        //必选sku组名，用来判断当前sku组是否是必选sku组
        private String mustSkuGroupName;
        //必选sku前端样式
        private int mustSkuAttrType;
        //可选sku前端样式
        private int optionalSkuAttrType;
        //市场价format
        private String marketPriceFormat;
        //skuTitle 的二次拼接规则
        private Map<String, SkuTitleCfg> skuCategory2TitleCfg;
        /**
         * 弹窗文案副标题映射
         */
        private Map<String,String> popUpTileSubTitleMap;
        /**
         * 弹窗icon
         */
        private String popUpDefaultIcon = "https://p0.meituan.net/ingee/d97872ac6f4f0a1396eede65dee27e9e1265.png";
        /**
         * AI弹窗的icon
         */
        private String popUpAIDefaultIcon = "https://p0.meituan.net/ingee/d97872ac6f4f0a1396eede65dee27e9e1265.png";
        /**
         * 美团AI弹窗的跳链
         */
        private String aiPopUpMtJumpUrl;
        /**
         * 点评AI弹窗的跳链
         */
        private String aiPopUpDpJumpUrl;
        /**
         * AI弹窗放量的城市
         */
        private List<Integer> mtCityIds;
        /**
         * AI弹窗的美团实验号
         */
        private String mtDouhuExpId;
        /**
         * AI弹窗的点评实验号
         */
        private String mtDouhuExpId;

        public SkuTitleCfg doGetSkuTitleCfgByCategory(String skuCategory) {
            if (MapUtils.isEmpty(this.skuCategory2TitleCfg)) {
                return null;
            }
            return this.skuCategory2TitleCfg.get(skuCategory);
        }
    }

    @Data
    private static class SkuTitleCfg {
        /**
         * 拼接格式（固定 title 是第一个参数，第二个参数是 attrValue）
         */
        private String format;

        /**
         * 参与拼接的属性key
         */
        private List<String> attrKeys;

        /**
         * 连词符
         */
        private String separator;

        public String buildSkuTitleByCfg(DealSkuVO dealSkuVO) {
            String defTitle = dealSkuVO.getTitle();
            if (StringUtils.isEmpty(this.format) || CollectionUtils.isEmpty(this.attrKeys)) {
                return defTitle;
            }
            List<String> values = this.attrKeys.stream()
                    .map(key -> getSkuItemValueBySkuItemName(dealSkuVO.getItems(), key))
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(values)) {
                return defTitle;
            }
            //拼接标题和属性
            return String.format(this.format, defTitle, StringUtils.join(values, this.separator));
        }
    }
}
