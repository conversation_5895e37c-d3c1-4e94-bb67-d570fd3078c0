package com.sankuai.dzviewscene.product.shelf.ability.builder.floors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.appkit.utils.VersionUtil;
import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfview.ShelfResponseAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.*;
import com.sankuai.dzviewscene.product.shelf.ability.builder.shelfshowtype.ShelfShowTypeVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.CommonProductTagUtils;
import com.sankuai.dzviewscene.product.shelf.utils.OldEngineAdaptUtil;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.framework.monitor.FloorItemsMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.ShelfMultiRankService;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.FloatTagGroup;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Ability(code = FloorsBuilder.CODE,
        name = "VO-楼层构造能力",
        description = "楼层构造能力。构造 List<FilterBtnIdAndProAreasVO>",
        activities = {DealShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE, CardFetcher.CODE, ProductActivitiesFetcher.CODE}
)
public class FloorsBuilder extends PmfAbility<List<FilterBtnIdAndProAreasVO>, FloorsBuilder.Request, FloorsBuilder.Config> {

    public static final String CODE = "FloorsBuilder";

    private static final String DEFAULT_EXT_CODE = "com.sankuai.dzviewscene.shelf.business.shelf.defaultshelf.builder.DzDefaultDealFloorsBuilderExt";
    public static final String PRICECIPHER_PARAM = "pricecipher=";

    @Resource
    private ComponentFinder componentFinder;

    @Resource
    private PmfExecutionHelper pmfExecutionHelper;

    @Resource
    private ShelfMultiRankService shelfMultiRankService;

    @Resource
    private FloorItemsMonitor floorItemsMonitor;

    @Override
    public CompletableFuture<List<FilterBtnIdAndProAreasVO>> build(ActivityCxt ctx, Request request, Config config) {
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> oldEngineRes = getOldEngineRes(ctx, config);
        if (oldEngineRes != null) {
            return oldEngineRes;
        }
        return getNewEngineRes(ctx, request, config);
    }

    /**
     * 获取旧框架的构造结果，若不走旧框架则返回 null
     *
     * @param ctx
     * @param config
     * @return
     */
    private CompletableFuture<List<FilterBtnIdAndProAreasVO>> getOldEngineRes(ActivityCxt ctx, Config config) {
        if (config.getOldEngineCfg() == null) {
            return null;
        }
        OldEngineAdaptUtil.paddingAbilityToCtx(ctx, com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder.ITEM_AREA_ABILITY_CODE, config.getOldEngineCfg());
        OldEngineAdaptUtil.paddingExtPointToCtx(ctx, FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, config.getOldEngineCfg());

        ActivityContext oldContext = ActivityCtxtUtils.toActivityContext(ctx);
        return componentFinder.findAbility(oldContext, com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder.ITEM_AREA_ABILITY_CODE).build(oldContext);
    }

    private CompletableFuture<List<FilterBtnIdAndProAreasVO>> getNewEngineRes(ActivityCxt ctx, Request request, Config config) {
        // 这里强制使用默认的，后续的重构实现都需满足默认逻辑，全量优化后，这里的旧引擎逻辑就可以删掉了
        OldEngineAdaptUtil.paddingExtPointToCtx(ctx, FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, DEFAULT_EXT_CODE);
        // 1. 查询主数据对象
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null || ModelUtils.hasNoProducts(shelfGroupM)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        // 2. 获取商品组
        if (CollectionUtils.isEmpty(request.getGroupNames())) {
            throw new BusinessException(String.format("构造货架楼层区, groupNames参数没传"));
        }
        // 3. 根据商品组列表顺序构造FilterBtnIdAndProAreasVO
        return CompletableFuture.completedFuture(buildFilterBtnIdAndProAreasVOList(ctx, request.getGroupNames(), shelfGroupM, request, config));
    }


    private List<FilterBtnIdAndProAreasVO> buildFilterBtnIdAndProAreasVOList(ActivityCxt ctx, List<String> productGroupNames, ShelfGroupM shelfGroupM, Request request, Config config) {
        if (ModelUtils.hasNoProducts(shelfGroupM)) {
            return Lists.newArrayList();
        }
        Map<String, FilterM> filters = shelfGroupM.getFilterMs() == null ? new HashMap<>() : shelfGroupM.getFilterMs();
        Map<String, ProductGroupM> productGroups = shelfGroupM.getProductGroupMs() == null ? new HashMap<>() : shelfGroupM.getProductGroupMs();
        //一组筛选 + 多组商品（当前实现为单筛选多组商品，无多筛选的诉求）
        if (isMultiItemAreaShelf(productGroupNames)) {
            return Lists.newArrayList(buildSingleFilterMultiItemArea(ctx, productGroupNames, productGroups, filters, request, config));
        }
        //一组筛选 + 一组商品
        // 先只考虑单组商品
        String groupName = productGroupNames.get(0);
        return buildSingleFloorShelfArea(ctx, groupName, filters.get(groupName), productGroups.get(groupName), request, config);
    }

    ///////////////////////////////////////////////以下是构造多个货架楼层区逻辑///////////////////////////////////////////////

    private boolean isMultiItemAreaShelf(List<String> productGroupNames) {
        return productGroupNames.size() > 1;
    }

    private FilterBtnIdAndProAreasVO buildSingleFilterMultiItemArea(ActivityCxt ctx, List<String> productGroupNames, Map<String, ProductGroupM> productGroups, Map<String, FilterM> filters, Request request, Config config) {
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(ctx);
        List<ProductAreaComponentVO> productAreaList = new ArrayList<>(productGroupNames.size());
        //取第一个有值的 FilterM
        FilterM filterM = MapUtils.isNotEmpty(filters) ? filters.values().stream().filter(Objects::nonNull).findFirst().orElse(null) : null;
        for (String groupName : productGroupNames) {
            //可解释性排序
            Map<Long, List<ProductM>> filterId2Products = new HashMap<>(1);
            List<ProductM> productLists = getNotNullProductLists(productGroups.get(groupName));
            filterId2Products.put(request.getSelectedFilterId(), productLists);
            // 对预填充团单进行到手价的赋值
            batchSetPreProductSalePrice(ctx, productGroups.get(groupName));
            // 对当前tab下的商品进行到手价的赋值
            PriceDisplayUtils.batchSetSalePrice(ctx, productLists);
            //构造商品区
            ProductAreaComponentVO productArea = buildProductAreaComponentVO(activityContext, ctx, groupName, filterId2Products.get(request.getSelectedFilterId()), request.getSelectedFilterId(), request, config, filterM, productGroups.get(groupName));
            if (productArea != null) {
                productAreaList.add(productArea);
            }
        }
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        filterBtnIdAndProAreasVO.setFilterBtnId(request.getSelectedFilterId());
        filterBtnIdAndProAreasVO.setSceneCode(ctx.getSceneCode());
        filterBtnIdAndProAreasVO.setProductAreas(productAreaList);
        filterBtnIdAndProAreasVO.setProductAreaTips(getProductAreaTips(ctx,  request.getSelectedFilterId(), request));
        filterBtnIdAndProAreasVO.setAttrs(getFilterItemAreaAttrs(ctx, filterM, request.getSelectedFilterId(), getCurrentProductMs(productGroupNames, productGroups)));
        //其他项先不支持
        logForPriceCipher(ctx, productAreaList);
        return filterBtnIdAndProAreasVO;
    }

    private void logForPriceCipher(ActivityCxt ctx, List<ProductAreaComponentVO> productAreaList) {
        if (CollectionUtils.isEmpty(productAreaList)) {
            return;
        }
        try {
            Map<String, String> logInfo = getPriceCipherLogMap(ctx, productAreaList);
            Cat.logMetricForCount("dealShelfPriceCipher", logInfo);
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    @NotNull
    private Map<String, String> getPriceCipherLogMap(ActivityCxt ctx, List<ProductAreaComponentVO> productAreaList) {
        Map<String, String> logInfo = new HashMap<>();
        // 商品没有返回跳转链接
        boolean anyOneNoJumpUrl = false;
        // 跳转链接没有价格一致率监控字段（包含没有跳转链接的情况）
        boolean anyOneNoPriceCipher = false;
        for (ProductAreaComponentVO productAreaComponentVO : productAreaList) {
            if (isAreaNoProductItem(productAreaComponentVO)) {
                continue;
            }
            for (DzItemVO item : productAreaComponentVO.getItemArea().getProductItems()) {
                if (item == null) {
                    continue;
                }
                anyOneNoJumpUrl = anyOneNoJumpUrl || StringUtils.isEmpty(item.getJumpUrl());
                anyOneNoPriceCipher = anyOneNoPriceCipher || !StringUtils.contains(item.getJumpUrl(), PRICECIPHER_PARAM);
            }
        }
        logInfo.put("anyOneNoJumpUrl", String.valueOf(anyOneNoJumpUrl));
        logInfo.put("anyOneNoPriceCipher", String.valueOf(anyOneNoPriceCipher));
        logInfo.put("sceneCode", ctx.getSceneCode());
        return logInfo;
    }

    private boolean isAreaNoProductItem(ProductAreaComponentVO productAreaComponentVO) {
        return productAreaComponentVO == null || productAreaComponentVO.getItemArea() == null
                || CollectionUtils.isEmpty(productAreaComponentVO.getItemArea().getProductItems());
    }

    private void batchSetPreProductSalePrice(ActivityCxt ctx, ProductGroupM productGroupM) {
        if (productGroupM == null || MapUtils.isEmpty(productGroupM.getPreLoadProducts())) {
            return;
        }
        PriceDisplayUtils.batchSetSalePrice(ctx, productGroupM.getPreLoadProducts().get(ProductTypeEnum.DEAL.getType()));
    }

    private Map<Integer, ProductM> getAllPreProducts(ProductGroupM productGroupM) {
        if (productGroupM == null || MapUtils.isEmpty(productGroupM.getPreLoadProducts())) {
            return Maps.newHashMap();
        }
        List<ProductM> products = productGroupM.getPreLoadProducts().get(ProductTypeEnum.DEAL.getType());
        if (CollectionUtils.isEmpty(products)){
            return Maps.newHashMap();
        }
        return products.stream().collect(Collectors.toMap(ProductM::getProductId, Function.identity(), (v1, v2) -> v1));
    }

    public ProductAreaTipsComponentVO getProductAreaTips(ActivityCxt activityCxt, long filterId, Request request) {
        try {
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            ProductAreaTipsVP<?> productAreaTipsVP = findVPoint(activityCxt, ProductAreaTipsVP.CODE);
            return productAreaTipsVP.execute(activityCxt,
                    ProductAreaTipsVP.Param.builder()
                            .platform(request.getPlatform())
                            .filterId(filterId)
                            .douHuList(douHuMList)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<ProductM> getCurrentProductMs(List<String> productGroupNames, Map<String, ProductGroupM> productGroups) {
        List<ProductM> currentProductMs = new ArrayList<>();
        if (CollectionUtils.isEmpty(productGroupNames) || MapUtils.isEmpty(productGroups)) {
            return currentProductMs;
        }
        for (String groupName : productGroupNames) {
            currentProductMs.addAll(getNotNullProductLists(productGroups.get(groupName)));
        }
        return currentProductMs;
    }

    ///////////////////////////////////////////////以下是构造单个货架楼层区逻辑///////////////////////////////////////////////

    // 注: 单层货架模式, 有处理分组逻辑, 每组一个FilterBtnIdAndProAreasVO对象, 每个FilterBtnIdAndProAreasVO一个ProductAreaComponentVO对象
    private List<FilterBtnIdAndProAreasVO> buildSingleFloorShelfArea(ActivityCxt ctx, String groupName, FilterM filterM, ProductGroupM currentFloorM, Request request, Config config) {
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(ctx);
        // 1. 使用扩展点分组
        Map<Long, List<ProductM>> filterId2Products = groupByFilter(activityContext, groupName, filterM, currentFloorM, config);
        // 2. 无分组逻辑处理
        if (MapUtils.isEmpty(filterId2Products)) {
            filterId2Products = new HashMap<>();
            filterId2Products.put(request.getSelectedFilterId(), getNotNullProductLists(currentFloorM));
        }
        // 对预填充团单进行到手价的赋值
        batchSetPreProductSalePrice(ctx, currentFloorM);
        // 对当前tab下的商品进行到手价的赋值
        PriceDisplayUtils.batchSetSalePrice(ctx, currentFloorM.getProducts());
        // 3. 构造货架商品区
        return filterId2Products.entrySet()
                .stream()
                .filter(this::filterProductEntry)
                .map(productsEntry -> buildFilterBtnIdAndProAreasVO(activityContext, ctx, groupName, filterM, productsEntry.getKey(), productsEntry.getValue(), request, config, currentFloorM))
                .collect(Collectors.toList());
    }

    //默认筛选商品区为空的筛选
    private boolean filterProductEntry(Map.Entry<Long, List<ProductM>> productEntry) {
        return CollectionUtils.isNotEmpty(productEntry.getValue());
    }

    /**
     * 返回非Null的结果
     *
     * @param currentFloorM
     * @return
     */
    private List<ProductM> getNotNullProductLists(ProductGroupM currentFloorM) {
        return currentFloorM == null || CollectionUtils.isEmpty(currentFloorM.getProducts()) ? new ArrayList<>() : currentFloorM.getProducts();
    }

    private FilterBtnIdAndProAreasVO buildFilterBtnIdAndProAreasVO(ActivityContext activityContext, ActivityCxt ctx, String groupName, FilterM filterM, long filterId, List<ProductM> currentProductMs, Request request, Config config, ProductGroupM currentFloorM) {
        ProductAreaComponentVO productAreaComponentVO = buildProductAreaComponentVO(activityContext, ctx, groupName, currentProductMs, filterId, request, config, filterM, currentFloorM);
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        filterBtnIdAndProAreasVO.setFilterBtnId(filterId);
        filterBtnIdAndProAreasVO.setProductAreas(productAreaComponentVO == null ? Lists.newArrayList() : Lists.newArrayList(productAreaComponentVO));
        filterBtnIdAndProAreasVO.setProductAreaTips(getProductAreaTips(ctx,  filterId, request));
        filterBtnIdAndProAreasVO.setSceneCode(ctx.getSceneCode());
        filterBtnIdAndProAreasVO.setAttrs(getFilterItemAreaAttrs(ctx, filterM, filterId, currentProductMs));
        return filterBtnIdAndProAreasVO;
    }

    /**
     * @param activityCxt
     * @param filterM
     * @param currentFilterId
     * @param currentProducts
     * @return 构造扩展属性
     */
    private List<AttrVO> getFilterItemAreaAttrs(ActivityCxt activityCxt, FilterM filterM, long currentFilterId, List<ProductM> currentProducts) {
        try {
            FilterItemAreaAttrsVP<?> filterItemAreaAttrsVP = findVPoint(activityCxt, FilterItemAreaAttrsVP.CODE);
            return filterItemAreaAttrsVP.execute(activityCxt, FilterItemAreaAttrsVP.Param.builder()
                    .filterM(filterM).currentFilterId(currentFilterId).products(currentProducts).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    ///////////////////////////////////////////////以下是构造货架楼层区逻辑///////////////////////////////////////////////

    private ProductAreaComponentVO buildProductAreaComponentVO(ActivityContext activityContext, ActivityCxt activityCxt, String groupName, List<ProductM> currentFloorProductMs, long filterId, Request request, Config config, FilterM filterM, ProductGroupM currentFloorM) {
        if (CollectionUtils.isEmpty(currentFloorProductMs)) {
            return null;
        }
        ProductAreaComponentVO productAreaComponentVO = new ProductAreaComponentVO();
        // 1. 构造商品区
        productAreaComponentVO.setItemArea(buildDzItemAreaComponentVO(activityContext, activityCxt, groupName, currentFloorProductMs, filterId, request, config, currentFloorM,filterM));
        // 2. 构造标题区
        productAreaComponentVO.setTitle(buildItemAreaTitleComponentVO(activityCxt, groupName, filterId, filterM, currentFloorProductMs));
        // 3. 构造更多模块
        productAreaComponentVO.setMore(buildMoreComponent(activityCxt, productAreaComponentVO.getItemArea(), request, filterId, groupName));
        // 4. 根据defaultNum截断
        limitByDefaultShowNumIfHasJumpUrl(productAreaComponentVO);
        // 5. 构造商品区提示文案
        productAreaComponentVO.setItemAreaTips(buildItemAreaTips(activityCxt, request, filterId, currentFloorProductMs, filterM));
        // 6. 构造商品区展示样式
        productAreaComponentVO.setShowType(buildItemAreaShowType(activityCxt, groupName));
        return productAreaComponentVO;
    }

    /**
     * @param activityCxt
     * @return 构造商品区的标题组件
     */
    private TitleComponentVO buildItemAreaTitleComponentVO(ActivityCxt activityCxt, String groupName, long filterId, FilterM filterM, List<ProductM> currentFloorProductMs) {
        try {
            ItemAreaTitleVP<?> itemAreaTitleVP = findVPoint(activityCxt, ItemAreaTitleVP.CODE);
            return itemAreaTitleVP.execute(activityCxt, ItemAreaTitleVP.Param.builder()
                    .groupName(groupName)
                    .filterId(filterId)
                    .filterM(filterM)
                    .currentFloorProductMs(currentFloorProductMs)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private void limitByDefaultShowNumIfHasJumpUrl(ProductAreaComponentVO productAreaComponentVO) {
        if (componentIsBlank(productAreaComponentVO)) {
            return;
        }
        int defaultShowNum = productAreaComponentVO.getItemArea().getDefaultShowNum();
        if (productAreaComponentVO.getItemArea().getProductItems().size() <= defaultShowNum) {
            return;
        }
        productAreaComponentVO.getItemArea().setProductItems(productAreaComponentVO.getItemArea().getProductItems().subList(0, defaultShowNum));
    }

    private boolean componentIsBlank(ProductAreaComponentVO productAreaComponentVO) {
        return productAreaComponentVO == null
                || productAreaComponentVO.getItemArea() == null
                || CollectionUtils.isEmpty(productAreaComponentVO.getItemArea().getProductItems())
                || productAreaComponentVO.getMore() == null
                || StringUtils.isEmpty(productAreaComponentVO.getMore().getJumpUrl());
    }

    private DzItemAreaComponentVO buildDzItemAreaComponentVO(ActivityContext activityContext, ActivityCxt activityCxt, String groupName, List<ProductM> currentFloorProductMs, long filterId, Request request, Config config, ProductGroupM currentFloorM, FilterM filterM) {
        DzItemAreaComponentVO dzItemAreaComponentVO = new DzItemAreaComponentVO();
        // 1. 放入商品
        dzItemAreaComponentVO.setProductItems(buildDzItemVOsFromItemMs(activityContext, activityCxt, groupName, currentFloorProductMs, filterId, request, config, getAllPreProducts(currentFloorM),filterM));

        // 2.展示样式
        dzItemAreaComponentVO.setShowType(getConfigItemAreaShowType(activityCxt, config));

        // 3. 设置默认展示数
        dzItemAreaComponentVO.setDefaultShowNum(getDefaultShowNum(activityCxt, currentFloorProductMs, activityCxt.getSource(ShelfDouHuFetcher.CODE), config, filterId, groupName));

        // 4. 设置hasNext
        dzItemAreaComponentVO.setHasNext(buildHasNext(activityCxt, currentFloorM));

        return dzItemAreaComponentVO;
    }

    public boolean buildHasNext(ActivityCxt activityCxt, ProductGroupM currentFloorM) {
        boolean defaultHasNext = false;
        try {
            FloorHasNextVP<?> floorHasNextVP = findVPoint(activityCxt, FloorHasNextVP.CODE);
            if (floorHasNextVP == null) {
                return defaultHasNext;
            }
            Boolean hasNext = floorHasNextVP.execute(activityCxt, FloorHasNextVP.Param.builder()
                .productGroup(currentFloorM)
                .build());
            return hasNext != null && hasNext;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return defaultHasNext;
        }
    }

    private int getConfigItemAreaShowType(ActivityCxt activityCxt, Config config) {
        try {
            List<DouHuM> douHuList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            if (Objects.isNull(config) || MapUtils.isEmpty(config.getDouHuSkItemAreaShowType())) {
                return 0;//默认-原逻辑
            }

            Optional<Integer> itemConfigRes = (Optional<Integer>) DouHuUtils.getConfigByDouHu(douHuList, config.douHuSkItemAreaShowType);
            return itemConfigRes.isPresent() ? itemConfigRes.get() : 0;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return 0;
        }
    }

    private int getDefaultShowNum(ActivityCxt activityCxt, List<ProductM> currentFloorProductMs, List<DouHuM> douHuList, Config config, long filterId, String groupName) {
        try {
            ItemAreaDefaultShowNumVP<?> itemAreaDefaultShowNumVP = findVPoint(activityCxt, ItemAreaDefaultShowNumVP.CODE);
            if (itemAreaDefaultShowNumVP == null) {
                //等历史包袱迁移完，这块逻辑可以下线
                return config.doGetFloorDefaultShowNum(douHuList);
            }
            Integer showNum = itemAreaDefaultShowNumVP.execute(activityCxt,
                    ItemAreaDefaultShowNumVP.Param.builder()
                            .products(currentFloorProductMs)
                            .douHuList(douHuList)
                            .filterId(filterId)
                            .groupName(groupName)
                            .build());
            if (showNum == null) {
                //等历史包袱迁移完，这块逻辑可以下线
                return config.doGetFloorDefaultShowNum(douHuList);
            }
            return showNum;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return 3;
        }
    }

    private List<DzItemVO> buildDzItemVOsFromItemMs(ActivityContext activityContext, ActivityCxt activityCxt, String groupName, List<ProductM> productMs, long filterId, Request sourceRequest, Config config, Map<Integer, ProductM> allPreProducts, FilterM filterM) {
        if (CollectionUtils.isEmpty(productMs)) {
            return Lists.newArrayList();
        }
        AtomicInteger index = new AtomicInteger(0);
        return productMs.stream()
                .filter(Objects::nonNull)
                .map(productM -> buildDzItemVO(activityContext, activityCxt, groupName, productM, index.getAndIncrement(), filterId, sourceRequest, config, productMs.size(), allPreProducts,filterM))
                .collect(Collectors.toList());
    }


    private DzItemVO buildDzItemVO(ActivityContext activityContext, ActivityCxt activityCxt, String groupName, ProductM productM, int index, long filterId, Request sourceRequest, Config config, int productTotalNum, Map<Integer, ProductM> allPreProducts, FilterM filterM) {
        // 1. 基本填充字段, 这里是标准化字段, 无需定义
        DzItemVO dzItemVO = new DzItemVO();
        try {
            CardM cardM = activityCxt.getSource(CardFetcher.CODE);
            //计算最优promoPrice
            productM.setBestPromoPrice(PriceUtils.getUserHasPromoPrice(productM, cardM));
            String salePrice = itemComponentSalePrice(activityCxt, productM, cardM, groupName);
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            String timesDealSk = getTimesDealSk(douHuMList, config, sourceRequest.getPlatform());
            dzItemVO.setItemId(productM.getProductId());
            dzItemVO.setItemIdL(productM.getProductId());
            dzItemVO.setBasePrice(getBasePrice(productM.getBasePrice()));
            dzItemVO.setActivityRemainSeconds(activityRemainSeconds(activityCxt, productM));
            dzItemVO.setActivityStartTime(activityStartTime(activityCxt, productM));
            // 2. 扩展填充字段, 这里是可以个性化定制样式的字段
            dzItemVO.setSalePrice(salePrice);
            dzItemVO.setMarketPrice(itemComponentMarketPrice(activityCxt, productM, douHuMList, timesDealSk));
            dzItemVO.setMarketPriceDesc(itemComponentMarketPriceDesc(activityCxt, productM, douHuMList));
            dzItemVO.setSale(itemComponentSale(activityCxt, productM, groupName));
            //商品普通标题构造
            dzItemVO.setTitle(itemComponentTitle(activityCxt, productM, filterId, douHuMList,sourceRequest));
            //商品富文本标题构造（WARN：依赖普通标题的构造结果，顺序不能乱）
            dzItemVO.setRichLabelsTitle(itemComponentRichLabelsTitle(activityCxt, sourceRequest.getPlatform(), dzItemVO.getTitle(), productM.getTitle()));
            dzItemVO.setJumpUrl(itemJumpUrl(activityCxt, productM, sourceRequest));
            dzItemVO.setAvailable(itemAvailable(activityCxt, productM));
            dzItemVO.setSalePriceDesc(itemComponentSalePriceDesc(activityCxt, productM, timesDealSk,filterId,filterM));
            dzItemVO.setPurchase(itemComponentPurchase(activityCxt, productM));
            dzItemVO.setRichSale(itemComponentRichSale(activityCxt, productM));
            dzItemVO.setBottomTags(itemComponentBottomTags(activityCxt, productM, douHuMList, salePrice));
            //Warn 依赖 dzItemVO.setJumpUrl
            dzItemVO.setBtn(itemComponentBtn(activityCxt, sourceRequest, dzItemVO.getJumpUrl(), productM, douHuMList, salePrice, groupName));
            dzItemVO.setPic(itemComponentPic(activityCxt, productM, sourceRequest, douHuMList, productTotalNum, filterId, index));
            dzItemVO.setPreTitleTag(preTitleTag(activityCxt, productM, douHuMList, filterId, groupName));
            //次卡售卖价会有次数文案拼接，这里取basePrice即可
            if (productM.getProductType() == ProductTypeEnum.TIME_CARD.getType()) {
                salePrice = Objects.nonNull(productM.getBasePrice()) ? productM.getBasePrice().stripTrailingZeros().toPlainString() : StringUtils.EMPTY;
            }
            // 调整vipPrice的顺序，PriceBottomTags的获取需要加判断
            dzItemVO.setVipPrice(itemComponentVipPrice(activityCxt, productM, cardM, timesDealSk));
            List<DzTagVO> dzTagVOS = itemComponentPriceBottomTags(activityCxt, productM, cardM, salePrice, sourceRequest.getPlatform(), douHuMList, groupName);
            dzItemVO.setPriceBottomTags(dzTagVOS);
            //Warn 依赖 dzItemVO.setPriceBottomTags
            dzItemVO.setPriceAboveTags(itemComponentPriceAboveTags(activityCxt, productM, douHuMList, dzTagVOS, salePrice));
            dzItemVO.setPromo(itemComponentPromo(activityCxt, sourceRequest, productM, cardM, salePrice, douHuMList, productTotalNum, timesDealSk, allPreProducts, groupName));
            dzItemVO.setProductTags(itemComponentProductTags(activityCxt, productM, douHuMList, filterId));
            dzItemVO.setProductRichTags(itemProductRichTags(activityCxt, productM, dzItemVO.getProductTags(), douHuMList));
            dzItemVO.setActivityRemainSecondsLabel(activityRemainSecondsLabel(productM));
            dzItemVO.setProgressBar(progressBar(activityCxt, productM));
            dzItemVO.setExtra(getExtra(activityCxt, productM,douHuMList));
            dzItemVO.setActivityEndTime(getActivityEndTime(productM, activityCxt, douHuMList));
            dzItemVO.setShowType(itemShowType(activityCxt, config, sourceRequest, douHuMList, productM, dzItemVO));
            dzItemVO.setWarmUp(itemWarmUp(activityCxt, productM));
            dzItemVO.setPriceTips(itemPriceTips(activityCxt, productM));
            dzItemVO.setSalePricePrefixDesc(itemSalePricePrefixDesc(activityCxt, productM, timesDealSk));
            dzItemVO.setSinglePrice(itemSinglePrice(activityCxt, productM, salePrice, timesDealSk));
            dzItemVO.setVsComponent(itemVsComponent(activityCxt, productM, timesDealSk, allPreProducts));

            //按钮上方|下方轮播信息
            dzItemVO.setButtonCarouselMsg(buildButtonCarouselMsg(activityCxt, productM));
            //追加下全行业共用副标题
            CommonProductTagUtils.appendCommonProductTag(dzItemVO, productM, activityCxt);
            //////////////////////////////字段冲突处理/////////////////////////////////////////////
            clearProductTagsIfNeeded(dzItemVO);
            finalHandleBtn(activityCxt, dzItemVO, productM);
            fixShowConflict(activityCxt, dzItemVO, productM, productTotalNum, sourceRequest, timesDealSk);
            //透穿团详头图-前端预加载使用
            dzItemVO.setDefaultPicPath(itemComponentDefaultPicPath(productM));
            //labs 放到最后处理，因为会依赖上面处理完的信息
            dzItemVO.setLabs(itemComponentLabs(activityCxt, dzItemVO, index, sourceRequest, cardM, productM, douHuMList, filterId));
            //水位监控
            floorItemsMonitor.doMonitor(activityCxt, dzItemVO, productM);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
        }
        return dzItemVO;
    }

    public String itemComponentDefaultPicPath(ProductM productM) {
        return ProductMAttrUtils.getAttrValue(productM, ProductMAttrUtils.DEAL_DEFAULT_PIC_PATH);
    }

    private String itemSalePricePrefixDesc(ActivityCxt activityCxt, ProductM productM, String timesDealSk) {
        try {
            ItemSalePricePrefixVP<?> extraVP = findVPoint(activityCxt, ItemSalePricePrefixVP.CODE);
            if (extraVP == null) {
                return null;
            }
            return extraVP.execute(activityCxt, ItemSalePricePrefixVP.Param.builder()
                    .productM(productM)
                    .timesDealSk(timesDealSk)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private DzVsComponentVO itemVsComponent(ActivityCxt activityCxt, ProductM productM, String timesDealSk, Map<Integer, ProductM> allPreProducts) {
        try {
            ItemVsComponentVP<?> extraVP = findVPoint(activityCxt, ItemVsComponentVP.CODE);
            if (extraVP == null) {
                return null;
            }
            return extraVP.execute(activityCxt, ItemVsComponentVP.Param.builder()
                    .productM(productM)
                    .timesDealSk(timesDealSk)
                    .allProducts(allPreProducts)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private String itemSinglePrice(ActivityCxt activityCxt, ProductM productM, String salePrice, String timesDealSk) {
        try {
            ItemSinglePriceVP<?> extraVP = findVPoint(activityCxt, ItemSinglePriceVP.CODE);
            if (extraVP == null) {
                return null;
            }
            return extraVP.execute(activityCxt, ItemSinglePriceVP.Param.builder()
                    .productM(productM)
                    .salePrice(salePrice)
                    .timesDealSk(timesDealSk)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 是否是酒吧双列货架，并且是多次卡
     */
    private boolean isBarDoubleMultiShelf(ActivityCxt activityCxt, ProductM productM, int productTotalNum) {
        if (!productM.isTimesDeal()) {
            return false;
        }

        try {
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreCase(false);
            copyOptions.setIgnoreError(true);

            ShelfResponseAssembler.Request param = BeanUtil.mapToBean(activityCxt.getParameters(), ShelfResponseAssembler.Request.class, false, copyOptions);
            ShelfShowTypeVP<?> shelfShowTypeVP = pmfExecutionHelper.findVPoint(activityCxt, ShelfResponseAssembler.CODE, ShelfShowTypeVP.CODE);
            if (Objects.isNull(shelfShowTypeVP) || Objects.isNull(shelfShowTypeVP.getVPointOptionCode())) {
                return false;
            }
            Integer showType = shelfShowTypeVP.execute(activityCxt,
                    ShelfShowTypeVP.Param.builder()
                            .productTotalNum(productTotalNum)
                            .douHuList(activityCxt.getSource(ShelfDouHuFetcher.CODE))
                            .shelfVersion(param.getShelfVersion())
                            .build());
            return showType != null && showType == 10105;
        } catch (Exception ex) {
            Cat.logErrorWithCategory(String.format("FloorsBuilder#isBarDoubleMultiShelf.productId=%s", productM.getProductId()), ex);
            return false;
        }
    }

    private int getDefaultShowType(ShelfResponseAssembler.Request request, ShelfResponseAssembler.Config config, List<DouHuM> douHuMList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder.getDefaultShowType(ShelfResponseAssembler$Request,ShelfResponseAssembler$Config,List)");
        //若表达式有值，则优先从表达式取，取不到值还是走默认的配置
        if (StringUtils.isNotEmpty(config.getCalcShowTypeExp())) {
            try {
                Map<String, Object> env = (JSONObject) JSON.toJSON(request);
                env.put("douHuMSkList", DouHuUtils.getDouHuMSkList(douHuMList));
                Expression expression = AviatorEvaluator.getInstance().compile(config.getCalcShowTypeExp(), true);
                Object result = expression.execute(env);
                int value = NumberUtils.objToInt(result);
                if (value > 0) {
                    return value;
                }
            } catch (Exception ex) {
                Cat.logErrorWithCategory(String.format("ShelfResponseAssembler#calcShowType.%s", request.toString()), ex);
            }
        }
        //通过命中的斗斛实验策略返回showType
        if (MapUtils.isNotEmpty(config.getDouHuSk2showType())) {
            List<String> douHuMSkList = DouHuUtils.getDouHuMSkList(douHuMList);
            if (CollectionUtils.isEmpty(douHuMSkList)) {
                return config.getShowType();
            }
            return douHuMSkList
                    .stream()
                    .filter(sk -> config.getDouHuSk2showType().containsKey(sk))
                    .findFirst()
                    .map(sk -> config.getDouHuSk2showType().get(sk))
                    .orElseGet(config::getShowType);
        }
        return config.doGetShowTypeByShelfVersion(request.getShelfVersion());
    }

    private void finalHandleBtn(ActivityCxt activityCxt, DzItemVO dzItemVO, ProductM productM) {
        try {
            FinalHandleBtnVP<?> extraVP = findVPoint(activityCxt, FinalHandleBtnVP.CODE);
            extraVP.execute(activityCxt,
                    FinalHandleBtnVP.Param.builder()
                            .dzItemVO(dzItemVO)
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private void fixShowConflict(ActivityCxt activityCxt, DzItemVO dzItemVO, ProductM productM, int productTotalNum, Request sourceRequest, String timesDealSk) {
        try {
            FixShowConflictVP<?> extraVP = findVPoint(activityCxt, FixShowConflictVP.CODE);
            extraVP.execute(activityCxt,
                    FixShowConflictVP.Param.builder()
                            .currentTotalNum(productTotalNum)
                            .extra(sourceRequest.getExtra())
                            .dzItemVO(dzItemVO)
                            .productM(productM)
                            .timesDealSk(timesDealSk)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private String getBasePrice(BigDecimal basePrice) {
        if (basePrice == null) {
            return null;
        }
        return basePrice.stripTrailingZeros().toPlainString();
    }

    /**
     * 富文本标题，目前只有关键词高亮逻辑
     * MARK：这个上下文可以替换掉，用到的参数不多
     *
     * @param activityCxt
     * @param builtTitle
     * @return
     */
    private String itemComponentRichLabelsTitle(ActivityCxt activityCxt, int platform, String builtTitle, String rawTitle) {
        try {
            String productTitle = StringUtils.isNotEmpty(builtTitle) ? builtTitle : rawTitle;
            ItemRichLabelsTitleVP<String> itemRichLabelsTitleVP = findVPoint(activityCxt, ItemRichLabelsTitleVP.CODE);
            return itemRichLabelsTitleVP.execute(activityCxt,
                    ItemRichLabelsTitleVP.Param.builder()
                            .sceneCode(activityCxt.getSceneCode())
                            .platform(platform)
                            .searchKeyword(activityCxt.getParam(ShelfActivityConstants.Params.searchKeyword))
                            .clientType(activityCxt.getParam(ShelfActivityConstants.Params.clientType))
                            .title(productTitle).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return null;
    }

    private long activityRemainSeconds(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemActivityRemainSecondsVP<?> itemActivityRemainSecondsVP = findVPoint(activityCxt, ItemActivityRemainSecondsVP.CODE);
            Long result = itemActivityRemainSecondsVP.execute(activityCxt, ItemActivityRemainSecondsVP.Param.builder().productM(productM).build());
            if (result == null) {
                return 0;
            }
            return result;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return 0L;
        }
    }

    private long activityStartTime(ActivityCxt activityContext, ProductM productM) {
        try {
            return PerfectActivityBuildUtils.isShowPerfectStartTime(productM, activityContext.getSceneCode()) ? PerfectActivityBuildUtils.getActivityStartTimeForShow(productM) : 0;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityContext, productM, e);
        }
        return 0;
    }

    private String itemComponentSalePriceDesc(ActivityCxt activityCxt, ProductM productM, String timesDealSk,long filterId,FilterM filterM) {
        try {
            ItemSalePriceRemarkVP<?> itemSalePriceRemarkVP = findVPoint(activityCxt, ItemSalePriceRemarkVP.CODE);
            return itemSalePriceRemarkVP.execute(activityCxt,
                    ItemSalePriceRemarkVP.Param.builder().productM(productM).timesDealSk(timesDealSk).filterId(filterId).filterM(filterM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * @param productM
     * @return 广义的购买信息，不局限于用户X小时前购买
     */
    private RichLabelVO itemComponentPurchase(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemPurchaseVP<?> itemPurchaseVP = findVPoint(activityCxt, ItemPurchaseVP.CODE);
            return itemPurchaseVP.execute(activityCxt,
                    ItemPurchaseVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private String itemComponentRichSale(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemRichSaleVP<?> itemRichSaleVP = findVPoint(activityCxt, ItemRichSaleVP.CODE);
            return itemRichSaleVP.execute(activityCxt,
                    ItemRichSaleVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private FloatTagGroup itemComponentFloatTag(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList,
                                                long filterId, int platform, int index) {
        try {
            ItemFloatTagPicVP<?> itemFloatTagPicVP = findVPoint(activityCxt, ItemFloatTagPicVP.CODE);
            return itemFloatTagPicVP.execute(activityCxt, ItemFloatTagPicVP.Param.builder().productM(productM)
                    .douHuList(douHuMList).filterId(filterId).platform(platform).index(index).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private PicAreaVO itemComponentPic(ActivityCxt activityCxt, ProductM productM, Request sourceRequest,
                                       List<DouHuM> douHuMList, int productTotalNum, long filterId, int index) {
        try {
            ItemPicVP<?> itemPicVP = findVPoint(activityCxt, ItemPicVP.CODE);
            PicAreaVO picAreaVO = itemPicVP.execute(activityCxt,
                    ItemPicVP.Param.builder().productM(productM).subScene(sourceRequest.getSubScene())
                            .douHuList(douHuMList).extra(sourceRequest.getExtra()).productTotalNum(productTotalNum)
                            .filterId(filterId).platform(sourceRequest.getPlatform()).build());

            int userAgent = Optional.ofNullable((Integer) activityCxt.getParam(ShelfActivityConstants.Params.userAgent)).orElse(0);
            //新的ItemFloatTagPicVP只对app生效
            if ((userAgent == VCClientTypeEnum.MT_APP.getCode() || userAgent == VCClientTypeEnum.DP_APP.getCode())) {
                FloatTagGroup floatTagGroup = itemComponentFloatTag(activityCxt, productM, douHuMList, filterId,
                        sourceRequest.getPlatform(), index);
                if (Objects.nonNull(picAreaVO) && Objects.nonNull(floatTagGroup)
                        && CollectionUtils.isNotEmpty(floatTagGroup.getFloatTagVOList())) {
                    picAreaVO.setFloatTags(floatTagGroup.getFloatTagVOList());
                    picAreaVO.setFloatTagType(floatTagGroup.getFloatTagType());
                }
            }
            return picAreaVO;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private VipPriceVO itemComponentVipPrice(ActivityCxt activityCxt, ProductM productM, CardM cardM, String timesDealSk) {
        try {
            ItemVipPriceVP<?> vPoint = findVPoint(activityCxt, ItemVipPriceVP.CODE);
            return vPoint.execute(activityCxt,
                    ItemVipPriceVP.Param.builder().productM(productM).cardM(cardM).timesDealSk(timesDealSk).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private RichLabelVO activityRemainSecondsLabel(ProductM productM) {
        return PerfectActivityBuildUtils.buildActivityRemainSecondsLabel(productM);
    }

    private String itemComponentLabs(ActivityCxt activityCxt, DzItemVO dzItemVO, int index, Request sourceRequest, CardM cardM, ProductM productM, List<DouHuM> douHuMList, long filterId) {
        try {
            ItemOceanLabsVP<?> itemOceanLabsVP = findVPoint(activityCxt, ItemOceanLabsVP.CODE);
            return itemOceanLabsVP.execute(activityCxt,
                    ItemOceanLabsVP.Param.builder()
                            .dzItemVO(dzItemVO)
                            .dpPoiId(PoiIdUtil.getDpPoiIdL(sourceRequest)).mtPoiId(PoiIdUtil.getMtPoiIdL(sourceRequest))
                            .productM(productM)
                            .shop(sourceRequest.getCtxShop()).platform(sourceRequest.getPlatform()).index(index)
                            .cardM(cardM).summaryDealIds(ParamUtil.getSummaryDealIds(sourceRequest.getSummarypids(), sourceRequest.getTopProductIds()))
                            .douHuList(douHuMList)
                            .filterId(filterId)
                            .flowId(getRecommendFlowId(activityCxt))
                            .cityId(PlatformUtil.isMT(sourceRequest.getPlatform()) ? activityCxt.getParam(ShelfActivityConstants.Params.mtCityId) : activityCxt.getParam(ShelfActivityConstants.Params.dpCityId))
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private DzSimpleButtonVO itemComponentBtn(ActivityCxt activityCxt, Request sourceRequest, String jumpUrl, ProductM productM, List<DouHuM> douHuMList, String salePrice, String groupName) {
        try {
            ItemButtonVP<?> itemButtonVP = findVPoint(activityCxt, ItemButtonVP.CODE);
            return itemButtonVP.execute(activityCxt, ItemButtonVP.Param.builder()
                    .subScene(sourceRequest.getSubScene())
                    .itemJumpUrl(jumpUrl)
                    .productM(productM)
                    .douHuMList(douHuMList)
                    .salePrice(salePrice)
                    .groupName(groupName)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<DzTagVO> itemComponentPriceAboveTags(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList, List<DzTagVO> priceBottomTags , String salePrice) {
        try {
            ItemPriceAboveTagsVP<?> itemPriceAboveTagsVP = findVPoint(activityCxt, ItemPriceAboveTagsVP.CODE);
            return itemPriceAboveTagsVP.execute(activityCxt,
                    ItemPriceAboveTagsVP.Param.builder()
                            .productM(productM).douHuList(douHuMList).priceBottomTags(priceBottomTags).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    public List<DzTagVO> itemComponentPriceBottomTagsForBar(ActivityCxt activityCxt, Request sourceRequest, ProductM productM, CardM cardM, String salePrice, List<DouHuM> douHuMList, int productTotalNum, String timesDealSk, Map<Integer, ProductM> allPreProducts) {
        List<DzPromoVO> dzPromoVOS = itemComponentPromo(activityCxt, sourceRequest, productM, cardM, salePrice, douHuMList, productTotalNum, timesDealSk, allPreProducts, "");
        return fillAttr(dzPromoVOS);
    }

    public List<DzTagVO> fillAttr(List<DzPromoVO> dzPromoVOS) {
        if (CollectionUtils.isEmpty(dzPromoVOS)) {
            return null;
        }

        List<DzTagVO> result = Lists.newArrayList();
        for (DzPromoVO dzPromoVO : dzPromoVOS) {
            DzTagVO dzTagVO = new DzTagVO();
            BeanUtils.copyProperties(dzPromoVO, dzTagVO);
            dzTagVO.setPromoDetail(dzPromoVO.getDetail());
            dzTagVO.setHasBorder(true);
            dzTagVO.setBorderRadius(3);
            dzTagVO.setTextColor(ColorUtils.colorFF4B10);
            dzTagVO.setBorderColor(ColorUtils.colorFF4B10);
            result.add(dzTagVO);
        }

        return CollectionUtils.isEmpty(result) ? null : result;
    }

    private List<DzTagVO> itemComponentPriceBottomTags(ActivityCxt activityCxt, ProductM productM, CardM cardM, String salePrice, int platform, List<DouHuM> douHuMList, String groupName) {
        try {
            ItemPriceBottomTagsVP<?> itemPriceBottomTagsVP = findVPoint(activityCxt, ItemPriceBottomTagsVP.CODE);
            return itemPriceBottomTagsVP.execute(activityCxt,
                    ItemPriceBottomTagsVP.Param.builder()
                            .productM(productM).cardM(cardM).platform(platform).douHuList(douHuMList)
                            .groupName(groupName).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<RichLabelVO> itemComponentBottomTags(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList, String salePrice) {
        try {
            ItemBottomTagsVP<?> itemBottomTagsVP = findVPoint(activityCxt, ItemBottomTagsVP.CODE);
            if (Objects.isNull(itemBottomTagsVP) || Objects.isNull(itemBottomTagsVP.getVPointOptionCode())) {
                return null;
            }
            return itemBottomTagsVP.execute(activityCxt,
                    ItemBottomTagsVP.Param.builder().productM(productM).douHuList(douHuMList).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private FloatTagVO preTitleTag(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList, long filterId, String groupName) {
        try {
            ItemPreTitleTagVP<?> itemPreTitleTagVP = findVPoint(activityCxt, ItemPreTitleTagVP.CODE);
            return itemPreTitleTagVP.execute(activityCxt,
                    ItemPreTitleTagVP.Param.builder()
                            .productM(productM)
                            .douHuList(douHuMList)
                            .filterId(filterId)
                            .groupName(groupName)
                            .build());
        } catch (Exception e) {
            //静默
            return null;
        }
    }

    private void clearProductTagsIfNeeded(DzItemVO itemVO) {
        if (CollectionUtils.isNotEmpty(itemVO.getProductRichTags())) {
            itemVO.setProductTags(null);
        }
    }

    private ProductAreaTipsComponentVO buildItemAreaTips(ActivityCxt activityCxt, Request sourceReq, long filterId, List<ProductM> currentFloorProductMs, FilterM filterM) {
        try {
            List<ProductActivityM> activities = activityCxt.getSource(ProductActivitiesFetcher.CODE);
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            ItemAreaTipsVP<?> itemAreaTipsVP = findVPoint(activityCxt, ItemAreaTipsVP.CODE);
            return itemAreaTipsVP.execute(activityCxt,
                    ItemAreaTipsVP.Param.builder()
                            .platform(sourceReq.getPlatform())
                            .filterId(filterId)
                            .douHuList(douHuMList)
                            .activities(activities)
                            .products(currentFloorProductMs)
                            .filterM(filterM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private int buildItemAreaShowType(ActivityCxt activityCxt, String groupName) {
        try {
            ItemAreaShowTypeVP<?> itemAreaShowTypeVP = findVPoint(activityCxt, ItemAreaShowTypeVP.CODE);
            return itemAreaShowTypeVP.execute(activityCxt,
                    ItemAreaShowTypeVP.Param.builder()
                            .groupName(groupName)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return 0;
        }
    }

    private DzMoreComponentVO buildMoreComponent(ActivityCxt activityCxt, DzItemAreaComponentVO dzItemAreaComponentVO, Request sourceReq, long filterId, String groupName) {
        DzMoreComponentVO dzMoreComponentVO = new DzMoreComponentVO();
        dzMoreComponentVO.setJumpUrl(moreComponentJumpUrl(activityCxt, sourceReq, groupName, filterId));
        dzMoreComponentVO.setText(moreComponentText(activityCxt, dzItemAreaComponentVO, groupName, filterId));
        return dzMoreComponentVO;
    }

    ///////////////////////////////////////////////扩展点冲突机制实现///////////////////////////////////////////////

    public List<DzPromoVO> itemComponentPromo(ActivityCxt activityCxt, Request sourceRequest, ProductM productM, CardM cardM, String salePrice, List<DouHuM> douHuMList, int productTotalNum, String timesDealSk, Map<Integer, ProductM> allPreProducts, String groupName) {
        try {
            ItemPromoTagsVP<?> vPoint = findVPoint(activityCxt, ItemPromoTagsVP.CODE);
            List<DzPromoVO> result = vPoint.execute(activityCxt,
                    ItemPromoTagsVP.Param.builder()
                            .productM(productM)
                            .cardM(cardM)
                            .salePrice(salePrice)
                            .douHuList(douHuMList)
                            .timesDealSk(timesDealSk)
                            .allProducts(allPreProducts)
                            .groupName(groupName)
                            .productTotalNum(productTotalNum).extra(sourceRequest.getExtra()).build());
            return filterForBar(activityCxt, productM, productTotalNum, formatPromoList(result));
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 酒吧行业单列货架多次卡过滤折扣标签
     */
    private List<DzPromoVO> filterForBar(ActivityCxt activityCxt, ProductM productM, int productTotalNum, List<DzPromoVO> promoList) {
        // 酒吧行业，并且是单列货架
        if (productM.getCategoryId() == 312 && !isBarDoubleMultiShelf(activityCxt, productM, productTotalNum)) {
            return CollectionUtils.isEmpty(promoList) ? promoList : Lists.newArrayList(promoList.get(promoList.size() - 1));
        }
        return promoList;
    }

    private List<DzPromoVO> formatPromoList(List<DzPromoVO> promoList) {
        if (CollectionUtils.isEmpty(promoList)) {
            return null;
        }
        promoList = promoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promoList)) {
            return null;
        }
        return promoList;
    }

    /**
     * 商品售价默认逻辑
     * 0、取卡价
     * 1、取玩美季价
     * 2、取立减价
     * 3、取默认售价
     *
     * @param activityCxt
     * @param productM
     * @return
     */
    private String itemComponentDefaultSalePrice(ActivityCxt activityCxt, ProductM productM, CardM cardM) {
        ProductPromoPriceM productPromoPriceM = PriceUtils.getUserHasPromoPrice(productM,cardM);
        if(Objects.nonNull(productPromoPriceM) && StringUtils.isNotBlank(productPromoPriceM.getPromoPriceTag())){
            return productPromoPriceM.getPromoPriceTag();
        }
        return productM.getBasePriceTag();
    }


    private List<String> itemComponentProductTags(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList, long filterId) {
        try {
            ItemProductTagsVP<?> itemProductTagsVP = findVPoint(activityCxt, ItemProductTagsVP.CODE);
            List<String> productTags = itemProductTagsVP.execute(activityCxt, ItemProductTagsVP.Param.builder().productM(productM).douHuMList(douHuMList).filterId(filterId).build());
            return formatStrList(productTags);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private List<RichLabelVO> itemProductRichTags(ActivityCxt activityCxt, ProductM productM, List<String> productTags, List<DouHuM> douHuMList) {
        try {
            ItemProductRichTagsVP<?> itemProductRichTagsVP = findVPoint(activityCxt, ItemProductRichTagsVP.CODE);
            return itemProductRichTagsVP.execute(activityCxt, ItemProductRichTagsVP.Param.builder().productM(productM).productTags(productTags).douHuMList(douHuMList).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private List<String> formatStrList(List<String> stringList) {
        if (CollectionUtils.isEmpty(stringList)) {
            return null;
        }
        // 空白的不能屏蔽
        stringList = stringList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stringList)) {
            return null;
        }
        return stringList;
    }

    /**
     * 销量标签
     *
     * @param productM
     * @return
     */
    private String itemComponentSale(ActivityCxt activityCxt, ProductM productM, String groupName) {
        if (productM.getSale() == null || StringUtils.isBlank(productM.getSale().getSaleTag())) {
            return StringUtils.EMPTY;
        }
        //走ItemSaleVP实现
        return itemComponentSaleVP(activityCxt, productM, groupName);
    }

    private String itemComponentSaleVP(ActivityCxt activityCxt, ProductM productM, String groupName) {
        try {
            ItemSaleVP<?> itemSaleVP = findVPoint(activityCxt, ItemSaleVP.CODE);
            return itemSaleVP.execute(activityCxt, ItemSaleVP.Param.builder().productM(productM).groupName(groupName).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return productM.getSale().getSaleTag();
        }
    }

    private String itemComponentMarketPrice(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList, String timesDealSk) {
        try {
            ItemMarketPriceVP<?> itemMarketPriceVP = findVPoint(activityCxt, ItemMarketPriceVP.CODE);
            String price = itemMarketPriceVP.execute(activityCxt, ItemMarketPriceVP.Param.builder().productM(productM).douHuList(douHuMList).timesDealSk(timesDealSk).build());
            if (FloorsBuilderExtAdapter.EMPTY_VALUE.equals(price)) {
                return StringUtils.EMPTY;
            }
            if (StringUtils.isNotBlank(price)) {
                return price;
            }
            return productM.getMarketPrice();
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private String itemComponentSalePrice(ActivityCxt activityCxt, ProductM productM, CardM cardM, String groupName) {
        try {
            ItemSalePriceVP<?> itemSalePriceVP = findVPoint(activityCxt, ItemSalePriceVP.CODE);
            String price = itemSalePriceVP.execute(activityCxt, ItemSalePriceVP.Param.builder().productM(productM).groupName(groupName).cardM(cardM).build());
            if (FloorsBuilderExtAdapter.EMPTY_VALUE.equals(price)) {
                return StringUtils.EMPTY;
            }
            if (StringUtils.isNotBlank(price)) {
                return price;
            }
            return itemComponentDefaultSalePrice(activityCxt, productM, cardM);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private String itemComponentMarketPriceDesc(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList) {
        try {
            ItemMarketPriceDescVP<?> itemMarketPriceDescVP = findVPoint(activityCxt, ItemMarketPriceDescVP.CODE);
            return itemMarketPriceDescVP.execute(activityCxt, ItemMarketPriceDescVP.Param.builder().productM(productM).douHuList(douHuMList).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private String itemComponentTitle(ActivityCxt activityCxt, ProductM productM, long filterId, List<DouHuM> douHuMList, Request sourceRequest) {
        try {
            ItemTitleVP<?> itemTitleVP = findVPoint(activityCxt, ItemTitleVP.CODE);
            return itemTitleVP.execute(activityCxt,
                    ItemTitleVP.Param.builder()
                            .dpPoiId(PoiIdUtil.getDpPoiIdL(sourceRequest)).mtPoiId(PoiIdUtil.getMtPoiIdL(sourceRequest))
                            .productM(productM)
                            .douHuList(douHuMList)
                            .filterId(filterId).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private String moreComponentJumpUrl(ActivityCxt activityCxt, Request sourceReq, String groupName, long filterId) {
        try {
            MoreComponentUrlVP<?> moreComponentUrlVP = findVPoint(activityCxt, MoreComponentUrlVP.CODE);
            return moreComponentUrlVP.execute(activityCxt,
                    MoreComponentUrlVP.Param.builder()
                            .dpPoiId(sourceReq.getDpPoiId()).mtPoiId(sourceReq.getMtPoiId())
                            .dpPoiIdL(PoiIdUtil.getDpPoiIdL(sourceReq)).mtPoiIdL(PoiIdUtil.getMtPoiIdL(sourceReq))
                            .platform(sourceReq.getPlatform()).filterId(filterId)
                            .shopUuid(sourceReq.getShopUuid()).userAgent(sourceReq.getUserAgent())
                            .groupName(groupName)
                            .cityId(PlatformUtil.isMT(sourceReq.getPlatform()) ? activityCxt.getParam(ShelfActivityConstants.Params.mtCityId) : activityCxt.getParam(ShelfActivityConstants.Params.dpCityId))
                            .locationCityId(activityCxt.getParam(ShelfActivityConstants.Params.locationCityId))
                            .lat(activityCxt.getParam(ShelfActivityConstants.Params.lat))
                            .lng(activityCxt.getParam(ShelfActivityConstants.Params.lng))
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }


    /**
     * 查看更多-文案
     *
     * @param activityCxt
     * @param itemAreaComponentVO
     * @return
     */
    private String moreComponentText(ActivityCxt activityCxt, DzItemAreaComponentVO itemAreaComponentVO, String groupName, long filterId) {
        try {
            MoreComponentTextVP<?> moreComponentTextVP = findVPoint(activityCxt, MoreComponentTextVP.CODE);
            int itemAreaItemCnt = CollectionUtils.isEmpty(itemAreaComponentVO.getProductItems()) ? 0 : itemAreaComponentVO.getProductItems().size();
            int unavailableItemCnt = CollectionUtils.isEmpty(itemAreaComponentVO.getProductItems()) ? 0
                    : (int) itemAreaComponentVO.getProductItems().stream().filter(productItem -> !productItem.isAvailable()).count();
            return moreComponentTextVP.execute(activityCxt,
                    MoreComponentTextVP.Param.builder()
                            .itemAreaItemCnt(itemAreaItemCnt)
                            .unavailableItemCnt(unavailableItemCnt)
                            .defaultShowNum(itemAreaComponentVO.getDefaultShowNum())
                            .groupName(groupName)
                            .filterId(filterId)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private Boolean itemAvailable(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemAvailableVP<?> itemAvailableVP = findVPoint(activityCxt, ItemAvailableVP.CODE);
            Boolean available = itemAvailableVP.execute(activityCxt, ItemAvailableVP.Param.builder()
                    .productM(productM)
                    .build());
            // 兜底做一层空判断，防止有 VP 没实现默认的
            return Objects.nonNull(available) ? available : Boolean.TRUE;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return Boolean.TRUE;
        }
    }

    private WarmUpVO itemWarmUp(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemWarmUpVP<?> itemWarmUpVP = findVPoint(activityCxt, ItemWarmUpVP.CODE);
            WarmUpVO warmUpVO = itemWarmUpVP.execute(activityCxt, ItemWarmUpVP.Param.builder()
                    .productM(productM)
                    .build());
            return warmUpVO;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private String itemPriceTips(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemPriceTipsVP<?> itemWarmUpVP = findVPoint(activityCxt, ItemPriceTipsVP.CODE);
            String itemPriceTips = itemWarmUpVP.execute(activityCxt, ItemPriceTipsVP.Param.builder()
                    .productM(productM)
                    .build());
            return itemPriceTips;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private List<RichLabelVO> buildButtonCarouselMsg(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemButtonCarouselMsgVP<?> buttonCarouselMsgVP = findVPoint(activityCxt, ItemButtonCarouselMsgVP.CODE);
            List<RichLabelVO>   buttonCarouselMsgs = buttonCarouselMsgVP.execute(activityCxt,
                    ItemButtonCarouselMsgVP.Param.builder().productM(productM).build());
            return buttonCarouselMsgs;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private String itemJumpUrl(ActivityCxt activityCxt, ProductM productM, Request sourceRequest) {
        try {
            ItemJumpUrlVP<?> itemJumpUrlVP = findVPoint(activityCxt, ItemJumpUrlVP.CODE);
            String jumpUrl = itemJumpUrlVP.execute(activityCxt, ItemJumpUrlVP.Param.builder()
                    .productM(productM).platform(sourceRequest.getPlatform())
                    .dpPoiId(sourceRequest.getDpPoiId()).mtPoiId(sourceRequest.getMtPoiId())
                    .dpPoiIdL(PoiIdUtil.getDpPoiIdL(sourceRequest)).mtPoiIdL(PoiIdUtil.getMtPoiIdL(sourceRequest))
                    .subScene(sourceRequest.getSubScene())
                    .userAgent(sourceRequest.userAgent)
                    .build());
            // 兜底做一层空判断，防止有 VP 没实现默认的
            return StringUtils.isEmpty(jumpUrl) ? productM.getJumpUrl() : jumpUrl;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return productM.getJumpUrl();
        }
    }

    private int itemShowType(ActivityCxt activityCxt, Config config, Request request, List<DouHuM> douHuMList, ProductM productM, DzItemVO dzItemVO) {
        try {
            ItemShowTypeVP<?> itemShowTypeVP = findVPoint(activityCxt, ItemShowTypeVP.CODE);
            if (itemShowTypeVP == null) {
                return itemShowType(config, request, douHuMList);
            }
            Integer showType = itemShowTypeVP
                    .execute(activityCxt, ItemShowTypeVP.Param.builder()
                            .productM(productM)
                            .dzItemVO(dzItemVO)
                            .douHuList(douHuMList)
                            .appVersion(request.getAppVersion())
                            .platform(request.getPlatform())
                            .build());
            if (showType == null) {
                //等历史包袱迁移完，这块逻辑可以下线
                return itemShowType(config, request, douHuMList);
            }
            return showType;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return DzItemShowTypeEnums.DEFAULT.getType();
        }
    }

    /**
     * 后期会逐渐下线
     *
     * @param config
     * @param request
     * @param douHuMList
     * @return
     */
    private int itemShowType(Config config, Request request, List<DouHuM> douHuMList) {
        String appVersion = request.getAppVersion();
        int platform = request.getPlatform();
        String limitVersion = config.getDpLimitVersion();
        if (PlatformUtil.isMT(platform)) {
            limitVersion = config.getMtLimitVersion();
        }
        if (StringUtils.isEmpty(limitVersion) || VersionUtil.compare(appVersion, limitVersion) <= 0) {
            return DzItemShowTypeEnums.DEFAULT.getType();
        }
        if (MapUtils.isNotEmpty(config.getDouHuSk2ShowType())) {
            return douHuMList
                    .stream()
                    .filter(douHuM -> config.getDouHuSk2ShowType().containsKey(douHuM.getSk()))
                    .findFirst()
                    .map(douHuM -> config.getDouHuSk2ShowType().get(douHuM.getSk()))
                    .orElseGet(config::getItemShowType);
        }
        return config.getItemShowType();
    }

    private ProgressBarVO progressBar(ActivityCxt activityCxt, ProductM productM) {
        try {
            ItemProgressBarVP<?> progressBarVP = findVPoint(activityCxt, ItemProgressBarVP.CODE);
            return progressBarVP.execute(activityCxt, ItemProgressBarVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private String getExtra(ActivityCxt activityCxt, ProductM productM, List<DouHuM> douHuMList) {
        try {
            ItemExtraVP<?> extraVP = findVPoint(activityCxt, ItemExtraVP.CODE);
            return extraVP.execute(activityCxt, ItemExtraVP.Param.builder().productM(productM).douHuList(douHuMList).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private CountdownLabelVO getActivityEndTime(ProductM productM, ActivityCxt activityCxt, List<DouHuM> douHuMList) {
        try {
            ItemActivityEndTimeVP<?> itemActivityEndTimeVP = findVPoint(activityCxt, ItemActivityEndTimeVP.CODE);
            return itemActivityEndTimeVP.execute(activityCxt, ItemActivityEndTimeVP.Param.builder()
                    .productM(productM)
                    .douHuList(douHuMList)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    ///////////////////////////////////////////////商品分组适配逻辑///////////////////////////////////////////////
    // 1. 如果上下文中有分好组的商品, 那么优先使用, 否则通过扩展点分组
    private Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM,
                                                    ProductGroupM productGroupM, Config config) {
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return null;
        }
        CompletableFuture<Map<String, Map<Long, List<ProductM>>>> group2GroupsCompletableFuture = activityContext.getAttachment(com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder.Attachments.groupedProducts);
        if (group2GroupsCompletableFuture == null) {
            return findExtPoint(activityContext, FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE).groupByFilter(activityContext, groupName, filterM, productGroupM);
        }
        Map<Long, List<ProductM>> groupedProducts = group2GroupsCompletableFuture.join().get(groupName);
        if (config.isGroupTotalProductByFilter()) {
            return groupTotalProductByFilter(groupedProducts, productGroupM, config);
        }
        if (MapUtils.isNotEmpty(groupedProducts)) {
            return groupedProducts;
        }
        return findExtPoint(activityContext, FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE).groupByFilter(activityContext, groupName, filterM, productGroupM);
    }

    private Map<Long, List<ProductM>> groupTotalProductByFilter(Map<Long, List<ProductM>> groupedProducts, ProductGroupM productGroupM, Config config) {
        if (MapUtils.isEmpty(groupedProducts)) {
            return groupedProducts;
        }
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return new HashMap<>();
        }
        Map<Long, List<ProductM>> result = new HashMap<>();
        for (Long filterId : groupedProducts.keySet()) {
            if (CollectionUtils.isEmpty(groupedProducts.get(filterId))) {
                continue;
            }
            result.put(filterId, readProductFromProductGroup(productGroupM.getProducts(), groupedProducts.get(filterId), config));
        }
        return result;
    }

    private List<ProductM> readProductFromProductGroup(List<ProductM> paddingProducts, List<ProductM> filterProducts, Config config) {
        return filterProducts.stream()
                .map(filterProduct -> getPaddingProductM(filterProduct, paddingProducts, config))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ProductM getPaddingProductM(ProductM filterProduct, List<ProductM> paddingProducts, Config config) {
        ProductM paddingProductM = findProductByIdAndType(filterProduct, paddingProducts);
        if (paddingProductM == null) {
            return null;
        }
        ProductM result = new ProductM();
        BeanUtils.copyProperties(paddingProductM, result);
        mergeAttr(filterProduct, result, config);
        return result;
    }

    private void mergeAttr(ProductM filterProduct, ProductM paddingProductM, Config config) {
        if (CollectionUtils.isEmpty(config.getMergeProductAttrFromQuery()) || CollectionUtils.isEmpty(filterProduct.getExtAttrs())) {
            return;
        }
        //之前是浅拷贝，attr对象被重写之后会整个被修改。新需求需要分组间属性隔离，统一修改会影响未知线上场景，这里加个配置控制
        if (config.mergeProductAttrIsFilterIsolate) {
            paddingProductM.setExtAttrs(deepCopyAttrMs(paddingProductM.getExtAttrs()));
        }
        for (String attrKey : config.getMergeProductAttrFromQuery()) {
            String attrValue = filterProduct.getAttr(attrKey);
            if (attrValue == null) {
                continue;
            }
            mergeProductAttr(paddingProductM, attrKey, attrValue);
        }
    }

    private List<AttrM> deepCopyAttrMs(List<AttrM> sourceAttrMs) {
        List<AttrM> newAttrMs = sourceAttrMs.stream().filter(Objects::nonNull)
                .map(item -> new AttrM(item.getName(), item.getValue())).collect(Collectors.toList());
        return newAttrMs;
    }

    private void mergeProductAttr(ProductM productM, String attrKey, String attrValue) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList(new AttrM(attrKey, attrValue)));
            return;
        }
        for (AttrM extAttr : productM.getExtAttrs()) {
            if (!StringUtils.equals(extAttr.getName(), attrKey)) {
                continue;
            }
            // 找到后用直接覆盖，然后返回
            extAttr.setValue(attrValue);
            return;
        }
        productM.getExtAttrs().add(new AttrM(attrKey, attrValue));
    }

    /**
     * 根据召回的productM找到主题渲染后的productM
     *
     * @param queryProduct    召回拿到的productM
     * @param paddingProducts 主题返回的productM集合
     * @return 找到的productM
     */
    private ProductM findProductByIdAndType(ProductM queryProduct, List<ProductM> paddingProducts) {
        return paddingProducts.stream()
                .filter(productM -> productM.getActProductId() == queryProduct.getActProductId()
                        && productM.getActProductType() == queryProduct.getActProductType())
                .findFirst().orElse(null);
    }

    /**
     * 查找扩展点实现
     *
     * @param extCode
     * @return
     */
    protected FloorsBuilderExt findExtPoint(ActivityContext ctx, String extCode) {

        return (FloorsBuilderExt) componentFinder.findExtPoint(ctx, extCode);
    }

    private String getRecommendFlowId(ActivityCxt ctx) {
        if (ctx == null || MapUtils.isEmpty(ctx.getParameters())) {
            return null;
        }
        CompletableFuture<String> future = ctx.getParam(ShelfActivityConstants.Ctx.ctxRecommendFlowId);
        if (future == null) {
            return null;
        }
        try {
            //最多允许影响业务50ms，并行处理
            return future.get(50, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
            return null;
        }
    }

    private String getTimesDealSk(List<DouHuM> douHuMList, Config config, int platform){
        if (CollectionUtils.isEmpty(douHuMList) || MapUtils.isEmpty(config.getTimesDealExpIdMap())){
            return null;
        }
        String expId = config.getTimesDealExpIdMap().get(platform);
        if (StringUtils.isBlank(expId)){
            return null;
        }
        return douHuMList.stream().filter(douHuM -> StringUtils.equals(douHuM.getExpId(), expId)).map(DouHuM::getSk).findFirst().orElse(null);
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 旧框架的适配配置
         */
        private OldEngineAdaptCfg oldEngineCfg;

        /**
         * 货架楼层默认展示商品数
         */
        @Deprecated
        private int floorDefaultShowNum = 3;

        /**
         * key - 斗斛分流结果
         * value - 货架楼层默认展示商品数
         */
        @Deprecated
        private Map<String, Integer> douHu2floorDefaultShowNum;

        /**
         * 将所有的商品按照filter分组
         */
        private boolean groupTotalProductByFilter = false;

        /**
         * ProductM中，将召回获取的attr合并到最终attr
         * 召回的attr优先级更高
         */
        private List<String> mergeProductAttrFromQuery;

        /**
         * 召回的attr的重写逻辑是否不同分组间隔离
         */
        private boolean mergeProductAttrIsFilterIsolate = false;

        /**
         * @param douHuMList
         * @return 获取货架默认展示商品数
         */
        public int doGetFloorDefaultShowNum(List<DouHuM> douHuMList) {
            //1. 配置斗斛映射，且命中斗斛，则返回映射项
            if (MapUtils.isNotEmpty(this.douHu2floorDefaultShowNum)) {
                Cat.logEvent("ExpHarnessing", "com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder.Config.doGetFloorDefaultShowNum");
                for (Map.Entry<String, Integer> entry : douHu2floorDefaultShowNum.entrySet()) {
                    if (entry.getValue() != null && DouHuUtils.hitAnySk(douHuMList, entry.getKey())) {
                        return entry.getValue();
                    }
                }
            }
            //2. 返回默认配置项
            return this.floorDefaultShowNum;
        }

        /**
         * 商品显示样式类型
         * {@link com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums}
         */
        @Deprecated
        private int itemShowType = 1;
        /**
         * 展示团单标题折行的点评最低版本
         */
        @Deprecated
        String dpLimitVersion = "10.47.0";
        /**
         * 展示团单标题折行的美团最低版本
         */
        @Deprecated
        String mtLimitVersion = "11.10.400";
        /**
         * 斗斛实验策略和商品显示样式类型 {@link com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums}映射关系
         */
        @Deprecated
        Map<String, Integer> douHuSk2ShowType;

        /**
         * 斗斛实验策略和商品显示样式类型
         */
        Map<String, Integer> douHuSkItemAreaShowType;

        /**
         * 多次卡的实验号  平台 -> 实验号
         */
        Map<Integer, String> timesDealExpIdMap;

    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * 【一级参数, List<String>型, 必填】自定义组名
         * {@link com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher} 时作为配置传入
         * {@link QueryFetcher.Params#groupNames}
         */
        private List<String> groupNames;

        /**
         * 当前选中的筛选标签ID
         * {@link ShelfActivityConstants.Params#selectedFilterId}
         */
        private long selectedFilterId;

        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private int dpPoiId;
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private int mtPoiId;
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Ctx#ctxShop}
         */
        private ShopM ctxShop;

        /**
         * {@link ShelfActivityConstants.Params#subScene}
         */
        private String subScene;

        /**
         * {@link ShelfActivityConstants.Params#shopUuid}
         */
        private String shopUuid;

        /**
         * {@link ShelfActivityConstants.Params#topProductIds}
         */
        private String topProductIds;

        /**
         * {@link ShelfActivityConstants.Params#summaryProductIds}
         */
        private String summarypids;

        /**
         * {@link ShelfActivityConstants.Params#appVersion}
         */
        private String appVersion;

        /**
         * {@link ShelfActivityConstants.Params#extra}
         */
        private String extra;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;
    }
}