package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

@VPoint(name = "市场价", description = "市场价", code = UnifiedShelfItemMarketPriceVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemMarketPriceVP<T> extends PmfVPoint<String, UnifiedShelfItemMarketPriceVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemMarketPriceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductM productM;
    }
}