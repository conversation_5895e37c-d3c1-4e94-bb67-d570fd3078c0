package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn.BtnNameBuilderFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.JumpUrlEnum;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

public class JumpUrlFactory {

    private static final String ATTR_MINIPROGRAM_JUMP_URL = "meidiMiniprogramLink";

    private static final Map<String, JumpUrlBuilder> jumpUrlBuilders = Maps.newHashMap();

    static {
        jumpUrlBuilders.put(JumpUrlEnum.APPLIANCE_MINIPROGRAM.getCode(), JumpUrlFactory.getApplianceMiniprogramJumpUrlBuilder());
        jumpUrlBuilders.put(JumpUrlEnum.RECYCLE_MINIPROGRAM.getCode(), JumpUrlFactory.getRecycleMiniprogramJumpUrlBuilder());
    }

    public static String getJumpUrl(ActivityCxt context, ProductM productM, String strategy) {
        if (StringUtils.isEmpty(strategy)) {
            return StringUtils.EMPTY;
        }
        JumpUrlBuilder jumpUrlBuilder = jumpUrlBuilders.get(strategy);
        if (Objects.nonNull(jumpUrlBuilder)) {
            return jumpUrlBuilder.build(context, productM);
        }
        return StringUtils.EMPTY;
    }

    private static JumpUrlBuilder getApplianceMiniprogramJumpUrlBuilder() {
        return (context, productM) -> {
            if (ApplianceShelfUtils.isMiniprogramLinked(productM)) {
                return productM.getAttr(ATTR_MINIPROGRAM_JUMP_URL);
            }
            return productM.getJumpUrl();
        };
    }

    private static JumpUrlBuilder getRecycleMiniprogramJumpUrlBuilder() {
        return (context, productM) -> {
            return productM.getAttr(ATTR_MINIPROGRAM_JUMP_URL);
        };
    }

    @FunctionalInterface
    public interface JumpUrlBuilder {
        String build(ActivityCxt context, ProductM productM);
    }
}
