package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.price;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemPriceVP;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Objects;

@VPointOption(name = "聚合卡片价格信息-默认",
        description = "默认聚合卡片价格信息",
        code = DefaultAggregateItemPriceOpt.CODE,
        isDefault = true)
public class DefaultAggregateItemPriceOpt extends AggregateItemPriceVP<DefaultAggregateItemPriceOpt.Config> {

    public static final String CODE = "DefaultAggregateItemPriceOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        ShelfItemVO shelfItemVO = param.getShelfItemVO();
        shelfItemVO.setSalePrice(minSalePrice(shelfItemVO));
        shelfItemVO.setSalePriceSuffix("起");
        return null;
    }

    private String minSalePrice(ShelfItemVO shelfItemVO) {
        if(CollectionUtils.isEmpty(shelfItemVO.getSubItems())) {
            return shelfItemVO.getSalePrice();
        }
        return shelfItemVO.getSubItems().stream().map(ShelfItemVO::getSalePrice).filter(Objects::nonNull)
                .min(Comparator.comparing(BigDecimal::new)).orElse("0");
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
