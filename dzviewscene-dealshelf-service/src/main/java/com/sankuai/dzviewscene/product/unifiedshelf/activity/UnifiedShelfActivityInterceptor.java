package com.sankuai.dzviewscene.product.unifiedshelf.activity;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.annotation.ActivityInterceptor;
import com.sankuai.athena.viewscene.framework.core.IActivityInterceptor;
import com.sankuai.dzviewscene.dealshelf.service.UnifiedShelfService;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.shelf.utils.LogControl;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.JumpUrlPatternEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.lang.StringUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.JumpUrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.sankuai.dzviewscene.product.unifiedshelf.utils.JumpUrlUtils.OTHER_TYPE;

@ActivityInterceptor(name = "货架活动打点拦截器", code = "activity_unified_shelf_interceptor", activity = UnifiedShelfActivity.CODE)
public class UnifiedShelfActivityInterceptor implements IActivityInterceptor<UnifiedShelfResponse> {

    private static final String REFRESH_PREFIX = "refresh.";

    private static final String FIRST_LOAD = "UNIFIED_SHELF_FIRST_LOAD_APP";

    private static final String JUMPURL_TYPE = "JumpUrlType.";
    private static final String ERROR_JUMPURL = "ShelfOtherJumpUrl";
    private static final String JUMPURL_TIME = "JumpUrl_Classify_Time";

    private static final Logger LOGGER = LoggerFactory.getLogger(UnifiedShelfActivityInterceptor.class.getSimpleName());

    @Override
    public void beforeExecute(InterceptorContext<UnifiedShelfResponse> interceptorContext) {

    }

    @Override
    public void complete(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        try {
            Cat.logMetricForCount(interceptorContext.getActivityCode(), buildMetricTags(interceptorContext, result));
            catWithCategory(interceptorContext, result);
            logMetricForRefreshCount(interceptorContext, result);
            logJumpUrlType(interceptorContext, result);
        } catch (Exception e) {
            /*静默*/
            Cat.logError(e);
        }
    }
    public void logJumpUrlType(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        // 记录 items和button跳链类型的指标数据
        // 记录时间，5ms以内进入到结束
        //优化成一次调用。
        long startTime = System.currentTimeMillis();
        List<HashMap<String, String>> metricTags = buildJumpUrlTypeMetricTags(interceptorContext, result);
        // 记录跳链类型的指标计数
        String name = JUMPURL_TYPE + interceptorContext.getActivityCode();
        for (HashMap<String, String> tag : metricTags) {
            Cat.logMetricForCount(name, tag);
        }
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        Cat.newCompletedTransactionWithDuration(JUMPURL_TIME, interceptorContext.getActivityCode(), executionTime);

    }
    private void catWithCategory(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        String requestType = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.requestType);
        long startTime = ParamsUtil.getLongSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.startTime);
        ShopM shopM = (ShopM) interceptorContext.getParameters().get(ShelfActivityConstants.Ctx.ctxShop);
        int userAgent = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
        if (startTime <= 0 || shopM == null) {
            return;
        }
        //记录app内首屏请求耗时
        if (RequestTypeEnum.API_UNIFIED_SHELF.getType().equals(requestType) && PlatformUtil.isApp(userAgent)) {
            Cat.newCompletedTransactionWithDuration(FIRST_LOAD, String.valueOf(shopM.getShopType()), System.currentTimeMillis() - startTime);
        }
    }

    private void logMetricForRefreshCount(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        String refreshTag = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.refreshTag);
        if (StringUtils.isBlank(refreshTag)) {
            return;
        }
        Cat.logMetricForCount(REFRESH_PREFIX + interceptorContext.getActivityCode(), buildRefreshMetricTags(interceptorContext, result, refreshTag));
    }

    private Map<String, String> buildMetricTags(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse UnifiedShelfResponse) {
        UnifiedShelfResponse finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : UnifiedShelfResponse;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("hasFilters", Boolean.toString(UnifiedShelfModelUtils.hasFilters(finalResult)));
            put("hasProducts", Boolean.toString(UnifiedShelfModelUtils.hasProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if (StringUtils.isNotEmpty(clientTypeMsg)) {
                put("clientType", clientTypeMsg);
            }
        }};
    }

    private Map<String, String> buildRefreshMetricTags(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse UnifiedShelfResponse, String refreshTag) {
        UnifiedShelfResponse finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : UnifiedShelfResponse;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("refreshTag", refreshTag);
            put("hasProducts", Boolean.toString(UnifiedShelfModelUtils.hasProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if (StringUtils.isNotEmpty(clientTypeMsg)) {
                put("clientType", clientTypeMsg);
            }
        }};
    }

    private List<HashMap<String, String>> buildJumpUrlTypeMetricTags(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse UnifiedShelfResponse) {
        UnifiedShelfResponse finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : UnifiedShelfResponse;
        List<ShelfFilterProductAreaVO> filterIdAndProductAreas = finalResult.getFilterIdAndProductAreas();
        //1.获取jumpurl
        String itemsJumpUrl = Optional.ofNullable(filterIdAndProductAreas)
                .filter(list -> !list.isEmpty()).map(list -> list.get(0)).filter(Objects::nonNull) // 检查 ShelfFilterProductAreaVO 是否为 null
                .map(ShelfFilterProductAreaVO::getProductAreas).filter(list -> !list.isEmpty())
                .map(list -> list.get(0)).filter(Objects::nonNull) // 检查 ShelfProductAreaVO 是否为 null
                .map(ShelfProductAreaVO::getItems).filter(list -> !list.isEmpty())
                .map(list -> list.get(0)).filter(Objects::nonNull) // 检查 ShelfItemVO 是否为 null
                .map(ShelfItemVO::getJumpUrl).orElse(null);
        String buttonJumpUrl = Optional.ofNullable(filterIdAndProductAreas)
                .filter(list -> !list.isEmpty()).map(list -> list.get(0))
                .filter(Objects::nonNull).map(ShelfFilterProductAreaVO::getProductAreas)
                .filter(list -> !list.isEmpty()).map(list -> list.get(0))
                .filter(Objects::nonNull).map(ShelfProductAreaVO::getItems)
                .filter(list -> !list.isEmpty()).map(list -> list.get(0))
                .filter(Objects::nonNull).map(ShelfItemVO::getButton)
                .filter(Objects::nonNull).map(button -> button.getJumpUrl())
                .orElse(null);
        //2. 得到跳链类型
        String itemsType = JumpUrlUtils.getJumpUrlType(itemsJumpUrl);
        String buttonType;
        if(Objects.equals(buttonJumpUrl, itemsJumpUrl)){
             buttonType= itemsType;
        }
        else{
             buttonType = JumpUrlUtils.getJumpUrlType(buttonJumpUrl);
        }
        String sceneCode = interceptorContext.getSceneCode();
        handleOtherTypeJumpUrl(itemsType, itemsJumpUrl, sceneCode, finalResult);
        handleOtherTypeJumpUrl(buttonType, buttonJumpUrl, sceneCode, finalResult);

        List<HashMap<String, String>> tags = new ArrayList<>();
        tags.add(new HashMap<String, String>(){{
            put("sceneCode", sceneCode);
            put("JumpUrlType", itemsType.toLowerCase());//方便人类阅读
            put("itemsOrButton","items");
        }});
        tags.add(new HashMap<String, String>(){{
            put("sceneCode", sceneCode);
            put("JumpUrlType", buttonType.toLowerCase());//方便人类阅读
            put("itemsOrButton","button");
        }});
        return tags;
    }
    private void handleOtherTypeJumpUrl(String type, String jumpUrl, String sceneCode, UnifiedShelfResponse finalResult) {
        if (OTHER_TYPE.equals(type)) {
            Cat.logEvent(ERROR_JUMPURL, sceneCode + "." + JumpUrlUtils.extractOutlier(jumpUrl));
            LogControl.logFuc(null, () -> LOGGER.info("jumpUrlOtherType,request:{}",
                    SafeJacksonUtils.serialize(finalResult)));
        }
    }


}
