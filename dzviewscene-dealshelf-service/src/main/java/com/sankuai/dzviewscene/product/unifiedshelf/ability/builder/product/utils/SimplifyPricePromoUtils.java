package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils;

import com.dianping.frog.sdk.util.CollectionUtils;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SimplifyPricePromoUtils {
    /**
     * 优惠收敛未覆盖的货架类型，目前支持配置fShelf、doubleColumn
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.promo.simplify.uncovered.shelf.types", defaultValue = "[]")
    private static List<String> uncoveredShelfTypes;
    /**
     * uncoveredShelfTypes中包含的货架类型，normal表示正常的单列T型货架
     */
    private static final String SHELF_TYPE_F = "fShelf";
    private static final String SHELF_TYPE_DOUBLE_COLUMN = "doubleColumn";

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.promo.simplify.exp.sks", defaultValue = "[]")
    private static List<String> simplifyExpSks;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.promo.simplify.switch", defaultValue = "false")
    private static Boolean simplifyPricePromoSwitch;

    /**
     * 保留的价格下方优惠策略，通过strategyName判断
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.promo.simplify.retained.strategies", defaultValue = "[]")
    private static List<String> retainedStrategies;
    
    public static boolean isRetainedStrategy(String strategy) {
        return CollectionUtils.isNotEmpty(retainedStrategies) && retainedStrategies.contains(strategy);
    }

    public static boolean hitSimplifyPromoTagConfig(ActivityCxt context) {
        ShelfTypeInfo shelfTypeInfo = extractShelfTypeInfo(context);
        boolean hitPromoSimplifyExp = checkPromoSimplifyExp(context);

        return hitPromoSimplifyExp && !shelfTypeInfo.isUnsupportedShelfType() && simplifyPricePromoSwitch;
    }

    private static ShelfTypeInfo extractShelfTypeInfo(ActivityCxt context) {
        boolean isFShelf = ParamsUtil.getBooleanSafely(context.getParameters(),
                ShelfActivityConstants.Params.isFShelf);

        int doubleColumnValue = ParamsUtil.getIntSafely(context,
                ShelfActivityConstants.Params.doubleColumnShelf);
        boolean isDoubleColumnShelf = doubleColumnValue == 1;

        return new ShelfTypeInfo(isFShelf, isDoubleColumnShelf);
    }

    private static boolean checkPromoSimplifyExp(ActivityCxt context) {
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if(CollectionUtils.isEmpty(simplifyExpSks)){
            //如果没有实验约束
            return true;
        }
        return DouHuUtils.hitAnySk(douHuMList, simplifyExpSks);
    }

    private static class ShelfTypeInfo {
        private final boolean isFShelf;
        private final boolean isDoubleColumnShelf;
        //后续补充一品多态货架逻辑

        public ShelfTypeInfo(boolean isFShelf, boolean isDoubleColumnShelf) {
            this.isFShelf = isFShelf;
            this.isDoubleColumnShelf = isDoubleColumnShelf;
        }

        public boolean isUnsupportedShelfType() {
            if(CollectionUtils.isEmpty(uncoveredShelfTypes)){
                return true;
            }
            //后续补充一品多态货架逻辑
            return (uncoveredShelfTypes.contains(SHELF_TYPE_F) && isFShelf)
                    || (uncoveredShelfTypes.contains(SHELF_TYPE_DOUBLE_COLUMN) && isDoubleColumnShelf);
        }
    }
}
