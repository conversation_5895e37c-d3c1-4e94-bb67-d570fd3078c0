package com.sankuai.dzviewscene.product.enums;

/**
 * <AUTHOR>
 * @date 2023/7/19 6:16 下午
 */

public enum ExtContextEnum {
    SHOP_INFO(0, "门店信息"),
    DP_SHOP_ID(1, "点评门店"),
    MT_SHOP_ID(2, "美团门店"),
    DP_CITY_ID(3,  "点评城市"),
    MT_CITY_ID(4, "美团城市"),
    SHOP_DP_CITY_ID(5, "点评门店所在城市"),
    SHOP_MT_CITY_ID(6, "美团门店所在城市"),
    VIRTUAL_USER_ID(7, "虚id"),
    DP_TOP_CITY_ID(8,  "点评顶级城市"),
    MT_TOP_CITY_ID(9, "美团顶级城市"),
    GENERAL_PRODUCT_SHOP_ID(10, "通过商品ID获取兜底门店ID"),
    DP_SHOP_DISTRICT_ID(11,  "点评门店所在城市区域"),
    MT_SHOP_DISTRICT_ID(12, "美团门店所在城市区域"),
    DEAl_NEAREST_SHOP_ID(13, "通过团单获取最近门店"),
    MT_USER_ID(14, "美团用户ID"),
    SHOP_BACK_CATS(15, "门店后台类目集合");

    private final int type;
    private final String desc;


    ExtContextEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
