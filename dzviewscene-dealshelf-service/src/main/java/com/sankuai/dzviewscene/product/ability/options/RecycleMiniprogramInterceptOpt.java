package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import lombok.Data;

import java.util.List;

@VPointOption(name = "回收小程序校验拦截",
        description = "白名单商户需要拦截免费服务货架",
        code = RecycleMiniprogramInterceptOpt.CODE)
public class RecycleMiniprogramInterceptOpt extends InterceptHandlerVP<RecycleMiniprogramInterceptOpt.Config> {

    public static final String CODE = "RecycleMiniprogramInterceptOpt";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        // 是白名单商户则进行拦截
        return context.getParam(RecycleMiniprogramFetcherOpt.CODE);
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
