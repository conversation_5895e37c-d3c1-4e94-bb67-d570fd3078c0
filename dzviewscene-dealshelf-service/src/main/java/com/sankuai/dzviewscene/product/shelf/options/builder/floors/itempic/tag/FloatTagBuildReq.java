package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.ActivityTagCfg;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
@Data
public class FloatTagBuildReq {

    private ProductM productM;
    
    /**
     * {@link ShelfShowTypeEnum}
     */
    private int shelfShowType;

    private ActivityTagCfg activityTagCfg;

    private int platform;

    private long filterId;

    private int index;

    private ActivityCxt context;

    public FloatTagBuildReq() {
    }

    public FloatTagBuildReq(ProductM productM) {
        this.productM = productM;
    }
}
