package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

@Data
public class PriceBottomTagBuildReq {

    private ProductM productM;

    private CardM cardM;

    private int platform;

    private String salePrice;

    private PriceBottomTagBuildCfg cfg;

    private ActivityCxt context;

    private boolean hiddenPreText;
}
