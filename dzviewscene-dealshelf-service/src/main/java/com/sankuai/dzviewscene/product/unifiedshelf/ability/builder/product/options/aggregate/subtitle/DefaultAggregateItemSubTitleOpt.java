package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.subtitle;


import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.SubTitleJoinTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.MassageBookUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.util.Collections;
import java.util.Collections;
import java.util.List;

@VPointOption(name = "默认-聚合卡片副标题",
        description = "聚合卡片副标题",
        code = DefaultAggregateItemSubTitleOpt.CODE,
        isDefault = true)
public class DefaultAggregateItemSubTitleOpt extends AggregateItemSubTitleVP<DefaultAggregateItemSubTitleOpt.Config> {

    public static final String CODE = "DefaultAggregateItemSubTitleOpt";

    private static final String SPU_TEXT_FORMAT = "可选%d个团购";

    private static final String DEFAULT_TEXT_FORMAT = "可选%d个套餐";

    private static final String SPU_EARLIEST_RESERVE_TEXT_FORMAT = "最早可订%s";

    @Override
    public ItemSubTitleVO compute(ActivityCxt activityCxt, Param param, Config config) {
        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        if (ProductHierarchyUtils.isSpuNode(param.getNodeM())) {
            // SPU 父商品
            if (ActivityCtxtUtils.checkMultiState(activityCxt)) {
                List<String> productTags = Lists.newArrayList(String.format(SPU_TEXT_FORMAT, param.getNodeM().getChildren().size()));
                // 一品多态逻辑，新增最早可订时间
                String earliestChildrenBookingTimeStr = getEarliestChildrenBookingTimeStr(param, config);
                if (StringUtils.isNotBlank(earliestChildrenBookingTimeStr)) {
                    productTags.add(earliestChildrenBookingTimeStr);
                }
                List<String> styles = Collections.nCopies(productTags.size(), TextStyleEnum.TEXT_GRAY_BACKGROUND.getType());
                return UnifiedShelfItemSubTitleVP.build4CustomStyle(productTags, styles, SubTitleJoinTypeEnum.SPACE.getType());
            }else {
                if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())) {
                    List<String> productTags = Lists.newArrayList(String.format(SPU_TEXT_FORMAT, param.getNodeM().getChildren().size()));
                    List<String> styles = Collections.nCopies(productTags.size(), TextStyleEnum.TEXT_GRAY_BACKGROUND.getType());
                    return UnifiedShelfItemSubTitleVP.build4CustomStyle(productTags, styles, config.getNewJoinType());
                }
                // 保留老逻辑
                return UnifiedShelfItemSubTitleVP.build4Default(Lists.newArrayList(String.format(SPU_TEXT_FORMAT, param.getNodeM().getChildren().size())));
            }
        }
        List<String> productTags = Lists.newArrayList();
        String format = StringUtils.isBlank(config.getTextFormat()) ? DEFAULT_TEXT_FORMAT : config.getTextFormat();
        productTags.add(String.format(format, param.getNodeM().getChildren().size()));
        //扩展副标题标签
        List<String> extProductTags = getExtProductTags(param.getNodeM(), config);
        if (CollectionUtils.isNotEmpty(extProductTags)) {
            productTags.addAll(extProductTags);
        }
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())) {
            List<String> styles = Collections.nCopies(productTags.size(), TextStyleEnum.TEXT_GRAY_BACKGROUND.getType());
            return UnifiedShelfItemSubTitleVP.build4CustomStyle(productTags, styles, config.getNewJoinType());
        }
        return UnifiedShelfItemSubTitleVP.build4Default(productTags);
    }

    /**
     * 获取spu所有子商卡中预订时间最早的productM该展示的副标题文本
     */
    private String getEarliestChildrenBookingTimeStr(Param param, Config config) {
        List<ProductHierarchyNodeM> childs = param.getNodeM().getChildren();
        if (CollectionUtils.isEmpty(childs)) {
            return null;
        }
        ProductM earliestProductM = null;
        LocalTime earliestTime = null;
        for (ProductHierarchyNodeM child : childs) {
            if (child == null) {
                continue;
            }
            ProductM productM = child.getProductM();
            if (productM == null) {
                continue;
            }
            LocalTime curTime = MassageBookUtils.getProductEarliestBookingTime(productM);
            if (curTime == null) {
                continue;
            }
            if (earliestTime == null || curTime.isBefore(earliestTime)) {
                earliestTime = curTime;
                earliestProductM = productM;
            }
        }
        return earliestProductM != null ? MassageBookUtils.getProductEarliestBookingTimeStr(earliestProductM) : null;
    }

    private List<String> getExtProductTags(ProductHierarchyNodeM nodeM, Config config) {
        ProductM productM = nodeM.getProductM();
        if (productM == null) {
            return Lists.newArrayList();
        }
        return productM.getProductTags();
    }

    @VPointCfg
    @Data
    public static class Config {
        private String textFormat;

        /**
         * 足疗新标签样式+有大促调整+副标题信息调整方案
         * 新标签样式+无大促调整+副标题信息调整方案
         */
        private List<String> massageTagExpSks;

        private int newJoinType=4;

    }
}
