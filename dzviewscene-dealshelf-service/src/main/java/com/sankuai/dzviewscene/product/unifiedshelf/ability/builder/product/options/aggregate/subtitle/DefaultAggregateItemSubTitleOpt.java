package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.subtitle;


import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "默认-聚合卡片副标题",
        description = "聚合卡片副标题",
        code = DefaultAggregateItemSubTitleOpt.CODE,
        isDefault = true)
public class DefaultAggregateItemSubTitleOpt extends AggregateItemSubTitleVP<DefaultAggregateItemSubTitleOpt.Config> {

    public static final String CODE = "DefaultAggregateItemSubTitleOpt";

    private static final String SPU_TEXT_FORMAT = "可选%d个团购";

    private static final String DEFAULT_TEXT_FORMAT = "可选%d个套餐";

    @Override
    public ItemSubTitleVO compute(ActivityCxt activityCxt, Param param, Config config) {
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            return UnifiedShelfItemSubTitleVP.build4Default(Lists.newArrayList(String.format(SPU_TEXT_FORMAT, param.getNodeM().getChildren().size())));
        }
        List<String> productTags = Lists.newArrayList();
        String format = StringUtils.isBlank(config.getTextFormat()) ? DEFAULT_TEXT_FORMAT : config.getTextFormat();
        productTags.add(String.format(format, param.getNodeM().getChildren().size()));
        //扩展副标题标签
        List<String> extProductTags = getExtProductTags(param.getNodeM(), param.getProductMMap(), config);
        if(CollectionUtils.isNotEmpty(extProductTags)){
            productTags.addAll(extProductTags);
        }
        return UnifiedShelfItemSubTitleVP.build4Default(productTags);
    }

    private List<String> getExtProductTags(ProductHierarchyNodeM nodeM, Map<String, ProductM> productMProductM, Config config) {
        ProductM productM = productMProductM.get(nodeM.getIdentityKey());
        if(productM == null) {
            return Lists.newArrayList();
        }
        return productM.getProductTags();
    }

    @VPointCfg
    @Data
    public static class Config {

        private String textFormat;
    }
}
