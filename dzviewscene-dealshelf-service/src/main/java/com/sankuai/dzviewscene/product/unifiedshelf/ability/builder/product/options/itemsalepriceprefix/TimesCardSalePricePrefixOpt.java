package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

@VPointOption(name = "次卡商品价格前缀",
        description = "次卡商品价格前缀，泛商品返回共x次，团购次卡返回单次",
        code = TimesCardSalePricePrefixOpt.CODE)
public class TimesCardSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<Void> {

    public static final String CODE = "TimesCardSalePricePrefixOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Void config) {
        //泛商品次卡
        if (param.getProductM().getProductType() == ProductTypeEnum.TIME_CARD.getType()) {
            String timesStr = ProductMAttrUtils.getAttrValue(param.getProductM(), TimesDealUtil.TIMES_CARD_TIMES);
            int times = NumberUtils.toInt(timesStr, 0);
            if (times <= 0) {
                return null;
            }
            return String.format("共%s次", times);
        }
        //团购次卡
        if(param.getProductM().isTimesDeal() && !TimesDealUtil.isContinuousMonthly(param.getProductM())){
            return "单次";
        }
        // 次卡优先级>神券价
        String pricePrefix = getPricePrefix(context, param);
        if (StringUtils.isNotBlank(pricePrefix)) {
            return pricePrefix;
        }
        return null;
    }
}