/*
 * Create Author : liyanmin
 * Create Date : 2024-09-11
 * Project :
 * File Name : UnifiedProductAreaBuilder.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.CommonProductTagUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.*;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.*;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.ItemShowTypeEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.FilterUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.framework.monitor.FloorItemsMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder.Attachments.groupedProducts;

/**
 * 功能描述:
 * <p>
 *
 * <AUTHOR> yanmin.li
 * <p>
 * @version 1.0 2024-09-11
 * @since dzviewscene-dealshelf-home 1.0
 */
@Ability(
        code = UnifiedProductAreaBuilder.CODE, name = "VO-商品区构造能力", description = "商品区构造能力",
        activities = {UnifiedShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE, CardFetcher.CODE, ProductActivitiesFetcher.CODE}
)
@Slf4j
public class UnifiedProductAreaBuilder extends
        PmfAbility<List<ShelfFilterProductAreaVO>, UnifiedProductAreaBuilder.Request, UnifiedProductAreaBuilder.Config> {

    public static final String CODE = "UnifiedProductAreaBuilder";

    public static final String PRICECIPHER_PARAM = "pricecipher=";

    private static final int DEFAULT_SHOW_NUM = 4;

    @Resource
    private FloorItemsMonitor floorItemsMonitor;

    @Override
    public CompletableFuture<List<ShelfFilterProductAreaVO>> build(ActivityCxt ctx, Request request, Config config) {
        // 1. 查询主数据对象
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null || ModelUtils.hasNoProducts(shelfGroupM)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        // 2. 获取商品组
        if (CollectionUtils.isEmpty(request.getGroupNames())) {
            throw new BusinessException(String.format("构造货架楼层区, groupNames参数没传"));
        }
        // 3. 根据商品组列表顺序构造ShelfFilterProductAreaVO
        return CompletableFuture.completedFuture(
                buildShelfFilterProductAreaVOs(ctx, request.getGroupNames(), shelfGroupM, request, config));
    }

    private List<ShelfFilterProductAreaVO> buildShelfFilterProductAreaVOs(ActivityCxt ctx,
                                                                          List<String> productGroupNames, ShelfGroupM shelfGroupM, UnifiedProductAreaBuilder.Request request,
                                                                          UnifiedProductAreaBuilder.Config config) {
        if (ModelUtils.hasNoProducts(shelfGroupM)) {
            return Lists.newArrayList();
        }
        Map<String, FilterM> filters = shelfGroupM.getFilterMs() == null ? new HashMap<>() : shelfGroupM.getFilterMs();
        Map<String, ProductGroupM> productGroups = shelfGroupM.getProductGroupMs() == null ? new HashMap<>()
                : shelfGroupM.getProductGroupMs();
        // 一组筛选 + 多组商品（当前实现为单筛选多组商品，无多筛选的诉求）
        boolean isMultiItemAreaShelf = productGroupNames.size() > 1;
        if (isMultiItemAreaShelf) {
            return Lists.newArrayList(buildSingleFilterMultiItemArea(ctx, productGroupNames, productGroups, filters, request, config));
        }
        // 一组筛选 + 一组商品
        // 先只考虑单组商品
        String groupName = productGroupNames.get(0);
        return buildSingleFloorShelfArea(ctx, groupName, filters.get(groupName), productGroups.get(groupName), request, config);
    }

    private ShelfFilterProductAreaVO buildSingleFilterMultiItemArea(ActivityCxt ctx, List<String> productGroupNames,
                                                                    Map<String, ProductGroupM> productGroups, Map<String, FilterM> filters, Request request, Config config) {
        List<ShelfProductAreaVO> productAreaList = new ArrayList<>(productGroupNames.size());
        // 取第一个有值的 FilterM
        FilterM filterM = MapUtils.isNotEmpty(filters)
                ? filters.values().stream().filter(Objects::nonNull).findFirst().orElse(null) : null;
        for (String groupName : productGroupNames) {
            ProductGroupM currentProductGroup = productGroups.get(groupName);
            List<ProductM> productLists = getNotNullProductLists(currentProductGroup);
            // 对预填充团单进行到手价的赋值
            batchSetPreProductSalePrice(ctx, currentProductGroup);
            // 对当前tab下的商品进行到手价的赋值
            PriceDisplayUtils.batchSetSalePrice(ctx, productLists);
            // 构造商品区
            ShelfProductAreaVO productArea = buildProductAreaVO(ctx, groupName,
                    productLists, request.getSelectedFilterId(), request,
                    config, filterM, currentProductGroup);
            if (productArea != null) {
                productAreaList.add(productArea);
            }
        }
        ShelfFilterProductAreaVO filterProductAreaVO = new ShelfFilterProductAreaVO();
        filterProductAreaVO.setFilterId(String.valueOf(request.getSelectedFilterId()));
        filterProductAreaVO.setProductAreas(productAreaList);
        return filterProductAreaVO;
    }

    private void batchSetPreProductSalePrice(ActivityCxt ctx, ProductGroupM productGroupM) {
        if (productGroupM == null || MapUtils.isEmpty(productGroupM.getPreLoadProducts())) {
            return;
        }
        PriceDisplayUtils.batchSetSalePrice(ctx,
                productGroupM.getPreLoadProducts().get(ProductTypeEnum.DEAL.getType()));
    }

    /**
     * 返回非Null的结果
     *
     * @param currentFloorM
     * @return
     */
    private List<ProductM> getNotNullProductLists(ProductGroupM currentFloorM) {
        return currentFloorM == null || CollectionUtils.isEmpty(currentFloorM.getProducts()) ? new ArrayList<>()
                : currentFloorM.getProducts();
    }

    // 注: 单层货架模式, 有处理分组逻辑, ShelfFilterProductAreaVO, 每个ShelfFilterProductAreaVO一个ProductAreaComponentVO对象
    private List<ShelfFilterProductAreaVO> buildSingleFloorShelfArea(ActivityCxt ctx, String groupName, FilterM filterM,
                                                                     ProductGroupM currentFloorM, Request request, Config config) {
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(ctx);
        // 上下文已有分好组的商品
        Map<Long, List<ProductM>> filterId2Products = groupByFilter(activityContext, groupName, filterM, currentFloorM, config);
        // 无分组逻辑处理
        if (MapUtils.isEmpty(filterId2Products)) {
            filterId2Products = new HashMap<>();
            filterId2Products.put(request.getSelectedFilterId(), getNotNullProductLists(currentFloorM));
        }
        // 3. 构造货架商品区
        return filterId2Products.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .map(productsEntry -> buildFilterBtnIdAndProAreasVO(ctx, groupName, filterM, productsEntry.getKey(), productsEntry.getValue(), request, config, currentFloorM))
                .collect(Collectors.toList());
    }

    private Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM, ProductGroupM productGroupM, Config config) {
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return null;
        }
        CompletableFuture<Map<String, Map<Long, List<ProductM>>>> group2GroupsCompletableFuture = activityContext.getAttachment(groupedProducts);
        if (group2GroupsCompletableFuture == null) {
            return null;
        }
        Map<String, Map<Long, List<ProductM>>> group2Groups = group2GroupsCompletableFuture.join();
        Map<Long, List<ProductM>> groupedProducts = group2Groups.get(groupName);
        if (config.isGroupTotalProductByFilter()) {
            return groupTotalProductByFilter(groupedProducts, productGroupM, config);
        }
        if (MapUtils.isNotEmpty(groupedProducts)) {
            return groupedProducts;
        }
        return null;
    }

    private Map<Long, List<ProductM>> groupTotalProductByFilter(Map<Long, List<ProductM>> groupedProducts, ProductGroupM productGroupM, Config config) {
        if (MapUtils.isEmpty(groupedProducts)) {
            return groupedProducts;
        }
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return new HashMap<>();
        }
        Map<Long, List<ProductM>> result = new HashMap<>();
        for (Long filterId : groupedProducts.keySet()) {
            if (CollectionUtils.isEmpty(groupedProducts.get(filterId))) {
                continue;
            }
            result.put(filterId, readProductFromProductGroup(productGroupM.getProducts(), groupedProducts.get(filterId), config));
        }
        return result;
    }

    private List<ProductM> readProductFromProductGroup(List<ProductM> paddingProducts, List<ProductM> filterProducts, Config config) {
        return filterProducts.stream()
                .map(filterProduct -> getPaddingProductM(filterProduct, paddingProducts, config))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ProductM getPaddingProductM(ProductM filterProduct, List<ProductM> paddingProducts, Config config) {
        ProductM paddingProductM = findProductByIdAndType(filterProduct, paddingProducts);
        if (paddingProductM == null) {
            return null;
        }
        ProductM result = new ProductM();
        BeanUtils.copyProperties(paddingProductM, result);
        mergeAttr(filterProduct, result, config);
        return result;
    }

    private void mergeAttr(ProductM filterProduct, ProductM paddingProductM, Config config) {
        if (CollectionUtils.isEmpty(config.getMergeProductAttrFromQuery()) || CollectionUtils.isEmpty(filterProduct.getExtAttrs())) {
            return;
        }
        //之前是浅拷贝，attr对象被重写之后会整个被修改。新需求需要分组间属性隔离，统一修改会影响未知线上场景，这里加个配置控制
        if (config.mergeProductAttrIsFilterIsolate) {
            paddingProductM.setExtAttrs(deepCopyAttrMs(paddingProductM.getExtAttrs()));
        }
        for (String attrKey : config.getMergeProductAttrFromQuery()) {
            String attrValue = filterProduct.getAttr(attrKey);
            if (attrValue == null) {
                continue;
            }
            mergeProductAttr(paddingProductM, attrKey, attrValue);
        }
    }

    private List<AttrM> deepCopyAttrMs(List<AttrM> sourceAttrMs) {
        List<AttrM> newAttrMs = sourceAttrMs.stream().filter(Objects::nonNull)
                .map(item -> new AttrM(item.getName(), item.getValue())).collect(Collectors.toList());
        return newAttrMs;
    }

    private void mergeProductAttr(ProductM productM, String attrKey, String attrValue) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList(new AttrM(attrKey, attrValue)));
            return;
        }
        for (AttrM extAttr : productM.getExtAttrs()) {
            if (!StringUtils.equals(extAttr.getName(), attrKey)) {
                continue;
            }
            // 找到后用直接覆盖，然后返回
            extAttr.setValue(attrValue);
            return;
        }
        productM.getExtAttrs().add(new AttrM(attrKey, attrValue));
    }

    /**
     * 根据召回的productM找到主题渲染后的productM
     *
     * @param queryProduct    召回拿到的productM
     * @param paddingProducts 主题返回的productM集合
     * @return 找到的productM
     */
    private ProductM findProductByIdAndType(ProductM queryProduct, List<ProductM> paddingProducts) {
        return paddingProducts.stream()
                .filter(productM -> productM.getActProductId() == queryProduct.getActProductId()
                        && productM.getActProductType() == queryProduct.getActProductType())
                .findFirst().orElse(null);
    }

    private ShelfFilterProductAreaVO buildFilterBtnIdAndProAreasVO(ActivityCxt ctx, String groupName, FilterM filterM,
                                                                   long filterId, List<ProductM> currentProductMs, Request request, Config config, ProductGroupM currentFloorM) {
        ShelfProductAreaVO productAreaVO = buildProductAreaVO(ctx, groupName, currentProductMs, filterId, request, config, filterM, currentFloorM);
        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        shelfFilterProductAreaVO.setFilterId(String.valueOf(filterId));
        shelfFilterProductAreaVO.setProductAreas(productAreaVO == null ? Lists.newArrayList() : Lists.newArrayList(productAreaVO));
        return shelfFilterProductAreaVO;
    }

    ///////////////////////////////////////////////以下是构造货架楼层区逻辑///////////////////////////////////////////////
    private ShelfProductAreaVO buildProductAreaVO(ActivityCxt activityCxt, String groupName, List<ProductM> currentFloorProductMs,
                                                  long filterId, Request request, Config config, FilterM filterM, ProductGroupM currentFloorM) {
        if (CollectionUtils.isEmpty(currentFloorProductMs)) {
            return null;
        }
        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        //构造前置处理
        preBuildHandler(activityCxt, currentFloorProductMs);
        // 1. 构造商品区
        shelfProductAreaVO.setItems(buildShelfItemVOs(activityCxt, currentFloorProductMs, filterId, request, config, currentFloorM));
        // 2. 默认展示数
        shelfProductAreaVO.setDefaultShowNum(buildDefaultShowNum(activityCxt, currentFloorProductMs, shelfProductAreaVO.getItems(), filterId, groupName));
        // 3. 更多文案
        shelfProductAreaVO.setMoreText(moreText(activityCxt, shelfProductAreaVO, currentFloorProductMs, filterId, currentFloorM));
        // 4. 更多跳转链接
        shelfProductAreaVO.setMoreJumpUrl(moreJumpUrl(activityCxt, shelfProductAreaVO, request, groupName, filterId, filterM));
        // 5. 商品区展示样式
        shelfProductAreaVO.setShowType(buildProductAreaShowType(activityCxt, groupName));
        // 6. 是否有下一页
        shelfProductAreaVO.setHasNext(buildHasNext(activityCxt, currentFloorM));
        // 6. 根据defaultNum截断
        limitByDefaultShowNumIfHasJumpUrl(shelfProductAreaVO);
        // 构造后置处理
        postBuildHandler(activityCxt, shelfProductAreaVO, currentFloorProductMs);
        return shelfProductAreaVO;
    }

    /**
     * 构造前置处理
     * @param activityCxt
     * @param currentFloorProductMs
     */
    private void preBuildHandler(ActivityCxt activityCxt, List<ProductM> currentFloorProductMs) {
        try {
            ProductAreaPreBuildHandlerVP<?> productAreaPreBuildHandlerVP = findVPoint(activityCxt, ProductAreaPreBuildHandlerVP.CODE);
            productAreaPreBuildHandlerVP.execute(activityCxt,
                    ProductAreaPreBuildHandlerVP.Param.builder()
                            .products(currentFloorProductMs)
                            .build());
        } catch (Exception e) {
            log.error("UnifiedProductAreaBuilder#preBuildHandler.error",e);
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private void limitByDefaultShowNumIfHasJumpUrl(ShelfProductAreaVO shelfProductAreaVO) {
        if (moreJumpUrlIsBlank(shelfProductAreaVO)) {
            return;
        }
        int defaultShowNum = shelfProductAreaVO.getDefaultShowNum();
        if (shelfProductAreaVO.getItems().size() <= defaultShowNum) {
            return;
        }
        shelfProductAreaVO.setItems(shelfProductAreaVO.getItems().subList(0, defaultShowNum));
    }

    private boolean moreJumpUrlIsBlank(ShelfProductAreaVO shelfProductAreaVO) {
        return shelfProductAreaVO == null
                || CollectionUtils.isEmpty(shelfProductAreaVO.getItems())
                || StringUtils.isEmpty(shelfProductAreaVO.getMoreJumpUrl());
    }

    /**
     * 默认展示数
     */
    private int buildDefaultShowNum(ActivityCxt activityCxt, List<ProductM> currentFloorProductMs, List<ShelfItemVO> shelfItemVOS, long filterId, String groupName) {
        try {
            ProductAreaDefaultShowNumVP<?> productAreaDefaultShowNumVP = findVPoint(activityCxt, ProductAreaDefaultShowNumVP.CODE);
            Integer showNum = productAreaDefaultShowNumVP.execute(activityCxt,
                    ProductAreaDefaultShowNumVP.Param.builder()
                            .products(currentFloorProductMs)
                            .shelfItemVOS(shelfItemVOS)
                            .douHuList(activityCxt.getSource(ShelfDouHuFetcher.CODE))
                            .filterId(filterId)
                            .groupName(groupName)
                            .build());
            return showNum == null ? DEFAULT_SHOW_NUM : showNum;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return DEFAULT_SHOW_NUM;
        }
    }

    /**
     * 查看更多-跳转链接
     */
    private String moreJumpUrl(ActivityCxt activityCxt, ShelfProductAreaVO shelfProductAreaVO, Request sourceReq, String groupName, long filterId, FilterM filterM) {
        try {
            ProductAreaMoreJumpUrlVP<?> moreUrlVP = findVPoint(activityCxt, ProductAreaMoreJumpUrlVP.CODE);
            int itemAreaItemCnt = CollectionUtils.isEmpty(shelfProductAreaVO.getItems()) ? 0 : shelfProductAreaVO.getItems().size();
            return moreUrlVP.execute(activityCxt,
                    ProductAreaMoreJumpUrlVP.Param.builder()
                            .dpPoiIdL(sourceReq.getDpPoiIdL()).mtPoiIdL(sourceReq.getMtPoiIdL())
                            .platform(sourceReq.getPlatform()).filterId(filterId)
                            .filterParentId(Objects.nonNull(filterM) ? FilterUtils.findFilterParentId(filterM.getFilters(), filterId) : 0L)
                            .shopUuid(sourceReq.getShopUuid()).userAgent(sourceReq.getUserAgent())
                            .groupName(groupName)
                            .cityId(PlatformUtil.isMT(sourceReq.getPlatform()) ? sourceReq.getMtCityId() : sourceReq.getDpCityId())
                            .locationCityId(activityCxt.getParam(ShelfActivityConstants.Params.locationCityId))
                            .lat(activityCxt.getParam(ShelfActivityConstants.Params.lat))
                            .lng(activityCxt.getParam(ShelfActivityConstants.Params.lng))
                            .itemAreaItemCnt(itemAreaItemCnt)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 查看更多-文案
     */
    private String moreText(ActivityCxt activityCxt, ShelfProductAreaVO shelfProductAreaVO, List<ProductM> currentFloorProductMs, long filterId, ProductGroupM currentFloorM) {
        try {
            ProductAreaMoreTextVP<?> moreTextVP = findVPoint(activityCxt, ProductAreaMoreTextVP.CODE);
            return moreTextVP.execute(activityCxt,
                    ProductAreaMoreTextVP.Param.builder()
                            .itemAreaItemCnt(calItemCnt(shelfProductAreaVO))
                            .defaultShowNum(calRealShowNum(shelfProductAreaVO))
                            .shelfProductAreaVO(shelfProductAreaVO)
                            .floorProductM(currentFloorProductMs)
                            .totalCount(currentFloorM.getTotal())
                            .filterId(filterId)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private int calItemCnt(ShelfProductAreaVO shelfProductAreaVO) {
        if(CollectionUtils.isEmpty(shelfProductAreaVO.getItems())){
            return 0;
        }
        // 遍历所有的 ShelfItemVO，统计全部叶子项
        return shelfProductAreaVO.getItems().stream()
                .mapToInt(item -> CollectionUtils.isNotEmpty(item.getSubItems()) ? item.getSubItems().size() : 1)
                .sum();
    }

    /**
     * 子项截断则全部展示，计算实际默认展示数
     * @param shelfProductAreaVO
     * @return
     */
    private int calRealShowNum(ShelfProductAreaVO shelfProductAreaVO) {
        int showNum = 0;
        int realNum = 0;
        for (ShelfItemVO shelfItemVO : shelfProductAreaVO.getItems()) {
            if(CollectionUtils.isNotEmpty(shelfItemVO.getSubItems())){
                showNum += shelfItemVO.getDefaultShowNum() > 0 ? Math.min(shelfItemVO.getSubItems().size(),
                        shelfItemVO.getDefaultShowNum()) : shelfItemVO.getSubItems().size();
                realNum += shelfItemVO.getSubItems().size();
            }else{
                showNum ++;
                realNum ++;
            }
            if (showNum >= shelfProductAreaVO.getDefaultShowNum()) {
                break;
            }
        }
        return realNum;
    }

    /**
     * 商品区样式
     */
    private int buildProductAreaShowType(ActivityCxt activityCxt, String groupName) {
        try {
            ProductAreaShowTypeVP<?> productAreaShowTypeVP = findVPoint(activityCxt, ProductAreaShowTypeVP.CODE);
            return productAreaShowTypeVP.execute(activityCxt,
                    ProductAreaShowTypeVP.Param.builder()
                            .groupName(groupName)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return 0;
        }
    }

    private boolean buildHasNext(ActivityCxt activityCxt, ProductGroupM productGroupM) {
        //分页情况加上是否有下一页
        if (productGroupM == null || !ParamsUtil.judgeDealShelfHasPage(activityCxt)) {
            return false;
        }
        return productGroupM.isHasNext();
    }

    private List<ShelfItemVO> buildShelfItemVOs(ActivityCxt activityCxt, List<ProductM> productMs, long filterId, Request request,
                                                Config config, ProductGroupM currentFloorM) {
        if (CollectionUtils.isEmpty(productMs)) {
            return Lists.newArrayList();
        }
        // 普通商品列表
        if (!config.isUseProductHierarchy() || currentFloorM.getProductHierarchyRoot() == null
                || CollectionUtils.isEmpty(currentFloorM.getProductHierarchyRoot().getChildren())) {
            return buildNormalShelfItems(activityCxt, productMs, filterId, request, config);
        }
        // 层级商品列表
        return buildHierarchyShelfItems(activityCxt, productMs, currentFloorM.getProductHierarchyRoot(), filterId, request, config);
    }

    /**
     * 构建层级商品列表
     */
    private List<ShelfItemVO> buildHierarchyShelfItems(ActivityCxt activityCxt, List<ProductM> productMs,
                                                       ProductHierarchyNodeM hierarchy, long filterId,
                                                       Request request, Config config) {
      //  Map<String, ProductM> productMMap = buildProductMap(productMs);
        //根据全局节点id将ProductM关联到节点上
        bindNodeProductM(hierarchy, productMs);
        AtomicInteger index = new AtomicInteger();
        return hierarchy.getChildren().stream()
                .map(nodeM -> buildHierarchyNode(activityCxt, nodeM, filterId, request, config, index))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建层级节点
     */
    private ShelfItemVO buildHierarchyNode(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, long filterId,
                                           Request request, Config config, AtomicInteger index) {
        // 普通商品节点
        if (CollectionUtils.isEmpty(nodeM.getChildren())) {
            return buildNormalNode(activityCxt, nodeM, filterId, request, config, index);
        }
        // 聚合商品节点
        return buildAggregateNode(activityCxt, nodeM, filterId, request, config, index);
    }

    /**
     * 构建普通节点
     */
    private ShelfItemVO buildNormalNode(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, long filterId,
                                        Request request, Config config, AtomicInteger indexAtomic) {
        return buildShelfItemVO(activityCxt, nodeM, nodeM.getProductM(), filterId, request, config, indexAtomic);
    }

    /**
     * 构建聚合节点
     */
    private ShelfItemVO buildAggregateNode(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, long filterId,
                                           Request request, Config config, AtomicInteger index) {
        // 构建子项商品卡片列表
        List<ShelfItemVO> childItems = nodeM.getChildren().stream()
                .map(childNode -> buildChildShelfItemVO(activityCxt, childNode, filterId, request, config, index))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(childItems)){
            return buildNormalNode(activityCxt, nodeM, filterId, request, config, index);
        }
        // 构建父项商品卡片
        ShelfItemVO parentItem = buildAggrateShelfItemVO(activityCxt, nodeM, childItems, filterId, request);
        // 聚合后置处理
        return aggregatePostHandleVP(activityCxt, nodeM, parentItem);
    }

    /**
     * 构建聚合商品卡片（父节点）
     */
    private ShelfItemVO buildAggrateShelfItemVO(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, List<ShelfItemVO> childItems,
                                                long filterId, Request request) {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setItemId(nodeM.getProductId());
        shelfItemVO.setItemType(nodeM.getProductType());
        shelfItemVO.setSubItems(childItems);
        shelfItemVO.setTitle(itemAggregateTitle(activityCxt, nodeM));
        shelfItemVO.setHeadPic(itemAggregatePicArea(activityCxt, nodeM, filterId));
        shelfItemVO.setProductTags(itemAggregateSubTitle(activityCxt, nodeM));
        shelfItemVO.setButtonCarouselMsg(itemAggregateCarouselMsg(activityCxt, nodeM));
        shelfItemVO.setSpecialTags(itemAggregateSpecialTag(activityCxt, nodeM));
        //spu无对应ProductM，虚拟一个对象，避免打点构造npe
        ProductM productM = nodeM.getProductM() != null ? nodeM.getProductM() : new ProductM();
        shelfItemVO.setLabs(itemOceanLabs(shelfItemVO, activityCxt, nodeM, productM, activityCxt.getSource(CardFetcher.CODE), filterId, request));
        shelfItemVO.setAvailable(true);
        //这里类型先用固定值，后续有变化再做变化点
        shelfItemVO.setShowType(ItemShowTypeEnum.AGGREGATE_CARD.getType());
        //聚合卡片价格信息，用一个vp处理为了减少vp数量
        aggregateItemPriceVP(activityCxt, nodeM, shelfItemVO);
        //聚合卡片子项折叠
        aggregateItemChildFold(activityCxt, nodeM, shelfItemVO);
        return shelfItemVO;
    }

    /**
     * 构建子商品卡片
     */
    private ShelfItemVO buildChildShelfItemVO(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM,
                                              long filterId, Request request, Config config, AtomicInteger index) {
        if(nodeM.getProductType() == ProductTypeEnum.SKU.getType()){
            //sku用父项dealgroup构造
            ProductM productM = buildProductSkuM(nodeM);
            return buildShelfItemVO(activityCxt, nodeM, productM, filterId, request, config, index);
        }else{
            ProductM productM = nodeM.getProductM();
            return buildShelfItemVO(activityCxt, nodeM, productM, filterId, request, config, index);
        }
    }

    private ProductM buildProductSkuM(ProductHierarchyNodeM nodeM){
        ProductM productM = nodeM.getParent().getProductM();
        if(productM == null || CollectionUtils.isEmpty(productM.getProductSkuList())){
            return null;
        }
        ProductSkuM productSkuM = productM.getProductSkuList().stream()
                .filter(sku -> sku.getSkuId() == nodeM.getProductId())
                .findFirst()
                .orElse(null);
        if(productSkuM == null){
            return null;
        }
        //将sku信息设置到productM中，方便后续构造
        ProductM productMSku = new ProductM();
        BeanUtils.copyProperties(productM, productMSku);
        productMSku.setPromoPrices(productSkuM.getPromoPrices());
        productMSku.setMarketPrice(productSkuM.getMarketPrice());
        if(StringUtils.isNotEmpty(productSkuM.getSkuName())){
            productMSku.setTitle(productSkuM.getSkuName());
        }
        if(StringUtils.isNotEmpty(productSkuM.getSalePrice())){
            productMSku.setBasePrice(new BigDecimal(productSkuM.getSalePrice()));
        }
        if(StringUtils.isNotEmpty(productSkuM.getJumpUrl())){
            productMSku.setJumpUrl(productSkuM.getJumpUrl());
        }
        //无sku维度销量先置为空
        productMSku.setSale(null);
        productMSku.setPurchase(null);
        return productMSku;
    }

    private void bindNodeProductM(ProductHierarchyNodeM hierarchy, List<ProductM> productMs){
        Map<Integer, ProductM> nodeId2ProductM = productMs.stream().collect(Collectors.toMap(ProductM::getNodeOrderId,
                Function.identity(), (p1, p2) -> p1));
        fillNodeProductM(nodeId2ProductM, hierarchy);
    }

    private void fillNodeProductM(Map<Integer, ProductM> nodeId2ProductM, ProductHierarchyNodeM hierarchy){
        if(CollectionUtils.isEmpty(hierarchy.getChildren())){
            return;
        }
        for(ProductHierarchyNodeM node : hierarchy.getChildren()){
            node.setProductM(nodeId2ProductM.get(node.getOrderId()));
            fillNodeProductM(nodeId2ProductM, node);
        }
    }

    /**
     * 构建商品标志到商品的映射
     */
    private Map<String, ProductM> buildProductMap(List<ProductM> productMs) {
        return productMs.stream()
                .collect(Collectors.toMap(
                        productM -> ProductHierarchyNodeM.identityKey(productM.getProductType(), productM.getProductId()),
                        Function.identity(),
                        (p1, p2) -> p1
                ));
    }

    /**
     * 构建普通商品列表
     */
    private List<ShelfItemVO> buildNormalShelfItems(ActivityCxt activityCxt, List<ProductM> productMs,
                                                    long filterId, Request request, Config config) {
        AtomicInteger index = new AtomicInteger();
        return productMs.stream()
                .filter(Objects::nonNull)
                .map(productM -> buildShelfItemVO(activityCxt, null, productM, filterId, request, config, index))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ShelfItemVO buildShelfItemVO(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, ProductM productM, long filterId,
                                         Request request, Config config, AtomicInteger indexAtomic) {
        try {
            if(productM == null){
                return null;
            }
            int index = indexAtomic.getAndIncrement();
            int platform = request.getPlatform();
            CardM cardM = activityCxt.getSource(CardFetcher.CODE);
            //计算最优promoPrice
            productM.setBestPromoPrice(PriceUtils.getUserHasPromoPrice(productM, cardM));
            //实际到手价
            String salePrice = itemSalePrice(activityCxt, productM, cardM);
            ShelfItemVO shelfItemVO = new ShelfItemVO();
            if(nodeM != null){
                shelfItemVO.setItemId(nodeM.getProductId());
                shelfItemVO.setItemType(nodeM.getProductType());
            }else{
                shelfItemVO.setItemId(productM.getProductId());
                shelfItemVO.setItemType(productM.getProductType());
            }
            shelfItemVO.setBasePrice(getBasePrice(productM.getBasePrice()));
            shelfItemVO.setSalePrice(itemDisplaySalePrice(activityCxt, productM, salePrice));
            shelfItemVO.setAvailable(itemAvailable(activityCxt, productM));
            shelfItemVO.setSalePricePrefix(itemSalePricePrefix(activityCxt, productM));
            shelfItemVO.setSalePriceSuffix(itemSalePriceSuffix(activityCxt, productM));
            shelfItemVO.setSalePriceSuffixObject(itemSalePriceSuffixObject(activityCxt, productM));
            shelfItemVO.setPromoTags(itemPromoTags(activityCxt, productM, salePrice));
            shelfItemVO.setMarketPrice(itemMarketPrice(activityCxt, productM, salePrice));
            shelfItemVO.setTitle(itemTitle(activityCxt, productM, filterId));
            shelfItemVO.setHeadPic(buildItemPicAreaVO(activityCxt, productM, index, filterId, platform));
            shelfItemVO.setButtonCarouselMsg(buildCarouselMsg(activityCxt, productM));
            shelfItemVO.setProductTags(itemSubTitle(activityCxt, productM));
            shelfItemVO.setPriceBottomTags(itemPriceBottomTags(activityCxt, productM, cardM, salePrice, platform));
            shelfItemVO.setSpecialTags(itemSpecialTag(activityCxt, productM, shelfItemVO));
            shelfItemVO.setJumpUrl(itemJumpUrl(activityCxt, productM));
            shelfItemVO.setButton(itemBtn(activityCxt, productM, shelfItemVO.getJumpUrl()));
            shelfItemVO.setActivity(itemActivity(activityCxt, productM));
            shelfItemVO.setExtra(getExtra(activityCxt, productM));
            shelfItemVO.setWarmUp(itemWarmUp(activityCxt, productM));
            //追加下全行业共用副标题
            CommonProductTagUtils.appendUnifiedCommonProductTag(shelfItemVO, productM, activityCxt);
            // 构造后置处理
            itemBuildPostHandler(activityCxt, shelfItemVO, productM, request);
            // labs 放到最后处理，因为会依赖上面处理完的信息
            shelfItemVO.setLabs(itemOceanLabs(shelfItemVO, activityCxt, nodeM, productM, cardM, filterId, request));
            // 楼层商品监控
            floorItemsMonitor.doMonitor(activityCxt, shelfItemVO);
            return shelfItemVO;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 预热信息
     * @param activityCxt
     * @param productM
     * @return
     */
    private List<ShelfWarmUpVO> itemWarmUp(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfItemWarmUpVP<?> itemWarmUpVP = findVPoint(activityCxt, UnifiedShelfItemWarmUpVP.CODE);
            return itemWarmUpVP.execute(activityCxt, UnifiedShelfItemWarmUpVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            log.error("UnifiedProductAreaBuilder#itemWarmUp.error", e);
            return null;
        }
    }

    /**
     * 到手价
     */
    private String itemSalePrice(ActivityCxt activityCxt, ProductM productM, CardM cardM) {
        try {
            UnifiedShelfItemSalePriceVP<?> itemSalePriceVP = findVPoint(activityCxt, UnifiedShelfItemSalePriceVP.CODE);
            return itemSalePriceVP.execute(activityCxt, UnifiedShelfItemSalePriceVP.Param.builder().productM(productM).cardM(cardM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 展示价
     */
    private String itemDisplaySalePrice(ActivityCxt activityCxt, ProductM productM, String salePrice) {
        try {
            UnifiedShelfItemDisplaySalePriceVP<?> itemDisplaySalePriceVP = findVPoint(activityCxt, UnifiedShelfItemDisplaySalePriceVP.CODE);
            return itemDisplaySalePriceVP.execute(activityCxt, UnifiedShelfItemDisplaySalePriceVP.Param.builder()
                    .productM(productM).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return salePrice;
        }
    }

    /**
     * 价格前缀
     */
    private String itemSalePricePrefix(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfSalePricePrefixVP<?> salePricePrefixVP = findVPoint(activityCxt, UnifiedShelfSalePricePrefixVP.CODE);
            return salePricePrefixVP.execute(activityCxt, UnifiedShelfSalePricePrefixVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 价格后缀
     */
    private String itemSalePriceSuffix(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfSalePriceSuffixVP<?> salePricePrefixVP = findVPoint(activityCxt, UnifiedShelfSalePriceSuffixVP.CODE);
            return salePricePrefixVP.execute(activityCxt, UnifiedShelfSalePriceSuffixVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 价格后缀对象
     */
    private ShelfSalePriceSuffixVO itemSalePriceSuffixObject(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfSalePriceSuffixObjectVP<?> salePriceSuffixObjectVP = findVPoint(activityCxt, UnifiedShelfSalePriceSuffixObjectVP.CODE);
            return salePriceSuffixObjectVP.execute(activityCxt, UnifiedShelfSalePriceSuffixObjectVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            log.error("UnifiedProductAreaBuilder#itemSalePriceSuffixObject.error", e);
            return null;
        }
    }

    /**
     * 优惠力度标签
     */
    private List<RichLabelModel> itemPromoTags(ActivityCxt activityCxt, ProductM productM, String salePrice) {
        try {
            UnifiedShelfItemPromoTagVP<?> itemPromoTagVP = findVPoint(activityCxt, UnifiedShelfItemPromoTagVP.CODE);
            return itemPromoTagVP.execute(activityCxt, UnifiedShelfItemPromoTagVP.Param.builder()
                    .productM(productM).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 市场价
     */
    private String itemMarketPrice(ActivityCxt activityCxt, ProductM productM, String salePrice) {
        try {
            UnifiedShelfItemMarketPriceVP<?> unifiedShelfItemMarketPriceVP = findVPoint(activityCxt, UnifiedShelfItemMarketPriceVP.CODE);
            return unifiedShelfItemMarketPriceVP.execute(activityCxt, UnifiedShelfItemMarketPriceVP.Param.builder()
                    .productM(productM).salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 商品是否有效
     */
    private Boolean itemAvailable(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfItemAvailableVP<?> unifiedShelfItemAvailableVP = findVPoint(activityCxt, UnifiedShelfItemAvailableVP.CODE);
            Boolean available = unifiedShelfItemAvailableVP.execute(activityCxt, UnifiedShelfItemAvailableVP.Param.builder()
                    .productM(productM)
                    .build());
            return Objects.nonNull(available) ? available : Boolean.TRUE;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return Boolean.TRUE;
        }
    }

    /**
     * 团单额外非标信息
     */
    private String getExtra(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfItemExtraVP<?> extraVP = findVPoint(activityCxt, UnifiedShelfItemExtraVP.CODE);
            return extraVP.execute(activityCxt, UnifiedShelfItemExtraVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    /**
     * 价格下方标签
     */
    private List<ShelfTagVO> itemPriceBottomTags(ActivityCxt activityCxt, ProductM productM, CardM cardM, String salePrice, int platform) {
        try {
            UnifiedShelfItemPriceBottomTagVP<?> itemPriceBottomTagVP = findVPoint(activityCxt, UnifiedShelfItemPriceBottomTagVP.CODE);
            return itemPriceBottomTagVP.execute(activityCxt, UnifiedShelfItemPriceBottomTagVP.Param.builder()
                    .productM(productM).cardM(cardM).platform(platform)
                    .salePrice(salePrice).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private String getBasePrice(BigDecimal basePrice) {
        if (basePrice == null) {
            return null;
        }
        return basePrice.stripTrailingZeros().toPlainString();
    }

    private List<StyleTextModel> itemTitle(ActivityCxt activityCxt, ProductM productM, long filterId) {
        try {
            UnifiedShelfItemTitleVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfItemTitleVP.CODE);
            return vPoint.execute(activityCxt, UnifiedShelfItemTitleVP.Param.builder().productM(productM).filterId(filterId).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return Lists.newArrayList();
        }
    }

    private ItemSubTitleVO itemSubTitle(ActivityCxt activityCxt, ProductM productM) {
        try {
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            long filterId = NumberUtils.objToLong(activityCxt.getParam(ShelfActivityConstants.Params.selectedFilterId));
            UnifiedShelfItemSubTitleVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfItemSubTitleVP.CODE);
            ItemSubTitleVO result = vPoint.execute(activityCxt, UnifiedShelfItemSubTitleVP.Param.builder()
                    .productM(productM)
                    .douHuList(douHuMList)
                    .filterId(filterId)
                    .build());
            if (result != null && (CollectionUtils.isEmpty(result.getTags())&& result.getIconTag()==null)) {
                return null;
            }
            return result;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }

        // TOTO 最终空对象应该是null
    }

    private ItemSpecialTagVO itemSpecialTag(ActivityCxt activityCxt, ProductM productM, ShelfItemVO shelfItemVO) {
        try {
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            UnifiedShelfItemSpecialTagVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfItemSpecialTagVP.CODE);
            return vPoint.execute(activityCxt,
                    UnifiedShelfItemSpecialTagVP.Param.builder().productM(productM).shelfItemVO(shelfItemVO).douHuList(douHuMList).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private ShelfButtonVO itemBtn(ActivityCxt activityCxt, ProductM productM, String jumpUrl) {
        try {
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            UnifiedShelfItemButtonVP<?> itemButtonVP = findVPoint(activityCxt, UnifiedShelfItemButtonVP.CODE);
            return itemButtonVP.execute(activityCxt, UnifiedShelfItemButtonVP.Param.builder()
                    .itemJumpUrl(jumpUrl).productM(productM).douHuMList(douHuMList)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private ShelfItemActivityVO itemActivity(ActivityCxt activityCxt, ProductM productM) {
        try {
            List<DouHuM> douHuMList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
            UnifiedShelfItemActivityVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfItemActivityVP.CODE);
            return vPoint.execute(activityCxt, UnifiedShelfItemActivityVP.Param.builder()
                    .productM(productM)
                    .douHuList(douHuMList)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private String itemJumpUrl(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfItemJumpUrlVP<?> itemJumpUrlVP = findVPoint(activityCxt, UnifiedShelfItemJumpUrlVP.CODE);
            String jumpUrl = itemJumpUrlVP.execute(activityCxt, UnifiedShelfItemJumpUrlVP.Param.builder().productM(productM).build());
            // 兜底做一层空判断，防止有 VP 没实现默认的
            return StringUtils.isEmpty(jumpUrl) ? productM.getJumpUrl() : jumpUrl;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return productM.getJumpUrl();
        }
    }

    private String itemOceanLabs(ShelfItemVO itemVO, ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, ProductM productM, CardM cardM, long filterId, Request sourceRequest) {
        try {
            UnifiedShelfItemOceanLabsVP<?> itemOceanLabsVP = findVPoint(activityCxt, UnifiedShelfItemOceanLabsVP.CODE);
            return itemOceanLabsVP.execute(activityCxt,
                    UnifiedShelfItemOceanLabsVP.Param.builder().shelfItemVO(itemVO).nodeM(nodeM)
                            .dpPoiId(PoiIdUtil.getDpPoiIdL(sourceRequest)).mtPoiId(PoiIdUtil.getMtPoiIdL(sourceRequest))
                            .productM(productM).shop(sourceRequest.getCtxShop()).platform(sourceRequest.getPlatform())
                            .cardM(cardM).filterId(filterId)
                            .cityId(PlatformUtil.isMT(sourceRequest.getPlatform()) ? sourceRequest.getMtCityId() : sourceRequest.getDpCityId())
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private void itemBuildPostHandler(ActivityCxt activityCxt, ShelfItemVO shelfItemVO, ProductM productM, Request sourceRequest) {
        try {
            UnifiedShelfItemBuildPostHandlerVP<?> extraVP = findVPoint(activityCxt, UnifiedShelfItemBuildPostHandlerVP.CODE);
            extraVP.execute(activityCxt,
                    UnifiedShelfItemBuildPostHandlerVP.Param.builder()
                            .extra(sourceRequest.getExtra())
                            .shelfItemVO(shelfItemVO)
                            .productM(productM)
                            .douHuMList(activityCxt.getSource(ShelfDouHuFetcher.CODE))
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private PicAreaVO buildItemPicAreaVO(ActivityCxt activityCxt, ProductM productM, int index, long filterId, int platform) {
        try {
            UnifiedShelfItemPicVP<?> itemPicVP = findVPoint(activityCxt, UnifiedShelfItemPicVP.CODE);
            PicAreaVO picAreaVO = new PicAreaVO();
            PictureModel pictureModel = itemPicVP.execute(activityCxt,
                    UnifiedShelfItemPicVP.Param.builder().productM(productM).build());
            UnifiedShelfItemPicFloatTagVP<?> itemPicTagVP = findVPoint(activityCxt, UnifiedShelfItemPicFloatTagVP.CODE);
            List<FloatTagModel> floatTagModels = itemPicTagVP.execute(activityCxt,
                    UnifiedShelfItemPicFloatTagVP.Param.builder().productM(productM).index(index).filterId(filterId).platform(platform).build());
            picAreaVO.setPic(pictureModel);
            picAreaVO.setFloatTags(floatTagModels);
            String defaultUrl = ProductMAttrUtils.getAttrValue(productM, ProductMAttrUtils.DEAL_DEFAULT_PIC_PATH);
            picAreaVO.setDefaultPicUrl(defaultUrl);
            if(picAreaVO.getPic() == null && StringUtils.isEmpty(picAreaVO.getDefaultPicUrl())){
                return null;
            }
            return picAreaVO;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<CarouselMsg> buildCarouselMsg(ActivityCxt activityCxt, ProductM productM) {
        try {
            UnifiedShelfItemCarouselMsgVP<?> itemCarouselMsgVP = findVPoint(activityCxt,
                    UnifiedShelfItemCarouselMsgVP.CODE);
            List<CarouselMsg> carouselMsgs = itemCarouselMsgVP.execute(activityCxt,
                    UnifiedShelfItemCarouselMsgVP.Param.builder().productM(productM).build());
            return carouselMsgs;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return Lists.newArrayList();
        }
    }

    private void postBuildHandler(ActivityCxt activityCxt, ShelfProductAreaVO shelfProductAreaVO, List<ProductM> productMs){
        try {
            ProductAreaPostBuildHandlerVP<?> itemsBuildPostHandlerVP = findVPoint(activityCxt,
                    ProductAreaPostBuildHandlerVP.CODE);
            itemsBuildPostHandlerVP.execute(activityCxt,
                    ProductAreaPostBuildHandlerVP.Param.builder()
                            .products(productMs)
                            .shelfProductAreaVO(shelfProductAreaVO).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    ///////////////////////////////////////////////聚合卡片构造///////////////////////////////////////////////
    private List<StyleTextModel> itemAggregateTitle(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM) {
        try {
            AggregateItemTitleVP<?> vPoint = findVPoint(activityCxt, AggregateItemTitleVP.CODE);
            return vPoint.execute(activityCxt, AggregateItemTitleVP.Param.builder().nodeM(nodeM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return Lists.newArrayList();
        }
    }

    private PicAreaVO itemAggregatePicArea(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, long filterId) {
        try {
            AggregateItemPicVP<?> vPoint = findVPoint(activityCxt, AggregateItemPicVP.CODE);
            PictureModel pictureModel = vPoint.execute(activityCxt, AggregateItemPicVP.Param.builder()
                    .nodeM(nodeM).build());
            if(pictureModel == null || StringUtils.isEmpty(pictureModel.getPicUrl())){
                return null;
            }
            AggregateItemPicFloatTagVP<?> itemPicTagVP = findVPoint(activityCxt, AggregateItemPicFloatTagVP.CODE);
            List<FloatTagModel> floatTagModels = itemPicTagVP.execute(activityCxt,
                    AggregateItemPicFloatTagVP.Param.builder().nodeM(nodeM).filterId(filterId).build());
            PicAreaVO picAreaVO = new PicAreaVO();
            picAreaVO.setPic(pictureModel);
            picAreaVO.setFloatTags(floatTagModels);
            return picAreaVO;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private ItemSubTitleVO itemAggregateSubTitle(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM) {
        try {
            AggregateItemSubTitleVP<?> vPoint = findVPoint(activityCxt, AggregateItemSubTitleVP.CODE);
            ItemSubTitleVO result = vPoint.execute(activityCxt, AggregateItemSubTitleVP.Param.builder().nodeM(nodeM).build());
            if (result != null && CollectionUtils.isEmpty(result.getTags())) {
                return null;
            }
            return result;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<CarouselMsg> itemAggregateCarouselMsg(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM) {
        try {
            AggregateItemCarouselMsgVP<?> vPoint = findVPoint(activityCxt, AggregateItemCarouselMsgVP.CODE);
            return vPoint.execute(activityCxt, AggregateItemCarouselMsgVP.Param.builder().nodeM(nodeM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private ItemSpecialTagVO itemAggregateSpecialTag(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM) {
        try {
            AggregateSpecialTagVP<?> vPoint = findVPoint(activityCxt, AggregateSpecialTagVP.CODE);
            return vPoint.execute(activityCxt, AggregateSpecialTagVP.Param.builder().nodeM(nodeM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private void aggregateItemPriceVP(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, ShelfItemVO shelfItemVO){
        try {
            AggregateItemPriceVP<?> vPoint = findVPoint(activityCxt, AggregateItemPriceVP.CODE);
            vPoint.execute(activityCxt, AggregateItemPriceVP.Param.builder().nodeM(nodeM).shelfItemVO(shelfItemVO).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private void aggregateItemChildFold(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, ShelfItemVO shelfItemVO){
        try {
            AggregateItemChildFoldVP<?> vPoint = findVPoint(activityCxt, AggregateItemChildFoldVP.CODE);
            vPoint.execute(activityCxt, AggregateItemChildFoldVP.Param.builder().nodeM(nodeM).shelfItemVO(shelfItemVO).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    /**
     * 聚合节点后置处理
     * @param activityCxt
     * @param nodeM
     * @param shelfItemVO
     */
    private ShelfItemVO aggregatePostHandleVP(ActivityCxt activityCxt, ProductHierarchyNodeM nodeM, ShelfItemVO shelfItemVO){
        try {
            AggregatePostHandlerVP<?> vPoint = findVPoint(activityCxt, AggregatePostHandlerVP.CODE);
            return vPoint.execute(activityCxt, AggregatePostHandlerVP.Param.builder().nodeM(nodeM).shelfItemVO(shelfItemVO).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return shelfItemVO;
        }
    }

    @AbilityCfg
    @Data
    public static class Config {

        /**
         * 将所有的商品按照filter分组
         */
        boolean groupTotalProductByFilter = false;

        /**
         * ProductM中，将召回获取的attr合并到最终attr
         * 召回的attr优先级更高
         */
        List<String> mergeProductAttrFromQuery;

        /**
         * 召回的attr的重写逻辑是否不同分组间隔离
         */
        boolean mergeProductAttrIsFilterIsolate = false;


        /**
         * 是否使用商品层次结构构造
         */
        boolean useProductHierarchy = false;
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * 【一级参数, List<String>型, 必填】自定义组名
         * {@link com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher} 时作为配置传入
         * {@link QueryFetcher.Params#groupNames}
         */
        private List<String> groupNames;

        /**
         * 当前选中的筛选标签ID
         * {@link ShelfActivityConstants.Params#selectedFilterId}
         */
        private long selectedFilterId;

        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Ctx#ctxShop}
         */
        private ShopM ctxShop;

        /**
         * {@link ShelfActivityConstants.Params#topProductIds}
         */
        private String topProductIds;

        /**
         * {@link ShelfActivityConstants.Params#summaryProductIds}
         */
        private String summarypids;

        /**
         * {@link ShelfActivityConstants.Params#appVersion}
         */
        private String appVersion;

        /**
         * {@link ShelfActivityConstants.Params#extra}
         */
        private String extra;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;

        /**
         * {@link ShelfActivityConstants.Params#shopUuid}
         */
        private String shopUuid;

        /**
         * {@link ShelfActivityConstants.Params#dpCityId}
         */
        private int dpCityId;

        /**
         * {@link ShelfActivityConstants.Params#mtCityId}
         */
        private int mtCityId;
    }
}
