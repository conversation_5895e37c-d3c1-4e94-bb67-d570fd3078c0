package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.headpic;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfPicUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemPicVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicOpt.SCALE_PIC_HEIGHT;
import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedSingleRowPicOpt.SCALE_PIC_WIDTH;

@VPointOption(name = "默认-聚合卡片头图",
        description = "聚合卡片头图",
        code = DefaultAggregateItemPicOpt.CODE,
        isDefault = true)
public class DefaultAggregateItemPicOpt extends AggregateItemPicVP<DefaultAggregateItemPicOpt.Config> {

    public static final String CODE = "DefaultAggregateItemPicOpt";

    @Override
    public PictureModel compute(ActivityCxt activityCxt, Param param, Config config) {
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            return buildSpuPic(param.getNodeM(), param.getProductMMap());
        }
        return buildPicUrl(param.getProductMMap().get(param.getNodeM().getIdentityKey()));
    }

    private PictureModel buildSpuPic(ProductHierarchyNodeM nodeM, Map<String, ProductM> productMMap) {
        // 1. 获取所有子节点商品，并按销量排序
        ProductM bestSaleProduct = nodeM.getChildren().stream()
                .map(childNode -> productMMap.get(childNode.getIdentityKey()))
                .filter(Objects::nonNull)
                // 按销量降序排序，取销量最高的商品
                .max(Comparator.comparing(productM -> Optional.ofNullable(productM.getSale())
                        .map(ProductSaleM::getSale)
                        .orElse(0)))
                .orElse(null);
        return buildPicUrl(bestSaleProduct);
    }

    private PictureModel buildPicUrl(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        //图片裁剪
        String picUrl = UnifiedShelfPicUtils.toHttpsUrl(productM.getPicUrl(), SCALE_PIC_WIDTH, SCALE_PIC_HEIGHT, PictureURLBuilders.ScaleType.Cut);
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(picUrl);
        pictureModel.setAspectRadio(1);
        return pictureModel;
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
