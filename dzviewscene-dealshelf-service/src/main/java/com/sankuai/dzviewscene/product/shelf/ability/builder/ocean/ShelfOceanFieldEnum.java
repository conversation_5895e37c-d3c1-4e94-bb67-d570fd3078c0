package com.sankuai.dzviewscene.product.shelf.ability.builder.ocean;

import lombok.Getter;

/**
 * 枚举来源于对象内的所有字段{@link com.sankuai.dzviewscene.productshelf.vu.vo.ShelfOceanVO}
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
public enum ShelfOceanFieldEnum {
    ActivityPromoPopView("activityPromoPopView"),
    ActivityPromoPopViewButton("activityPromoPopViewButton"),
    ActivityPromoPopViewClose("activityPromoPopViewClose"),
    PromoPopViewButton("promoPopViewButton"),
    PromoPopView("promoPopView"),
    SecondBookingBtn("secondBookingBtn"),
    ProductArea("productArea"),
    SecondProductArea("secondProductArea"),
    ChildrenFilterBar("childrenFilterBar"),
    FilterBar("filterBar"),
    More("more"),
    ProductItem("productItem"),
    ProductItemTag("productItemTag"),
    ProductBottomTag("productBottomTag"),
    SearchIcon("searchIcon"),
    WholeShelf("wholeShelf"),
    BookingBtn("bookingBtn"),
    BuyBtn("buyBtn"),
    JoyCard("joyCard"),
    SecondProductItem("secondProductItem"),
    MoreFilter("moreFilter"),
    PinSession("pinSession"),
    SecondMore("secondMore"),
    PromoPopViewClose("promoPopViewClose"),
    ProductItemPromo("productItemPromo"),
    ProductAreaTips("productAreaTips"),
    PageView("pageView"),
    ExplainLayer("explainLayer"),
    FilterPicker("filterPicker"),
    ChildrenCollapse("childrenCollapse"),
    ChildrenMore("childrenMore"),
    SpuItem("spuItem"),
    SkuItem("skuItem"),
    ReserveMindBar("reserveMindBar"),

    ReserveMindSupernatant("reserveMindSupernatant");

    @Getter
    private String code;

    ShelfOceanFieldEnum(String code) {
        this.code = code;
    }

    /** 以下为生成代码
     public static void main(String[] args) {
     for (Field field : ShelfOceanVO.class.getDeclaredFields()) {
     System.out.println(String.format("%s(\"%s\"),",capitalize(field.getName()), field.getName()));
     }
     }

     public static String capitalize(String str){
     return str.substring(0, 1).toUpperCase() + str.substring(1);
     }
     **/
}
