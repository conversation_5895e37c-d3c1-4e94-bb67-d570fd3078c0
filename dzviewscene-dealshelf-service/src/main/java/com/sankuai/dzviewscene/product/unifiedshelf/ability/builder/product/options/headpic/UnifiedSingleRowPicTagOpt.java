/*
 * Create Author : liyanmin
 * Create Date : 2024-09-13
 * Project :
 * File Name : SingleRowItemPicFloatTagOpt.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.athena.viewscene.framework.pmf.meta.utils.ExpressionUtils;
import com.sankuai.dzviewscene.dealshelf.shelfvo.FloatTagModel;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.ActivityTagCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicFloatTagVP;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 功能描述:
 * <p>
 *
 * <AUTHOR> yanmin.li
 * <p>
 * @version 1.0 2024-09-13
 * @since dzviewscene-dealshelf-home 1.0
 */
@VPointOption(name = "单列商卡头图角标", description = "单列商卡头图角标", code = UnifiedSingleRowPicTagOpt.CODE)
public class UnifiedSingleRowPicTagOpt extends UnifiedShelfItemPicFloatTagVP<UnifiedSingleRowPicTagOpt.Config> {

    public static final String CODE = "UnifiedSingleRowPicTagOpt";

    @Autowired
    private FloatTagBuildStrategyFactory tagBuildStrategyFactory;

    @Override
    public List<FloatTagModel> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (Objects.isNull(productM) || StringUtils.isEmpty(productM.getPicUrl()))
            return null;
        if (Objects.isNull(config) || CollectionUtils.isEmpty(config.getRuleTagGroups())) {
            return null;
        }
        // 根据规则表达式获取配置的标签组，实验场景
        RuleTagGroup ruleTagGroup = calTagGroup(context, config, param.getFilterId());
        if (Objects.isNull(ruleTagGroup) || CollectionUtils.isEmpty(ruleTagGroup.getTagGroupCfgList())) {
            return null;
        }
        // 计算标签组的标签，非空则add最终result中
        List<FloatTagModel> result = Lists.newArrayList();
        int showType = getShelfPicShowTypeByCfg(context, config);
        ruleTagGroup.getTagGroupCfgList().stream().filter(item -> CollectionUtils.isNotEmpty(item.getTagItemCfgs()))
                .forEach(item -> {
                    FloatTagModel floatTagVO = calGroupFloatTagVO(showType, param, item.getTagItemCfgs(), result, context);
                    if (Objects.nonNull(floatTagVO)) {
                        result.add(floatTagVO);
                    }
                });
        return result;
    }

    private RuleTagGroup calTagGroup(ActivityCxt context, Config config,
                                     long selectedFilterId) {
        Optional<RuleTagGroup> ruleTagGroup = config.getRuleTagGroups().stream()
                .filter(item -> (StringUtils.isEmpty(item.getRuleExpression())
                        || ExpressionUtils.executeBoolean(item.getRuleExpression(), context.getParameters())))
                .findFirst();
        List<TagGroup> tagGroups = ruleTagGroup
                .map(RuleTagGroup::getTagGroupCfgList).orElse(null);
        if (CollectionUtils.isEmpty(tagGroups)) {
            return null;
        }
        return ruleTagGroup.get();
    }

    private int getShelfPicShowTypeByCfg(ActivityCxt context, Config config) {
        if (Objects.isNull(config.getPicShowTypeCfg())) {
            return 0;
        }
        if (config.getPicShowTypeCfg().getHeadPicShowType() > 0) {
            return config.getPicShowTypeCfg().getHeadPicShowType();
        }
        Map<Integer, String> showType2RuleExpression = config.getPicShowTypeCfg().getShowType2RuleExpression();
        if (MapUtils.isNotEmpty(showType2RuleExpression)) {
            for (Integer type : showType2RuleExpression.keySet()) {
                if (ExpressionUtils.executeBoolean(showType2RuleExpression.get(type), context.getParameters())) {
                    return type;
                }
            }
        }
        return 0;
    }

    private FloatTagModel calGroupFloatTagVO(int showType, Param param,
                                             List<TagItemCfg> tagItemCfgs, List<FloatTagModel> result, ActivityCxt context) {
        if (CollectionUtils.isEmpty(tagItemCfgs)) {
            return null;
        }
        if (Objects.isNull(param) || CollectionUtils.isEmpty(tagItemCfgs)) {
            return null;
        }
        for (TagItemCfg tagItemCfg : tagItemCfgs) {
            FloatTagBuildStrategy currentStrategy = tagBuildStrategyFactory
                    .getBuildStrategy(tagItemCfg.getStrategyName());
            if (currentStrategy == null) {
                continue;
            }
            FloatTagModel floatTagVO = currentStrategy.newBuild(
                    buildFloatTagBuildReq(param, tagItemCfg.getActivityTagCfg(), showType, result, context),
                    tagItemCfg.getProductTagCfg());
            if (floatTagVO != null) {
                return floatTagVO;
            }
        }
        return null;
    }

    private FloatTagBuildReq buildFloatTagBuildReq(Param param, ActivityTagCfg activityTagCfg, int showType,
            List<FloatTagModel> result, ActivityCxt context) {
        FloatTagBuildReq floatTagBuildReq = new FloatTagBuildReq();
        floatTagBuildReq.setShelfShowType(showType);
        floatTagBuildReq.setProductM(param.getProductM());
        floatTagBuildReq.setActivityTagCfg(activityTagCfg);
        floatTagBuildReq.setPlatform(param.getPlatform());
        floatTagBuildReq.setIndex(param.getIndex());
        floatTagBuildReq.setFilterId(param.getFilterId());
        floatTagBuildReq.setContext(context);
        return floatTagBuildReq;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 必须配置
         */
        private List<RuleTagGroup> ruleTagGroups;

        /**
         * 必须配置
         */
        private HeadPicShowTypeCfg picShowTypeCfg;

    }

    @Data
    public static class HeadPicShowTypeCfg {

        /**
         * 静态样式直接配置headPicShowType，会优先取headPicShowType
         * {@link ShelfShowTypeEnum}
         */
        private int headPicShowType;

        /**
         * 动图规则的则请配置showType2RuleExpression
         * key为ShelfShowTypeEnum的type，详见 {@link ShelfShowTypeEnum}
         * value为命中规则，规则的参数请确保在context.getParameters()能获取到
         */
        private Map<Integer, String> showType2RuleExpression;
    }

    @Data
    public static class RuleTagGroup implements Serializable {

        /**
         * 实验路由表达式，无实验场景，无需配置
         */
        private String ruleExpression;

        /**
         * 全局的头图标签配置
         */
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<TagGroup> tagGroupCfgList;

        /**
         * 多标签共存时位置冲突解决方案，详见 {@link  FloatTagTypeEnum}
         */
        private int floatTagType;
    }

    @Data
    public static class TagGroup implements Serializable {

        /**
         * 全局的头图标签配置
         */
        private List<TagItemCfg> tagItemCfgs;

        /**
         * 指定 filterId 下优先展示对应策略的标签
         * 如美团补贴筛选下，同时有美团补贴和新人特惠标签时，展示美团补贴
         * key - filterId
         * value - 策略名
         */
        private Map<Long, String> filterId2TagStrategyMap;

    }


    @Data
    public static class TagItemCfg implements Serializable {

        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 非活动或无需对活动进行过滤则无需配置，图片信息全部来着M端上传
         */
        private ActivityTagCfg activityTagCfg;

        /**
         * 产品化标签的物料配置，不配置则使用策略默认的
         */
        private FloatTagBuildCfg productTagCfg;
    }
}
