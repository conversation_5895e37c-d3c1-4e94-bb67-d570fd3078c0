package com.sankuai.dzviewscene.product.unifiedshelf.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum JumpUrlPatternEnum {
    // 为 PATTERN_1 定义多个 Pattern Map，每个 Map 包含不同数量的 k-pattern 对
    TUANDEAL(
            createPatternMapList(
                    createPatternMap("path", ".*(?i)tuandeal.*"),
                    createPatternMap("path", "(?i).*deal.*detail.*"),
                    createPatternMap(
                            "mrn_entry", "(?i).*deal.*detail.*"))
    ),
    CARDDEAL(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gcbu",
                            "mrn_entry", ".*(?i)card.*detail.*"))
    ),
    // 为 PATTERN_2 定义多个 Pattern Map
    PREPAY(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gc",
                            "mrn_entry", ".*(?i)prepay.*"))
    ),
    BOOKDETAIL(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gc",
                            "mrn_entry", ".*(?i)bookdetail.*",
                            "mrn_component",".*(?i)bookdetail.*")
            )),
    // 打包商品
    PACKAGEDEAL(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz", "(?i)gc.*",
                            "mrn_entry", ".*(?i)mall.*card.*",
                            "mrn_component", ".*(?i)productpage.*"))
    ),
    ORDERSUBMIT(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gc",
                            "mrn_entry", ".*(?i)order.*submit.*"))
    ),
    RESERVE(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gc",
                            "mrn_entry", ".*(?i)reserve.*",
                            "mrn_component",".*(?i)submit.*"))
    ),
    MINIPROGRAM(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz","gc.*",
                            "mrn_entry", ".*(?i)miniprogram.*",
                            "mrn_component",".*(?i)miniprogram.*"))
    ),
    ADDTOCART(
            createPatternMapList(
                    createPatternMap(
                            "mrn_biz", "(?i)gc.*", // 匹配以 gc 开头的字符串，忽略大小写
                            "mrn_entry", ".*(?i)submit*", // 匹配包含 gcsubmitordermrnmodules-unify 的字符串，忽略大小写
                            "mrn_component", ".*(?i)add.*to.*cart.*") // 匹配包含 addtocartpage-popup 的字符串，忽略大小写
            )
    )

    ;

    private final List<Map<String, String>> patternMaps;

    JumpUrlPatternEnum(List<Map<String, String>> patternMaps) {
        this.patternMaps = patternMaps;
    }

    public List<Map<String, String>> getPatternMaps() {
        return patternMaps;
    }

    /**
     * 创建一个包含键 - 正则表达式对的 Map，支持不定数量的参数
     * @param keyPatternPairs 键 - 正则表达式对，按 key1, pattern1, key2, pattern2... 的顺序传入
     * @return 包含键 - 正则表达式对的 Map
     * @throws IllegalArgumentException 如果传入的参数数量为奇数
     */
    private static Map<String, String> createPatternMap(String... keyPatternPairs) {
        if (keyPatternPairs.length % 2 != 0) {
            throw new IllegalArgumentException("参数数量必须为偶数，以成对提供键和正则表达式");
        }
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < keyPatternPairs.length; i += 2) {
            String key = keyPatternPairs[i];
            String pattern = keyPatternPairs[i + 1];
            map.put(key, pattern);
        }
        return map;
    }

    private static List<Map<String, String>> createPatternMapList(Map<String, String>... patternMaps) {
        List<Map<String, String>> list = new ArrayList<>();
        for (Map<String, String> map : patternMaps) {
            list.add(map);
        }
        return list;
    }
}