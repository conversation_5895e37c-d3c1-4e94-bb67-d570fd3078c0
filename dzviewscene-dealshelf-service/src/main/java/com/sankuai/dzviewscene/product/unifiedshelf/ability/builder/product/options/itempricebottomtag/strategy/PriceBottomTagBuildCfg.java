package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PriceBottomTagBuildCfg {

    /**
     * 标签新增商户黑名单，目前仅OtherPromoTagBuildStrategy有消费
     */
    private Map<Integer, List<Long>> tagBlackDpShopIds;

    /**
     * 使用普通神券标签，无膨胀文案
     */
    private boolean useNormalMagicalTag;
}
