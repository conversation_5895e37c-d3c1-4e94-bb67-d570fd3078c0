package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils;

import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

public class ProductHierarchyUtils {

    public static boolean isSpuNode(ProductHierarchyNodeM nodeM) {
        if (nodeM == null) {
            return false;
        }
        return nodeM.getProductType() == ProductTypeEnum.SPT_SPU.getType();
    }

    public static boolean isSpuNode(ShelfItemVO shelfItemVO) {
        if (shelfItemVO == null) {
            return false;
        }
        return shelfItemVO.getItemType() == ProductTypeEnum.SPT_SPU.getType();
    }

    public static boolean isSkuNode(ProductHierarchyNodeM nodeM) {
        if (nodeM == null) {
            return false;
        }
        return nodeM.getProductType() == ProductTypeEnum.SKU.getType();
    }

    public static DealProductSpuDTO getSpuNodeModel(ProductHierarchyNodeM nodeM, Map<String, ProductM> productMMap) {
        return nodeM.getChildren().stream()
                .map(childNode -> productMMap.get(childNode.getIdentityKey()))
                .map(product -> getSpuDTO(nodeM.getProductId(), product))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    public static DealProductSpuDTO getSpuDTO(long spuId, ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getSpuMList())) {
            return null;
        }
        return productM.getSpuMList().stream().filter(v -> spuId == v.getSpuId()
                && StringUtils.isNotBlank(v.getSpuName())).findFirst().orElse(null);
    }
}
