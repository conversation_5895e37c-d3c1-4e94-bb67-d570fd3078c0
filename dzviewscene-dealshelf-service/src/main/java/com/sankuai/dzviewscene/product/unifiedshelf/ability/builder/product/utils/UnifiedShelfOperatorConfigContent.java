package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.dianping.vc.sdk.dp.config.LionObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfCategoryAndVersionDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;

/**
 * 配置内容
 *
 * @auther: liweilong06
 * @date: 2025/2/25 10:30 上午
 */
public class UnifiedShelfOperatorConfigContent {

    public static final String LION_KEY_SUB_TITLE_VERSION_CONFIG = "com.sankuai.dzviewscene.dealshelf.operator.config.subtitle.version";

    private static final String SUB_TITLE_GROOVY_SHELL_IMPORT = "import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP.Param;\n" +
            "import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;\n" +
            "import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;\n" +
            "import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;\n" +
            "import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;\n" +
            "import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;\n" +
            "import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyDouHuSkExc;\n" +
            "import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyRunner;\n" +
            "import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyRunnerRequest;\n" +
            "import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;\n" +
            "import com.google.common.collect.Lists;\n"+
            "import com.sankuai.dzviewscene.shelf.platform.utils.ProductMUtils;\n" +
            "import com.google.gson.Gson;\n" +
            "import com.google.gson.reflect.TypeToken;\n";

    @SuppressWarnings("unchecked")
    private static final LionObject<List<OperatorShelfCategoryAndVersionDTO>> SUB_TITLE_VERSION_LION = LionObject.create(
        LION_KEY_SUB_TITLE_VERSION_CONFIG,
            new TypeReference<List<OperatorShelfCategoryAndVersionDTO>>() {});

    /**
     * 获取运营货架副标题版本配置
     * @return
     */
    public static List<OperatorShelfCategoryAndVersionDTO> getOperatorShelfSubTitleVersionConfig() {
        if (SUB_TITLE_VERSION_LION == null || CollectionUtils.isNotEmpty(SUB_TITLE_VERSION_LION.getObject())) {
            return SUB_TITLE_VERSION_LION.getObject();
        }
        return Lists.newArrayList();
    }

    /**
     * 获取运营货架副标题的代码Improt部分
     * @param configDTO
     * @return
     */
    public static String getSubTitleWholeShellContent(OperatorShelfConfigDTO configDTO) {
        if (configDTO == null || StringUtils.isBlank(configDTO.getGroovyContent())) {
            return null;
        }
        return SUB_TITLE_GROOVY_SHELL_IMPORT + configDTO.getGroovyContent();
    }
    

}
