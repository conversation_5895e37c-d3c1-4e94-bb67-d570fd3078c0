package com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpadding;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.data.Converters;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.PaddingConstants;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.*;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.ProductParallelQueryPostHandler;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.OldEngineAdaptUtil;
import com.sankuai.dzviewscene.product.shelf.ability.common.DealRecallFallbackComponent;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.framework.monitor.AbilityExecuteMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import graphql.execution.Async;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Ability(code = ProductPaddingFetcher.CODE,
        name = "Model-商品填充能力",
        description = "填充商品。构造 Map<String, ProductGroupM>，支持多种商品类型填充（需要配置multiGroupParams参数）",
        activities = {DealShelfActivity.CODE, DealFilterListActivity.CODE, UnifiedShelfActivity.CODE},
        dependency = {DealQueryFetcher.CODE, ProductPrePaddingFetcher.CODE, ShopProductPaddingFetcher.CODE, ProductParallelQueryPostHandler.CODE}
)
public class ProductPaddingFetcher extends PmfAbility<Map<String, ProductGroupM>, ProductPaddingFetcher.Request, ProductPaddingFetcher.Config> implements BeanFactoryAware {

    public static final String CODE = "ProductPaddingFetcher";

    @Resource
    private ComponentFinder componentFinder;

    @Resource
    private BeanFactory beanFactory;

    @Resource
    private DealRecallFallbackComponent dealRecallFallbackComponent;

    private static Map<Integer, Class<? extends PaddingHandler>> paddingType2PaddingHandlers = new HashMap<>();

    static {
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.spuThemePadding.type, SpuProductThemeHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.dealThemePadding.type, DealGroupThemePaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.noPaddingHandler.type, NoPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.combinationProductPadding.type, CombinationProductThemePaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.timeCardProductPadding.type, TimeCardThemePaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.PaddingType.generalProductPadding.type, GeneralProductThemePaddingHandler.class);
    }

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt ctx, Request request, Config config) {
        long startTime = System.currentTimeMillis();
        return AbilityExecuteMonitor.executeMonitor(execute(ctx, request, config), ctx, CODE, startTime);
    }

    private CompletableFuture<Map<String, ProductGroupM>> execute(ActivityCxt ctx, Request request, Config config) {
        //填充前召回数据兜底判断处理
        dealRecallFallbackComponent.recallFallback(ctx);
        //如果配置了填充相关的参数，走新框架逻辑
        if (MapUtils.isNotEmpty(config.multiGroupParams)) {
            return getNewEngineRes(ctx, config);
        }
        //兜底老框架的填充能力
        return getOldEngineRes(ctx, config);
    }

    /**
     * 新的填充能力支持多种商品类型的填充
     *
     * @param ctx
     * @param config
     * @return
     */
    private CompletableFuture<Map<String, ProductGroupM>> getNewEngineRes(ActivityCxt ctx, Config config) {
        // 1.  获取上下文多组商品
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = CompletableFuture.completedFuture(getProductGroups(ctx));
        // 2. 批量填充
        CompletableFuture<Map<String, CompletableFuture<ProductGroupM>>> productGroupCompletableFuture = productGroupsCompletableFuture.thenApply(productGroupMMap -> {
            return batchPadding(ctx, productGroupMMap, config.multiGroupParams, config);
        });
        // 3. 结构转换
        return productGroupCompletableFuture.thenApply(productGroupCompletableFutureMap -> buildProductGroupMap(productGroupCompletableFutureMap));
    }

    private Map<String, ProductGroupM> getProductGroupM(ActivityCxt ctx) {
        if (ctx.getSource(ProductParallelQueryPostHandler.CODE) != null) {
            return ctx.getSource(ProductParallelQueryPostHandler.CODE);
        }
        return ctx.getSource(DealQueryFetcher.CODE);
    }

    public void addPrePaddingProductM(ActivityCxt ctx, Map<String, ProductGroupM> productGroups) {
        if (MapUtils.isEmpty(productGroups)) {
            return;
        }
        // 获取预填充商品
        Map<String, ProductGroupM> preProductGroups = ctx.getSource(ProductPrePaddingFetcher.CODE);
        // 获取门店召回并填充的商品
        Map<String, ProductGroupM> shopProductGroups = ctx.getSource(ShopProductPaddingFetcher.CODE);
        productGroups.forEach((groupName, productGroupM) -> {
            // 合并预填充的商品
            if (MapUtils.isNotEmpty(preProductGroups)){
                mergePrePaddingProductM(productGroupM, preProductGroups.get(groupName));
            }
            // 合并门店召回填充的商品
            if (MapUtils.isNotEmpty(shopProductGroups)) {
                mergePrePaddingProductM(productGroupM, shopProductGroups.get(groupName));
            }
        });
    }

    private void mergePrePaddingProductM(ProductGroupM productGroupM, ProductGroupM preProductGroupM) {
        if (productGroupM == null || preProductGroupM == null
                || MapUtils.isEmpty(preProductGroupM.getPreLoadProducts())) {
            return;
        }
        if(MapUtils.isEmpty(productGroupM.getPreLoadProducts())) {
            productGroupM.setPreLoadProducts(preProductGroupM.getPreLoadProducts());
        }else{
            productGroupM.getPreLoadProducts().putAll(preProductGroupM.getPreLoadProducts());
        }
    }

    private Map<String, ProductGroupM> buildProductGroupMap(Map<String, CompletableFuture<ProductGroupM>> productGroupCompletableMap) {
        if (MapUtils.isEmpty(productGroupCompletableMap)) {
            return Maps.newHashMap();
        }

        return productGroupCompletableMap.entrySet()
                .stream()
                .collect(HashMap::new,
                        (map, productGroupEntry) -> {
                            map.put(productGroupEntry.getKey(), productGroupEntry.getValue().join());
                        },
                        HashMap::putAll
                );
    }

    private Map<String, CompletableFuture<ProductGroupM>> batchPadding(ActivityCxt activityContext, Map<String, ProductGroupM> productGroups, Map<String, List<Map<String, Object>>> multiGroupParams, Config config) {
        if (MapUtils.isEmpty(productGroups)) {
            return Maps.newHashMap();
        }
        return productGroups.entrySet()
                .stream()
                .collect(
                        HashMap::new,
                        (map, productGroupEntry) -> {
                            map.put(productGroupEntry.getKey(), paddingSingleProductGroup(activityContext, productGroupEntry.getValue(), multiGroupParams.get(productGroupEntry.getKey()), config));
                        },
                        HashMap::putAll
                );
    }

    /**
     * 填充多种商品类型的商品
     *
     * @param ctx
     * @param productGroupM
     * @param multiGroupParams
     * @return
     */
    private CompletableFuture<ProductGroupM> paddingSingleProductGroup(ActivityCxt ctx, ProductGroupM productGroupM, List<Map<String, Object>> multiGroupParams, Config config) {
        //参数校验，参数无效直接返回
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts()) || CollectionUtils.isEmpty(multiGroupParams)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        //获取填充对象列表
        CompletableFuture<List<ProductGroupM>> productMsFutureMap = getProductGroupMsFutureMap(ctx, productGroupM, multiGroupParams, config);
        //构造结果
        return productMsFutureMap.thenApply(productGroupMS -> mergeSingleProductGroup(productGroupMS, productGroupM));
    }

    private ProductGroupM mergeSingleProductGroup(List<ProductGroupM> productGroupMS, ProductGroupM productGroupM) {
        if (CollectionUtils.isEmpty(productGroupMS)) {
            return productGroupM;
        }
        Map<Integer, Map<Integer, ProductM>> productType2IdProductMap = getProductType2IdProductMap(productGroupMS);
        if (MapUtils.isEmpty(productType2IdProductMap)) {
            return productGroupM;
        }
        List<ProductM> productMS = new ArrayList<>();
        productGroupM.getProducts().forEach(productM -> {
            int productType = productM.getProductType();
            Map<Integer, ProductM> idProductMap = productType2IdProductMap.get(productType);
            if (MapUtils.isEmpty(idProductMap) || idProductMap.get(productM.getProductId()) == null) {
                return;
            }
            productMS.add(idProductMap.get(productM.getProductId()));
        });
        productGroupM.setProducts(productMS);
        return productGroupM;
    }

    private Map<Integer, Map<Integer, ProductM>> getProductType2IdProductMap(List<ProductGroupM> productGroupMS) {
        Map<Integer, Map<Integer, ProductM>> productType2IdProductMap = new HashMap<>();
        for (ProductGroupM productGroupM : productGroupMS) {
            if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
                continue;
            }
            Map<Integer, List<ProductM>> productType2Products = Converters.newAggByPropertyNameConverter("productType", productGroupM.getProducts());
            if (MapUtils.isEmpty(productType2Products)) {
                continue;
            }
            productType2Products.entrySet().forEach(productType2ProductsEntry -> {
                if (CollectionUtils.isEmpty(productType2ProductsEntry.getValue())) {
                    return;
                }
                Map<Integer, ProductM> productId2Product = productType2IdProductMap.get(productType2ProductsEntry.getKey());
                if (MapUtils.isEmpty(productId2Product)) {
                    productId2Product = new HashMap<>();
                }
                productId2Product.putAll(Converters.newMapByPropertyNameConverter("productId", productType2ProductsEntry.getValue()));
                productType2IdProductMap.put(productType2ProductsEntry.getKey(), productId2Product);
            });
        }
        return productType2IdProductMap;
    }

    private CompletableFuture<List<ProductGroupM>> getProductGroupMsFutureMap(ActivityCxt ctx, ProductGroupM productGroupM, List<Map<String, Object>> multiGroupParams, Config config) {
        Map<Integer, List<ProductM>> productTypeProductsMap = Converters.newAggByPropertyNameConverter("productType", productGroupM.getProducts());
        if (MapUtils.isEmpty(productTypeProductsMap)) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        List<CompletableFuture<ProductGroupM>> productMFutures = new ArrayList<>();
        productTypeProductsMap.entrySet().forEach(productTypeProductsMEntry -> {
            if (productTypeProductsMEntry == null || CollectionUtils.isEmpty(productTypeProductsMEntry.getValue())) {
                return;
            }
            Map<String, Object> groupParams = getParamsByProductType(multiGroupParams, productTypeProductsMEntry.getKey());
            if (MapUtils.isEmpty(groupParams)) {
                return;
            }
            int paddingProductType = getPaddingProductType(groupParams);
            preHandleParamsByCategory(ctx, groupParams);
            Class paddingHandlerClazz = paddingType2PaddingHandlers.get(paddingProductType);
            if (paddingHandlerClazz == null) {
                Cat.logErrorWithCategory("PaddingFetcher_Error", String.format("paddingProductType:%s找不到", paddingProductType), new BusinessException(String.format("查找填充器=%s, 找不到", paddingProductType)));
                return;
            }
            PaddingHandler paddingHandler = (PaddingHandler) beanFactory.getBean(paddingHandlerClazz);
            if (paddingHandler == null) {
                Cat.logErrorWithCategory("PaddingFetcher_Error", String.format("paddingHandlerClazz:%s找不到", paddingHandlerClazz.getName()), new BusinessException(String.format("查找填充器找不到", paddingHandlerClazz.getName())));
                return;
            }
            productMFutures.add(paddingHandler.padding(ctx, buildProductGroupM(productTypeProductsMEntry.getValue(), productGroupM.getPreLoadProducts()), groupParams));

        });
        return Async.each(productMFutures);
    }

    public static void preHandleParamsByCategory(ActivityCxt ctx, Map<String, Object> groupParams) {
        if (Objects.nonNull(groupParams.get(PaddingFetcher.Params.backCategory2DiffParams))) {
            ShopCategoryConfig backCategory2DiffParams = JSON.parseObject(JSON.toJSONString(groupParams.get(PaddingFetcher.Params.backCategory2DiffParams)), ShopCategoryConfig.class);
            DivergentParams4DealTheme hitConfig = ShopCategoryConfigUtils.getHitConfig(ctx, backCategory2DiffParams, DivergentParams4DealTheme.class);
            preHandleParams4DealTheme(groupParams, hitConfig);
        }
    }

    private static void preHandleParams4DealTheme(Map<String, Object> groupParams, DivergentParams4DealTheme hitConfig) {
        if (Objects.isNull(hitConfig)) {
            return;
        }
        if (StringUtils.isNotBlank(hitConfig.getPlanId())) {
            groupParams.put(PaddingFetcher.Params.planId, hitConfig.getPlanId());
        }
        if (hitConfig.getPromoTemplateId() > 0) {
            groupParams.put(PaddingFetcher.Params.promoTemplateId, hitConfig.getPromoTemplateId());
        }
        if (hitConfig.getTcMergePromoTemplateId() > 0) {
            groupParams.put("tcMergePromoTemplateId", hitConfig.getTcMergePromoTemplateId());
        }
        if (CollectionUtils.isNotEmpty(hitConfig.getBpDealGroupTypes())) {
            groupParams.put("bpDealGroupTypes", hitConfig.getBpDealGroupTypes());
        }
        if (hitConfig.getDirectPromoScene() > 0) {
            groupParams.put("directPromoScene", hitConfig.getDirectPromoScene());
        }
        if (hitConfig.getScene() > 0) {
            groupParams.put("scene", hitConfig.getScene());
        }
    }

    private ProductGroupM buildProductGroupM(List<ProductM> products, Map<Integer, List<ProductM>> preLoadProducts) {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(products);
        productGroupM.setPreLoadProducts(preLoadProducts);
        return productGroupM;
    }

    private Map<String, Object> getParamsByProductType(List<Map<String, Object>> multiGroupParams, int productType) {
        for (Map<String, Object> groupParams : multiGroupParams) {
            int currentProductType = ParamsUtil.getIntSafely(groupParams, PaddingConstants.Params.paddingProductType);
            if (productType == currentProductType) {
                return groupParams;
            }
        }
        //兜底返回第一组
        return multiGroupParams.get(0);
    }

    private int getPaddingProductType(Map<String, Object> params) {
        int productType = ParamsUtil.getIntSafely(params, PaddingConstants.Params.paddingProductType);
        //填充类型为空，默认是团购主题填充
        if (productType <= 0) {
            return PaddingConstants.PaddingType.dealThemePadding.type;
        }
        return productType;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    /**
     * 获取旧框架的构造结果，若不走旧框架则返回 null
     *
     * @param ctx
     * @param config
     * @return
     */
    private CompletableFuture<Map<String, ProductGroupM>> getOldEngineRes(ActivityCxt ctx, Config config) {
        if (config.getOldEngineCfg() == null) {
            return null;
        }
        OldEngineAdaptUtil.paddingAbilityToCtx(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, config.getOldEngineCfg());
        OldEngineAdaptUtil.paddingExtPointToCtx(ctx, PaddingFetcherExt.EXT_POINT_PADDING_FETCHER_CODE, config.getOldEngineCfg());
        ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(getProductGroups(ctx)));
        ctx.addParam(PaddingFetcher.QueryParam.sortId, config.getSortId());
        ctx.addParam(PaddingFetcher.QueryParam.topN, config.getTopN());
        ctx.addParam(PaddingFetcher.QueryParam.unsupportedSortNavTags, config.getUnsupportedSortNavTags());

        ActivityContext oldContext = ActivityCtxtUtils.toActivityContext(ctx);
        return componentFinder.findAbility(oldContext, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(oldContext);
    }

    private Map<String, ProductGroupM> getProductGroups(ActivityCxt ctx) {
        Map<String, ProductGroupM> productGroupMMap = getProductGroupM(ctx);
        addPrePaddingProductM(ctx, productGroupMMap);
        return productGroupMMap;
    }

    @AbilityRequest
    @Data
    public static class Request {

    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 旧框架的适配配置
         */
        private OldEngineAdaptCfg oldEngineCfg;


        /**
         * 排序枚举，取 {@link PaddingFetcher.QueryValue}
         * {@link PaddingFetcher.QueryParam#sortId}
         */
        private int sortId;


        /**
         * 排序枚举，取 {@link PaddingFetcher.QueryValue}
         * {@link PaddingFetcher.QueryParam#topN}
         */
        private int topN;

        /**
         * {@link PaddingFetcher.QueryParam#unsupportedSortNavTags}
         */
        private String unsupportedSortNavTags;

        /**
         * 多组商品填充参数，其中【一级参数, List<Map<String, Object>>型, 必填】指定每组商品填充参数
         * key:一级参数：{@link QueryFetcher.Params#groupParams}
         * value:每组商品需要填充的参数，支持多种类型商品填充
         */
        private Map<String, List<Map<String, Object>>> multiGroupParams;
    }

    @Data
    public static class DivergentParams4DealTheme {
        private String planId;
        private int tcMergePromoTemplateId;
        private int promoTemplateId;
        private List<Integer> bpDealGroupTypes;
        private int directPromoScene;
        private int scene;
    }
}
