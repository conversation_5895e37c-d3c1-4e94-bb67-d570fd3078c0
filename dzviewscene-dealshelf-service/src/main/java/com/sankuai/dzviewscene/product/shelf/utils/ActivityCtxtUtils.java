package com.sankuai.dzviewscene.product.shelf.utils;


import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.springframework.beans.BeanUtils;

/**
 * created by zhangzhiyuan04 in 2021/8/20
 */
public class ActivityCtxtUtils {

    public static ActivityContext toActivityContext(ActivityCxt ctx) {
        ActivityContext activityContext = new ActivityContext();
        BeanUtils.copyProperties(ctx, activityContext);
        return activityContext;
    }

    public static ActivityCxt toActivityCtx(ActivityContext activityContext) {
        ActivityCxt activityCxt = new ActivityCxt();
        BeanUtils.copyProperties(activityContext, activityCxt);
        return activityCxt;
    }
}
