package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupTitleVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/26 3:34 下午
 */
@VPointOption(name = "团购详情sku货列表组名默认变化点", description = "团购详情sku货列表组名默认变化点，支持配置",code = DefaultDealDetailSkusGroupTitleVPO.CODE, isDefault = true)
public class DefaultDealDetailSkusGroupTitleVPO extends DealDetailSkusGroupTitleVP<DefaultDealDetailSkusGroupTitleVPO.Config>{

    public static final String CODE = "DefaultDealDetailSkusGroupTitleVP";

    @Override
    public String compute(ActivityCxt context, DealDetailSkusGroupTitleVP.Param param, DefaultDealDetailSkusGroupTitleVPO.Config config) {
        boolean isMustGroup = param.isMustGroup();
        if (isMustGroup) {
            return config.getMustTitle();
        }
        String optionalTitleFormat = config.getOptionalTitleFormat();
        if(StringUtils.isEmpty(optionalTitleFormat)) {
            return null;
        }
        return String.format(optionalTitleFormat, param.getTotalNum(), param.getOptionalNum());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String optionalTitleFormat;
        private String mustTitle;
    }

}
