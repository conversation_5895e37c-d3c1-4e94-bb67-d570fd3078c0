package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@VPointOption(name = "空商品图片",
        description = "返回null",
        code = "NullItemPicOpt")
public class NullItemPicOpt extends ItemPicVP<Void> {
    @Override
    public PicAreaVO compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
