package com.sankuai.dzviewscene.product.unifiedshelf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.meituan.mtrace.Tracer;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.dealshelf.enums.UnifiedShelfSceneCodeEnum;
import com.sankuai.dzviewscene.dealshelf.req.UnifiedShelfRequest;
import com.sankuai.dzviewscene.dealshelf.service.UnifiedShelfService;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.shelf.utils.LogControl;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.core.UnifiedShelfFTConfiguration;
import com.sankuai.dzviewscene.product.unifiedshelf.core.UnifiedShelfProductFTConfiguration;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.MonitorUtils;
import com.sankuai.dzviewscene.shelf.faulttolerance.ActivityContextRequestBuilder;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@MdpPigeonServer(url = "com.sankuai.dzviewscene.shelf.UnifiedShelfService")
public class UnifiedShelfServiceImpl implements UnifiedShelfService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UnifiedShelfService.class.getSimpleName());
    private static final String CAT_TYPE = UnifiedShelfService.class.getSimpleName();

    private final FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();

    @Resource
    private UnifiedShelfFTConfiguration unifiedShelfFTConfiguration;

    @Resource
    private UnifiedShelfProductFTConfiguration unifiedShelfProductFTConfiguration;

    @Resource
    private ActivityContextRequestBuilder activityContextRequestBuilder;

    @Override
    public UnifiedShelfResponse queryShelfNavAndProduct(UnifiedShelfRequest shelfRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryShelfNavAndProduct");
        try {
            putTrace(shelfRequest);
            // 参数校验
            ActivityContextRequest contextRequest = buildActivityContextRequest(shelfRequest);
            if (invalidRequest(contextRequest)) {
                return failResp("异常的请求，缺少必要参数");
            }
            MonitorUtils.monitor(contextRequest, "QueryShelfNavAndProduct");
            contextRequest.setChannel(ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
            activityContextRequestBuilder.buildActivityContextRequest(contextRequest);
            LogControl.logFuc(null, () -> LOGGER.info("queryShelfNavAndProductEntrance,request:{},contextRequest:{}",
                    SafeJacksonUtils.serialize(shelfRequest), SafeJacksonUtils.serialize(contextRequest)));
            UnifiedShelfResponse response = setUpResponse(faultToleranceEngine.execute(contextRequest, unifiedShelfFTConfiguration));
            LogControl.logFuc(null, () -> LOGGER.info("queryShelfNavAndProductEnd,response:{}", JSON.toJSONString(response)));
            if (StringUtils.isNotBlank(contextRequest.getShopIdInconsistencyFlag()) && LionConfigHelper.shopIdInconsistencySwitch()) {
                LogUtils.recordKeyMsg("dzdealshelfEntranceshopIdInconsistency", contextRequest);
            }
            return response;
        } catch (Exception e) {
            Cat.logEvent("QueryShelfNavAndProduct", "error");
            LOGGER.error("queryShelfNavAndProduct-error,req:{}", SafeJacksonUtils.serialize(shelfRequest), e);
            return failResp("服务异常，请稍后重试！");
        } finally {
            transaction.complete();
        }
    }

    @Override
    public ShelfFilterProductAreaVO queryShelfProductByFilterId(UnifiedShelfRequest shelfRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryShelfProductByFilterId");
        try {
            putTrace(shelfRequest);
            ActivityContextRequest contextRequest = buildActivityContextRequest(shelfRequest);
            contextRequest.setChannel(ShelfActivityConstants.ChannelType.dealShelfListForTab);
            // 参数校验
            if (invalidRequest(contextRequest)) {
                return failResp2("异常的请求，缺少必要参数");
            }
            MonitorUtils.monitor(contextRequest, "QueryShelfProductByFilterId");
            activityContextRequestBuilder.buildActivityContextRequest(contextRequest);
            LogControl.logFuc(null, () -> LOGGER.info("queryShelfProductByFilterIdEntrance,request:{},contextRequest:{}",
                    SafeJacksonUtils.serialize(shelfRequest), SafeJacksonUtils.serialize(contextRequest)));
            ShelfFilterProductAreaVO productAreaVO = setUpResponse2(faultToleranceEngine.execute(contextRequest, unifiedShelfProductFTConfiguration));
            LogControl.logFuc(null, () -> LOGGER.info("queryShelfProductByFilterIdEnd,response:{}", JSON.toJSONString(productAreaVO)));
            return productAreaVO;
        } catch (Exception e) {
            Cat.logEvent("QueryShelfProductByFilterId", "error");
            LOGGER.error("queryShelfProductByFilterId-error,req:{}", SafeJacksonUtils.serialize(shelfRequest), e);
            return failResp2("服务异常，请稍后重试！");
        } finally {
            transaction.complete();
        }
    }

    private void putTrace(UnifiedShelfRequest request) {
        try {
            if (request == null) {
                return;
            }
            Tracer.putContext(LogControl.LOG_CONTROL_DEBUG_MODE, request.get_shelf_debug());
            Tracer.putContext(LogControl.LOG_CONTROL_USER, String.valueOf(getUserId(request)));
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    private ActivityContextRequest buildActivityContextRequest(UnifiedShelfRequest shelfRequest) {
        if (shelfRequest == null) {
            return null;
        }
        ActivityContextRequest request = new ActivityContextRequest();
        // 对 SceneCode 做一下管理
        if (StringUtils.isNotEmpty(shelfRequest.getSceneCode())
                && UnifiedShelfSceneCodeEnum.getScenes().contains(shelfRequest.getSceneCode())) {
            request.setSceneCode(shelfRequest.getSceneCode());
        } else {
            Cat.logEvent("UnacceptedSceneCode", shelfRequest.getSceneCode());
            LOGGER.warn("Unaccepted SceneCode:{}", shelfRequest.getSceneCode());
        }
        request.setPlatform(getPlatform(shelfRequest));

        //门店信息
        request.setShopId(getShopId(shelfRequest));
        //用户信息
        request.setUnionId(getUnionId(shelfRequest));
        request.setVersion(getVersion(request.getPlatform(), shelfRequest));
        request.setUserId(getUserId(shelfRequest));
        request.setLng(shelfRequest.getLng());
        request.setLat(shelfRequest.getLat());
        request.setCoordType(shelfRequest.getCoordType());
        //待确认原因
        request.setCityId(shelfRequest.getCityid());
        request.setLocationCityId(shelfRequest.getLocationcityid());
        request.setClient(getClient(shelfRequest));
        request.setDeviceId(getDeviceId(shelfRequest));
        request.setOpenId(shelfRequest.getGatewayParam() != null ? shelfRequest.getGatewayParam().getOpenId() : null);
        request.setOpenIdCipher(shelfRequest.getGatewayParam() != null ? shelfRequest.getGatewayParam().getOpenIdCipher() : null);
        if(shelfRequest.getPlatform() == VCClientTypeEnum.DP_XCX.getCode()){
            request.setOpenId(shelfRequest.getGatewayParam() != null ? shelfRequest.getGatewayParam().getOpenidPlt() : null);
            request.setOpenIdCipher(shelfRequest.getGatewayParam() != null ? shelfRequest.getGatewayParam().getOpenId() : null);
        }

        //意向承接
        request.setSearchKeyword(shelfRequest.getSearchkeyword());
        request.setSummaryProductIds(getSummaryProductIds(shelfRequest));
        request.setAnchorGoodId(shelfRequest.getAnchorgoodid());
        //request.setBizType(biztype);
        //request.setRecommendinfo(recommendinfo);

        request.setShelfVersion(shelfRequest.getShelfversion());
        request.setMtSIFlag(shelfRequest.getMtsiflag());

        request.setPageSource(shelfRequest.getPagesource());
        //经纬度坐标类型，货架默认为GCJ02
        request.setCoordType(StringUtils.isNotEmpty(shelfRequest.getCoordType()) ? shelfRequest.getCoordType() : "GCJ02");

        //价格一致率加密串
        request.setPricecipher(shelfRequest.getPricecipher());

        //神券相关参数
        request.setPosition(shelfRequest.getPosition());
        request.setWttRegionId(shelfRequest.getWttregionid());

        // 分页参数
        request.setPageindex(0);
        request.setPagesize(0);
        if ("1".equals(shelfRequest.getPagination())) {
            request.setPageindex(Optional.ofNullable(shelfRequest.getPageindex()).orElse(0));
            request.setPagesize(Optional.ofNullable(shelfRequest.getPagesize()).orElse(0));
            request.setPagination(shelfRequest.getPagination());
        }
        request.setCustomInfo(shelfRequest.getCustominfo());
        request.setFilterBtnId(shelfRequest.getFilterbtnid());
        request.setFilterParams(shelfRequest.getFilterparams());
        request.setRefreshTag(shelfRequest.getRefreshtag());
        request.setExtra(shelfRequest.getExtra());
        request.setAppId(shelfRequest.getGatewayParam() == null ? null : shelfRequest.getGatewayParam().getAppId());
        request.setOperatorPreviewConfigTags(shelfRequest.getOperatorPreviewConfigTags());
        request.setMockDouHuResult(shelfRequest.getMockDouHuResult());
        request.setChannelType(shelfRequest.getChannelType());
        return request;
    }

    private static String getSummaryProductIds(UnifiedShelfRequest shelfRequest) {
        if (StringUtils.isEmpty(shelfRequest.getSummarypids())) {
            return null;
        }
        //兼容老productids
        if (!shelfRequest.getSummarypids().trim().startsWith("{")) {
            Map<String, String> summarypids = Maps.newHashMap();
            summarypids.put("deal", shelfRequest.getSummarypids());
            return JSONObject.toJSONString(summarypids);
        }
        return shelfRequest.getSummarypids();
    }

    private static int getPlatform(UnifiedShelfRequest shelfRequest) {
        if (shelfRequest.getPlatform() > 0) {
            return shelfRequest.getPlatform();
        }
        if (shelfRequest.getGatewayParam() == null
                || StringUtils.isEmpty(shelfRequest.getGatewayParam().getPlatform())
                || shelfRequest.getGatewayParam().getPlatform().equals("dp")) {
            return VCClientTypeEnum.DP_APP.getCode();
        }
        return VCClientTypeEnum.MT_APP.getCode();
    }

    private static String getUnionId(UnifiedShelfRequest shelfRequest) {
        if (StringUtils.isNotEmpty(shelfRequest.getUnionId())) {
            return shelfRequest.getUnionId();
        }
        if (shelfRequest.getGatewayParam() != null) {
            return shelfRequest.getGatewayParam().getUnionid();
        }
        return null;
    }

    private static String getVersion(int platform, UnifiedShelfRequest shelfRequest) {
        if (StringUtils.isNotEmpty(shelfRequest.getVersion())) {
            return shelfRequest.getVersion();
        }
        if (shelfRequest.getGatewayParam() != null) {
            return shelfRequest.getGatewayParam().getVersion();
        }
        return null;
    }

    private static String getDeviceId(UnifiedShelfRequest shelfRequest) {
        if (StringUtils.isNotEmpty(shelfRequest.getDeviceId())) {
            return shelfRequest.getDeviceId();
        }
        if (shelfRequest.getGatewayParam() != null) {
            return shelfRequest.getGatewayParam().getDpid();
        }
        return null;
    }

    private static String getClient(UnifiedShelfRequest shelfRequest) {
        if (StringUtils.isNotEmpty(shelfRequest.getClient())) {
            return shelfRequest.getClient();
        }
        if (shelfRequest.getGatewayParam() != null) {
            return shelfRequest.getGatewayParam().getClient();
        }
        return null;
    }

    private static long getShopId(UnifiedShelfRequest shelfRequest) {
        if (shelfRequest.getGatewayParam() == null) {
            return 0L;
        }
        return shelfRequest.getGatewayParam().getShopId();
    }

    private static long getUserId(UnifiedShelfRequest shelfRequest) {
        if (shelfRequest.getGatewayParam() == null) {
            return 0L;
        }
        return shelfRequest.getGatewayParam().getUserId();
    }

    public static String getTraceId() {
        try {
            return Tracer.id();
        } catch (Exception e) {
            Cat.logError("GetTraceError", e);
            return null;
        }
    }

    private boolean invalidRequest(ActivityContextRequest shelfRequest) {
        if (shelfRequest == null) {
            return true;
        }
        if (shelfRequest.getShopId() <= 0) {
            return true;
        }
        return false;
    }

    private UnifiedShelfResponse setUpResponse(UnifiedShelfResponse response) {
        if (response == null) {
            return failResp("未查询到有效数据，请稍后重试！");
        }
        response.setTraceId(getTraceId());
        return response;
    }

    private ShelfFilterProductAreaVO setUpResponse2(ShelfFilterProductAreaVO response) {
        if (response == null) {
            return failResp2("未查询到有效数据，请稍后重试！");
        }
        response.setTraceId(getTraceId());
        return response;
    }

    private static UnifiedShelfResponse failResp(String msg) {
        UnifiedShelfResponse response = new UnifiedShelfResponse();
        response.setTraceId(getTraceId());
        response.setRetainMsg(msg);
        return response;
    }

    private static ShelfFilterProductAreaVO failResp2(String msg) {
        ShelfFilterProductAreaVO response = new ShelfFilterProductAreaVO();
        response.setTraceId(getTraceId());
        response.setRetainMsg(msg);
        return response;
    }
}
