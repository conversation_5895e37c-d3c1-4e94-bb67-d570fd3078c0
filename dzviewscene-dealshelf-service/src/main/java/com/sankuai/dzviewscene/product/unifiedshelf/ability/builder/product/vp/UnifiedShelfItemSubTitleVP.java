package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding.ContextHandlerAbility;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigContent;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.enums.OperatorShelfConfigStrategyUnit;

@Slf4j
@VPoint(name = "商品-副标题", description = "商品-副标题", code = UnifiedShelfItemSubTitleVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemSubTitleVP<T> extends PmfVPoint<ItemSubTitleVO, UnifiedShelfItemSubTitleVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemSubTitleVP";
    public static final String UNIFIED_SHELF_VP_COUNT = "unifiedShelfVPCount";
    public static final String UNIFIED_SHELF_VP_DIFF_COUNT = "unifiedShelfVPDiffCount";
    public static final String DIFF_SAME = "sameResult";
    public static final String DIFF_DIFF = "diffResult";
    public static final String TOTAL_SUBTITLE = "totalSubtitle";
    public static final String HAS_SUB_TITLE_SHELL = "has_sub_title_shell";
    public static final String TEMPLATE_SCENE_CODE_SERVICE_TYPE = "%s_%s";
    public static final String SUB_TITLE = "subTitle";
    public static final String SHELL_RUN_ERROR = "shellRunError";
    public static final String EMPTY_SUB_TITLE_BY_SHELL = "emptySubTitleByShell";
    public static final String SUB_TITLE_SHOULD_HAS_CONFIG = "subTitleShouldHasConfig";
    public static final String EMPTY_SUB_TITLE_BY_CODE = "emptySubTitleByCode";
    public static final String COMPUTE_FROM_OPERATOR_CONFIG_ERROR = "computeFromOperatorConfigError";
    public static final String USE_GROOVY_SUB_TITLE = "useGroovySubTitle";
    public static final String USE_CODE_SUB_TITLE = "useCodeSubTitle";
    public static final String DIFF_FORMAT = "%s_%s_%s_%s";

    @Data
    public static class Config {

        /**
         * 禁用搜索关键词高亮，默认开启高亮
         */
        private boolean disableHighlight;
    }

    public static ItemSubTitleVO build4Default(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
        itemSubTitleVO.setJoinType(0);
        itemSubTitleVO.setTags(build4DefaultTags(tags));
        return itemSubTitleVO;
    }

    public static List<StyleTextModel> build4DefaultTags(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Lists.newArrayList();
        }
        List<StyleTextModel> styleTextModels = Lists.newArrayList();
        for (String tag : tags) {
            if (StringUtils.isNotBlank(tag)) {
                styleTextModels.add(buildGrayText(tag));
            }
        }
        return styleTextModels;
    }

    protected static List<StyleTextModel> build4DefaultTags(ProductM productM) {
        return build4DefaultTags(productM.getProductTags());
    }

    public static StyleTextModel buildHighlightText(String tag) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(tag);
        styleTextModel.setStyle(TextStyleEnum.TEXT_HIGHLIGHT.getType());
        return styleTextModel;
    }

    public static StyleTextModel buildGrayText(String tag) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(tag);
        styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
        return styleTextModel;
    }

    public static StyleTextModel buildColorText(String tag, String colorType) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(tag);
        styleTextModel.setStyle(colorType);
        return styleTextModel;
    }

    @Override
    public final ItemSubTitleVO compute(ActivityCxt context, Param request, T config) {
        Cat.logEvent(UNIFIED_SHELF_VP_COUNT, TOTAL_SUBTITLE);
        boolean isDiff2Code = UnifiedShelfOperatorConfigUtils.isDiff2Code(context, request.getProductM(), OperatorShelfConfigStrategyUnit.subTitle);
        ItemSubTitleVO resultFromGroovy = computeFromOperatorConfig(context, request);
        if (resultFromGroovy != null && !isDiff2Code) {
            // 只有结果不为空并且不进行diff的时候，才返回groovy的结果
            Cat.logEvent(UNIFIED_SHELF_VP_COUNT, USE_GROOVY_SUB_TITLE);
            return resultFromGroovy;
        }
        Cat.logEvent(UNIFIED_SHELF_VP_COUNT, USE_CODE_SUB_TITLE);
        ItemSubTitleVO resultFromCode = computeFromOpt(context, request, config);
        if (isDiff2Code) {
            isResultSameAndReport(resultFromGroovy, resultFromCode, context, request);
        }
        if (resultFromCode == null || CollectionUtils.isEmpty(resultFromCode.getTags())) {
            Cat.logEvent(EMPTY_SUB_TITLE_BY_CODE, String.format(TEMPLATE_SCENE_CODE_SERVICE_TYPE, context.getSceneCode(),
                    getServiceTypeId(request)));
        }
        return resultFromCode;
    }

    /**
     * 比对两个结果是不是一样
     * @param resultFromGroovy
     * @param resultFromCode
     * @param context
     * @return true 结果一样
     */
    private boolean isResultSameAndReport(ItemSubTitleVO resultFromGroovy, ItemSubTitleVO resultFromCode, ActivityCxt context, Param request) {
        try {
            if (resultFromGroovy == null || resultFromCode == null) {
                Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, DIFF_SAME);
                return true;
            }
            int platform = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform);
            long shopId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpPoiIdL);
            if (PlatformUtil.isMT(platform)) {
                shopId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.mtPoiIdL);
            }
            // 比较joinType
            if (resultFromGroovy.getJoinType() != resultFromCode.getJoinType()) {
                Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, String.format(DIFF_FORMAT, DIFF_DIFF, platform, shopId, "joinTypeDiff"));
                return false;
            }

            // 比较tags
            List<StyleTextModel> groovyTags = resultFromGroovy.getTags();
            List<StyleTextModel> codeTags = resultFromCode.getTags();
            
            String groovyTagsStr = JacksonUtils.serialize(groovyTags);
            String codeTagsStr = JacksonUtils.serialize(codeTags);
            int productId=request.getProductM().getProductId();

            if (groovyTags == null || codeTags == null) {
                Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, DIFF_SAME);
                return false;
            }

            if (groovyTags.size() != codeTags.size()) {
                Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, String.format(DIFF_FORMAT, DIFF_DIFF, platform, shopId, productId));
                log.info("unifiedShelfVPDiff: platform : {}, shopId : {}, productId : {}, groovyTags : {}, codeTags : {}, groovyParams : {}",
                        platform, shopId, productId, groovyTagsStr, codeTagsStr, JacksonUtils.serialize(buildGroovyParams(request)));
                return false;
            }
            for (int i = 0; i < groovyTags.size(); i++) {
                StyleTextModel groovyTag = groovyTags.get(i);
                StyleTextModel codeTag = codeTags.get(i);

                if (!Objects.equals(groovyTag.getText(), codeTag.getText()) ||
                    !Objects.equals(groovyTag.getStyle(), codeTag.getStyle())) {
                    Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, String.format(DIFF_FORMAT, DIFF_DIFF, platform, shopId, productId));
                    log.info("unifiedShelfVPDiff: platform : {}, shopId : {}, productId : {}, groovyTags : {}, codeTags : {}, groovyParams : {}",
                            platform, shopId, productId, groovyTagsStr, codeTagsStr, JacksonUtils.serialize(buildGroovyParams(request)));
                    return false;
                }
            }

            // 所有字段都相同
            Cat.logEvent(UNIFIED_SHELF_VP_DIFF_COUNT, DIFF_SAME);
            return true;
        } catch (Exception e) {
            log.error("diff2Code error", e);
        }
        return false;
    }

    private long getServiceTypeId(Param request) {
        if (request == null || request.getProductM() == null) {
            return 0L;
        }
        return request.getProductM().getServiceTypeId();
    }

    private ItemSubTitleVO computeFromOperatorConfig(ActivityCxt context, Param param) {
        if (!shouldProcessOperatorConfig(context, param)) {
            return null;
        }

        try {
            OperatorConfigs configs = fetchOperatorConfigs(context, param.getProductM());
            if (!configs.hasValidConfig()) {
                handleMissingConfig(context, param.getProductM());
                return null;
            }

            Cat.logEvent(UNIFIED_SHELF_VP_COUNT, HAS_SUB_TITLE_SHELL);
            return executeOperatorConfig(context, configs, buildGroovyParams(param));
        } catch (Throwable e) {
            handleOperatorConfigError(context, e);
            return null;
        }
    }

    private boolean shouldProcessOperatorConfig(ActivityCxt context, Param param) {
        return param != null
            && param.getProductM() != null
            && UnifiedShelfOperatorConfigUtils.shouldHasOperatorConfig(context, param.getProductM());
    }

    @Data
    @Builder
    private static class OperatorConfigs {
        private OperatorShelfConfigDTO previewOrExpConfig;
        private OperatorShelfConfigDTO releaseConfig;

        public boolean hasValidConfig() {
            return isValidConfig(previewOrExpConfig) || isValidConfig(releaseConfig);
        }

        private static boolean isValidConfig(OperatorShelfConfigDTO config) {
            return config != null && StringUtils.isNotBlank(config.getGroovyContent());
        }
    }

    private OperatorConfigs fetchOperatorConfigs(ActivityCxt context, ProductM productM) {
        return OperatorConfigs.builder()
                .previewOrExpConfig(UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(
                        context, productM, OperatorShelfConfigStrategyUnit.subTitle, false))
                .releaseConfig(UnifiedShelfOperatorConfigUtils.getShelfOperatorConfig(
                        context, productM, OperatorShelfConfigStrategyUnit.subTitle, true))
                .build();
    }

    private void handleMissingConfig(ActivityCxt context, ProductM productM) {
        context.addParam(ShelfActivityConstants.Ctx.needFlashOperatorConfigFlag, true);
        Cat.logEvent(SUB_TITLE_SHOULD_HAS_CONFIG, String.format(TEMPLATE_SCENE_CODE_SERVICE_TYPE,
                context.getSceneCode(), productM.getServiceTypeId()));
    }

    private Map<String, Object> buildGroovyParams(Param param) {
        Map<String, Object> params = new HashMap<>();
        params.put("param", param);
        // 兼容处理
        params.put("productM", param.getProductM());
        params.put("douHuList", param.getDouHuList());
        params.put("filterId", param.getFilterId());
        return params;
    }

    private ItemSubTitleVO executeOperatorConfig(ActivityCxt context, OperatorConfigs configs, Map<String, Object> params) {
        // 先尝试执行预览Or实验版本
        Object result = configs.getPreviewOrExpConfig() != null
                ? getGroovyResult(configs.getPreviewOrExpConfig(), params, UnifiedShelfOperatorConfigUtils.isPreview(context))
                : null;

        // 预览Or实验版本没有这个团单分类的逻辑，那么尝试执行线上正式版
        if (result == null && configs.getReleaseConfig() != null) {
            result = getGroovyResult(configs.getReleaseConfig(), params, false);
        }

        if (result == null) {
            return null;
        }

        ItemSubTitleVO subTitleVO = (ItemSubTitleVO) result;
        subTitleVO.setTags(removeEmptyTags(subTitleVO.getTags()));

        if (CollectionUtils.isEmpty(subTitleVO.getTags())) {
            Cat.logEvent(EMPTY_SUB_TITLE_BY_SHELL, String.format(TEMPLATE_SCENE_CODE_SERVICE_TYPE,
                    context.getSceneCode(), ((ProductM) params.get("productM")).getServiceTypeId()));
        }

        return subTitleVO;
    }

    private void handleOperatorConfigError(ActivityCxt context, Throwable e) {
        Cat.logEvent(COMPUTE_FROM_OPERATOR_CONFIG_ERROR,
                String.format(TEMPLATE_SCENE_CODE_SERVICE_TYPE, SUB_TITLE,
                        UnifiedShelfOperatorConfigUtils.isPreview(context)));
        log.error("computeFromOperatorConfig error", e);
        Cat.logError("computeFromOperatorConfig error", e);
    }

    private Object getGroovyResult(OperatorShelfConfigDTO configDTO, Map<String, Object> params, boolean preView) {
        // 单独做TryCatch是为了实验版有问题能降级到线上正式版
        try {
            String groovyContent = UnifiedShelfOperatorConfigContent.getSubTitleWholeShellContent(configDTO);
            return UnifiedShelfOperatorConfigUtils.executeGroovy(groovyContent, params);
        } catch (Exception e) {
            // 打点记录是预览链路异常还是正式链路异常
            Cat.logEvent(SHELL_RUN_ERROR,
                    String.format(TEMPLATE_SCENE_CODE_SERVICE_TYPE, SUB_TITLE, preView));
            log.error("getGroovyResultError，previewOrExpConfig={}，params={}",
                    JsonCodec.encodeWithUTF8(configDTO), JsonCodec.encodeWithUTF8(params), e);
            Cat.logError("getGroovyResultError", e);
        }
        return null;
    }

    private List<StyleTextModel> removeEmptyTags(List<StyleTextModel> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Lists.newArrayList();
        }
        return tags.stream().filter(tag -> StringUtils.isNotBlank(tag.getText())).collect(Collectors.toList());
    }

    public abstract ItemSubTitleVO computeFromOpt(ActivityCxt context, Param request, T config);

    public static void tryQueryHighLight(ActivityCxt context, ItemSubTitleVO itemSubTitleVO, boolean disableHighlight) {
        if (disableHighlight || itemSubTitleVO == null
                || CollectionUtils.isEmpty(itemSubTitleVO.getTags())) {
            return;
        }
        ContextHandlerResult contextHandlerResult = context.getSource(ContextHandlerAbility.CODE);
        if (contextHandlerResult == null || CollectionUtils.isEmpty(contextHandlerResult.getRecognizeList())) {
            return;
        }
        itemSubTitleVO.getTags().forEach(tag -> highLightText(tag, contextHandlerResult.getRecognizeList()));
    }

    private static void highLightText(StyleTextModel tag, List<String> recognizeList) {
        if (tag == null || StringUtils.isBlank(tag.getText())) {
            return;
        }
        if (recognizeList.contains(tag.getText())) {
            tag.setStyle(TextStyleEnum.TEXT_HIGHLIGHT.getType());
        }
    }

    /**
     * 预售单逻辑处理
     */
    public static List<StyleTextModel> getPreSaleProductRichTags(ProductM productM, UnifiedShelfItemSubTitleVP.Config config) {
        if (!PreSaleUtils.isPreSaleDeal(productM)) {
            return Lists.newArrayList();
        }
        String preSaleDate = PreSaleUtils.getPreSaleDateProductTag(productM);
        List<String> productTags;
        if (StringUtils.isBlank(preSaleDate)) {
            productTags = productM.getProductTags();
        } else {
            productTags = Lists.newArrayList(preSaleDate);
        }
        return productTags.stream()
                .filter(StringUtils::isNotEmpty)
                .map(UnifiedShelfItemSubTitleVP::buildGrayText)
                .collect(Collectors.toList());
    }

    @Data
    @Builder
    @VPointParam
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Param {
        /**
         * 商品信息
         */
        private ProductM productM;
        /**
         * 斗斛实验结果列表
         */
        private List<DouHuM> douHuList;

        private long filterId;

    }
}
