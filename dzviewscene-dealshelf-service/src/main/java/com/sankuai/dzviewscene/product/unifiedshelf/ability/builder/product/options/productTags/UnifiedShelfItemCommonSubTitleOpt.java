package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.MarketingActivityStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.subtitle.DefaultAggregateItemSubTitleOpt;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.SubTitleJoinTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.product.utils.DateUtils;
import com.sankuai.dzviewscene.product.utils.MassageBookUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
@VPointOption(name = "默认-富文本副标题", description = "返回团单标签副标题", code = "UnifiedShelfItemCommonSubTitleOpt", isDefault = true)
public class UnifiedShelfItemCommonSubTitleOpt extends UnifiedShelfItemSubTitleVP<UnifiedShelfItemCommonSubTitleOpt.Config> {

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    public static final String ATTR_VALID_PRODUCT_TAG_IDS = "attr_validProductTagIds";

    private static final Integer PRODUCT_CATEGORY = 2200073;

    private static final String SHI_YONG_WEN_TI = "shiyongwenti";

    /**
     * 保价attr
     */
    public static final String ATTR_GUARANTEE_TAG_CODES = "attr_guaranteeTagCodes";

    @Autowired
    private ProductTagStrategyFactory productTagStrategyFactory;

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param param, Config config) {
        if (config.isForceNull()) {
            return null;
        }
        // 处理普通副标题逻辑
        ItemSubTitleVO commonSubTitle = buildCommonSubTitle(context, param, config);
        // 处理安心逻辑
        ItemSubTitleVO anXinSubTitle = buildAnXinSubTitle(param, config);
        // 组装
        if (anXinSubTitle != null) {
            // 安心逻辑
            if (config.isAnXinOverrideOriginalSubTitle()) {
                // 安心优先
                return anXinSubTitle;
            }
            if (commonSubTitle != null && CollectionUtils.isNotEmpty(commonSubTitle.getTags())) {
                anXinSubTitle.getTags().addAll(commonSubTitle.getTags());
            }
            return anXinSubTitle;
        }
        //处理足疗营销标签放首位
        ItemSubTitleVO activityTag = getActivityTag(context, param, config);
        if (activityTag != null) {
            if (commonSubTitle != null && CollectionUtils.isNotEmpty(commonSubTitle.getTags())) {
                activityTag.getTags().addAll(commonSubTitle.getTags());
            }
            return activityTag;
        }
        return commonSubTitle;
    }

    private ItemSubTitleVO buildCommonSubTitle(ActivityCxt context, Param param, Config config) {
        // 类目树路由策略副标题
        List<StyleTextModel> strategyTags = getStrategyTags(context, param.getProductM(), config);
        if (CollectionUtils.isNotEmpty(strategyTags)) {
            ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
            itemSubTitleVO.setJoinType(0);
            itemSubTitleVO.setTags(strategyTags);
            return itemSubTitleVO;
        }
        List<String> productTags = getProductTags(context, param.getProductM(), config);
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        ItemSubTitleVO itemSubTitleVO = build4Default(productTags);
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks()) || DouHuUtils.hitAnySk(douHuMList, config.getNewJoinTypeExpSks())) {
            List<String> styles = Collections.nCopies(productTags.size(), TextStyleEnum.TEXT_GRAY_BACKGROUND.getType());
            itemSubTitleVO = build4CustomStyle(productTags, styles, config.getNewJoinType());
        }
        if(ActivityCtxtUtils.checkMultiState(context)){
            if(StringUtils.isNotBlank(getEarliestReserveTime(param, config))){
                // 一品多态逻辑，新增最早可订时间，优先级最高
                productTags.add(0, getEarliestReserveTime(param, config));
            }
            List<String> styles = Collections.nCopies(productTags.size(), TextStyleEnum.TEXT_GRAY_BACKGROUND.getType());
            itemSubTitleVO = build4CustomStyle(productTags, styles, SubTitleJoinTypeEnum.SPACE.getType());
        }
        if (config.isEnableAfterPay() && ProductMAttrUtils.timeCardIsShowAfterPayTag(param.getProductM(),context,config.getAfterPayExps())) {
            if (!DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks()) && !DouHuUtils.hitAnySk(douHuMList, config.getNewJoinTypeExpSks())) {
                //新样式不需要先用后付
                itemSubTitleVO = itemSubTitleVO == null ? new ItemSubTitleVO() : itemSubTitleVO;
                StyleTextModel afterPayTag = buildPreAfterPayTag(config);
                itemSubTitleVO.setJoinType(0);

                List<StyleTextModel> tags = itemSubTitleVO.getTags() == null ? new ArrayList<>() : itemSubTitleVO.getTags();
                tags.add(0, afterPayTag);
                itemSubTitleVO.setTags(tags);
            }
        }
        tryQueryHighLight(context, itemSubTitleVO, config.isDisableHighlight());
        return itemSubTitleVO;
    }

    /**
     * 获取最早可订时间文本
     * @param param
     * @param config
     * @return
     */
    private String getEarliestReserveTime(Param param, Config config) {
        try{
            if(param.getProductM() == null || CollectionUtils.isEmpty(param.getProductM().getExtAttrs())){
                return null;
            }
            return MassageBookUtils.getProductEarliestBookingTimeStr(param.getProductM());
        }catch (Exception e){
            log.error("generate earliest reserve time error", e);
            return null;
        }
    }

    private List<StyleTextModel> getStrategyTags(ActivityCxt context, ProductM productM, Config config) {
        String strategy = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2Strategy(), String.class);
        if(StringUtils.isNotBlank(strategy)){
            ProductTagStrategy buildStrategy = productTagStrategyFactory.getProductTagStrategy(strategy);
            if (Objects.nonNull(buildStrategy)) {
                ProductTagBuildReq req = buildRequest(context, productM);
                List<StyleTextModel> build = buildStrategy.build(req);
                if (CollectionUtils.isNotEmpty(build)) {
                    return build;
                }
            }
        }
        return null;
    }

    private ItemSubTitleVO buildAnXinSubTitle(Param param, Config config) {
        if (!config.isEnableAnXin()) {
            return null;
        }
        if (!hasAnXinTag(param.getProductM(), config.getAnXinTagCodes()) && !hasAnXinProductTag(param.getProductM(), config.getAnXinProductTagCodes())) {
            return null;
        }
        ItemSubTitleVO anXinSubTitleVO = new ItemSubTitleVO();
        // 间隔样式
        anXinSubTitleVO.setJoinType(config.getAnXinJoinType());
        // 图片
        anXinSubTitleVO.setIconTag(buildAnXinIcon(config));
        // 文案说明
        if (CollectionUtils.isNotEmpty(config.getAnXinTextList())) {
            List<StyleTextModel> tags = config.getAnXinTextList().stream().map(tag -> {
                StyleTextModel styleTextModel = new StyleTextModel();
                styleTextModel.setText(tag);
                styleTextModel.setStyle(config.getAnXinTextType());
                return styleTextModel;
            }).collect(Collectors.toList());
            anXinSubTitleVO.setTags(tags);
        }
        return anXinSubTitleVO;
    }

    private List<String> getProductTags(ActivityCxt context, ProductM productM, Config config){
        List<String> result = Lists.newArrayList();
        //静态副标题
        if (StringUtils.isNotBlank(config.getStaticSubTitle())) {
            return Lists.newArrayList(config.getStaticSubTitle());
        }
        //美团放心改
        if (isFangxinChange(productM)) {
            return Lists.newArrayList(getSKUApplicableIssues(productM));
        }
        //预售单逻辑
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            String preSaleDate = PreSaleUtils.getPreSaleDateProductTag(productM);
            if(StringUtils.isNotBlank(preSaleDate)){
                return Lists.newArrayList(preSaleDate);
            }
        }
        //爆品副标题
        if (config.isEnableHotSpuTag() && ProductMAttrUtils.hotSpu(productM)) {
            List<String> hotSpuTags = ProductMAttrUtils.getHotSpuSpecialProductTags(productM);
            if (CollectionUtils.isNotEmpty(hotSpuTags)) {
                result.addAll(hotSpuTags);
                return result;
            }
        }
        //通用副标题
        if(CollectionUtils.isNotEmpty(productM.getProductTags())){
            result.addAll(productM.getProductTags());
        }
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks()) || DouHuUtils.hitAnySk(douHuMList, config.getNewJoinTypeExpSks())) {
            //推荐语
            String recommend = getRecommendTag(productM, config);
            if (StringUtils.isNotBlank(recommend)) {
                result.add(recommend);
            }
        }
        return result;
    }

    private String getSKUApplicableIssues(ProductM productM) {
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        return allSkus.stream()
                .filter(sku -> PRODUCT_CATEGORY == sku.getProductCategory())
                .findFirst()
                .map(this::extractApplicableIssues)
                .orElse(null);
    }

    private String extractApplicableIssues(SkuItemDto skuItemDto) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        return skuItemDto.getAttrItems().stream()
                .filter(attr -> SHI_YONG_WEN_TI.equals(attr.getAttrName()))
                .findFirst()
                .map(SkuAttrItemDto::getAttrValue)
                .orElse(null);
    }

    private boolean isFangxinChange(ProductM productM) {
        return "true".equals(productM.getAttr(IS_FANGXIN_CHANGE));
    }

    /**
     * 检查商品是否有安心标签
     * @param productM 商品信息
     * @param anXinTagCodes 安心标签ID
     * @return 是否有安心标签
     */
    private boolean hasAnXinProductTag(ProductM productM, List<Long> anXinProductTagCodes) {
        if (CollectionUtils.isEmpty(anXinProductTagCodes) || StringUtils.isBlank(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS))) {
            return false;
        }
        List<Long> productTagIds = JsonCodec.converseList(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS), Long.class);
        if (CollectionUtils.isEmpty(productTagIds)) {
            return false;
        }
        return productTagIds.stream().anyMatch(anXinProductTagCodes::contains);
    }

    /**
     * 检查商品是否有安心标签
     * @param productM 商品信息
     * @param anXinTagCodes 安心标签ID
     * @return 是否有安心标签
     */
    private boolean hasAnXinTag(ProductM productM, List<Integer> anXinTagCodes) {
        String json = productM.getAttr(ATTR_GUARANTEE_TAG_CODES);
        if (StringUtils.isBlank(json)) {
            return false;
        }
        List<Integer> tagCodes = JsonCodec.converseList(json, Integer.class);
        if (CollectionUtils.isEmpty(tagCodes)) {
            return false;
        }
        if (CollectionUtils.isEmpty(anXinTagCodes)) {
            return false;
        }
        return tagCodes.stream().anyMatch(anXinTagCodes::contains);
    }

    private ProductTagBuildReq buildRequest(ActivityCxt context, ProductM productM) {
        ProductTagBuildReq req = new ProductTagBuildReq();
        req.setProductM(productM);
        req.setCtx(context);
        return req;
    }

    public StyleTextModel buildPreAfterPayTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreAfterTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }

    /**
     * 创建安心图片
     * @param config 配置
     * @return IconRichLabelModel
     */
    private com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel buildAnXinIcon(Config config) {
        if (StringUtils.isBlank(config.getAnXinIconUrl())) {
            return null;
        }
        com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel icon = new com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel();
        icon.setPicUrl(config.getAnXinIconUrl());
        icon.setAspectRadio(config.getAnXinIconAspectRatio());
        icon.setPicHeight(config.getAnXinIconHeight());
        com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel iconRichLabelModel = new com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel();
        iconRichLabelModel.setIcon(icon);
        iconRichLabelModel.setType(com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum.ICON.getType());
        return iconRichLabelModel;
    }

    /**
     * 获取营销标签放首位
     * @param context
     * @param param
     * @param config
     * @return
     */
    public ItemSubTitleVO getActivityTag(ActivityCxt context, Param param, Config config) {
        if (!config.isEnableActivityTag() || Objects.isNull(param.getProductM())) {
            return null;
        }
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())) {
            List<ProductActivityM> productActivityMS = param.getProductM().getActivities();
            if (CollectionUtils.isEmpty(productActivityMS)) {
                return null;
            }
            ProductActivityM activityM = getProductActivityM(productActivityMS);
            if (activityM == null) {
                return null;
            }
            Map<String, ActivityPicUrlDTO> activityPicUrlMap = activityM.getActivityPicUrlMap();
            ItemSubTitleVO activitySubTitleVO = new ItemSubTitleVO();
            // 间隔样式
            activitySubTitleVO.setJoinType(config.getNewJoinType());
            List<StyleTextModel> tags = new ArrayList<>();
            if (MapUtils.isNotEmpty(activityPicUrlMap)) {
                ActivityPicUrlDTO activityPicUrlDTO = activityPicUrlMap.get(ExposurePicUrlKeyEnum.ICON.getKey());
                if (activityPicUrlDTO != null && StringUtils.isNotBlank(activityPicUrlDTO.getUrl())) {
                    // 图片
                    activitySubTitleVO.setIconTag(buildActivityIcon(activityPicUrlDTO,config));
                    activitySubTitleVO.setTags(tags);
                    return activitySubTitleVO;
                }
                Map<String, String> textMap = activityM.getTextMap();
                // 业务级大促使用
                String labelText = textMap.get("labelText");
                if (StringUtils.isNotBlank(labelText)) {
                    StyleTextModel styleTextModel = buildRedBackground(labelText);
                    tags.add(styleTextModel);
                    activitySubTitleVO.setTags(tags);
                    return activitySubTitleVO;
                }
            }
        }
        return null;
    }

    private IconRichLabelModel buildActivityIcon(ActivityPicUrlDTO activityPicUrlDTO, Config config) {
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(activityPicUrlDTO.getUrl());
        pictureModel.setPicHeight(activityPicUrlDTO.getHeight() == null ? 16 : activityPicUrlDTO.getHeight() / 2);
        pictureModel.setAspectRadio(activityPicUrlDTO.getUrlAspectRadio());
        iconRichLabelModel.setIcon(pictureModel);
        iconRichLabelModel.setType(1);
        return iconRichLabelModel;
    }

    public ProductActivityM getProductActivityM(List<ProductActivityM> productActivityMS) {
        Map<Integer, ProductActivityM> activityMap = productActivityMS.stream().collect(Collectors
                .toMap(ProductActivityM::getExposurePromotionType, item -> item, (v1, v2) -> v1));
        for(Integer promotionType : MarketingActivityStrategy.exposurePromotionTypes) {
            ProductActivityM productActivityM = activityMap.get(promotionType);
            if (Objects.nonNull(productActivityM)) {
                return productActivityM;
            }
        }
        return null;
    }

    public String getRecommendTag(ProductM productM, Config config) {
        if(config==null || !config.isEnableRecommendTag()){
            return null;
        }
        // 竞争圈标签
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(productM);
        if (StringUtils.isNotEmpty(tradeRateTag)) {
            return tradeRateTag;
        }
        // UGC标签
        List<String> recommendTags = PriceAboveTagsUtils.getRecommendTag(productM);
        if (CollectionUtils.isNotEmpty(recommendTags)) {
            return recommendTags.get(0);
        }
        return null;
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemSubTitleVP.Config {

        // 一品多态品的最早可订时间
        private String RESERVE_TIME_KEY = "reserve_info";
        private String EARLIEST_BOOK_TIME = "earliestBookingTime";
        private String CHOSEN_BOOK_FORMAT_KEY = "currentAvailableFlag";

        /**
         * 静态副标题，用于配置直接返回
         */
        private String staticSubTitle;

        private boolean forceNull;

        /**
         * 使用爆品副标题
         */
        private boolean enableHotSpuTag;

        /**
         * 根据后台类目树配置副标题策略
         * 策略类型：{@link String}
         */
        private ShopCategoryConfig backCategory2Strategy;

        /**
         * 是否支持先用后付副标题展示
         */
        private boolean enableAfterPay;

        private String preAfterTag;

        private List<String> afterPayExps;

        /**
         * 安心价保标签ID
         */
        private List<Integer> anXinTagCodes;

        /**
         * 安心商品标签ID
         */
        private List<Long> anXinProductTagCodes;

        /**
         * 是否启用安心
         */
        private boolean enableAnXin = false;

        /**
         * 安心是否覆盖原副标题
         */
        private boolean anXinOverrideOriginalSubTitle = false;

        /**
         * 安心图片URL
         */
        private String anXinIconUrl;

        /**
         * 安心图片高度
         */
        private int anXinIconHeight;

        /**
         * 安心图片宽高比
         */
        private double anXinIconAspectRatio;

        /**
         * 安心说明文案
         */
        private List<String> anXinTextList;

        /**
         * 安心文案样式
         */
        private String anXinTextType = TextStyleEnum.TEXT_GRAY_RED.getType();

        /**
         * 安心文案拼接类型
         */
        private int anXinJoinType = SubTitleJoinTypeEnum.GRAY_RED_VERTICAL_LINE.getType();

        /**
         * 足疗新标签样式+有大促调整+副标题信息调整方案
         */
        private List<String> massageTagExpSks;

        private List<String> newJoinTypeExpSks;

        /**
         * 营销放首位标签开关
         */
        private boolean enableActivityTag = false;

        /**
         * 是否启用推荐标签
         */
        private boolean enableRecommendTag;

        private int newJoinType;
    }
}
