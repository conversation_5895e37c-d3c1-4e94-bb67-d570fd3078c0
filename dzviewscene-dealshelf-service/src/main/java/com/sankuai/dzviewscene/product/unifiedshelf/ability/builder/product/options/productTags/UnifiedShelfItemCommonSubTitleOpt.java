package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.SubTitleJoinTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "默认-富文本副标题", description = "返回团单标签副标题", code = "UnifiedShelfItemCommonSubTitleOpt", isDefault = true)
public class UnifiedShelfItemCommonSubTitleOpt extends UnifiedShelfItemSubTitleVP<UnifiedShelfItemCommonSubTitleOpt.Config> {

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    public static final String ATTR_VALID_PRODUCT_TAG_IDS = "attr_validProductTagIds";

    private static final Integer PRODUCT_CATEGORY = 2200073;

    private static final String SHI_YONG_WEN_TI = "shiyongwenti";

    /**
     * 保价attr
     */
    public static final String ATTR_GUARANTEE_TAG_CODES = "attr_guaranteeTagCodes";

    @Autowired
    private ProductTagStrategyFactory productTagStrategyFactory;

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param param, Config config) {
        if (config.isForceNull()) {
            return null;
        }
        // 处理普通副标题逻辑
        ItemSubTitleVO commonSubTitle = buildCommonSubTitle(context, param, config);
        // 处理安心逻辑
        ItemSubTitleVO anXinSubTitle = buildAnXinSubTitle(param, config);
        // 组装
        if (anXinSubTitle != null) {
            // 安心逻辑
            if (config.isAnXinOverrideOriginalSubTitle()) {
                // 安心优先
                return anXinSubTitle;
            }
            anXinSubTitle.getTags().addAll(commonSubTitle.getTags());
            return anXinSubTitle;
        }
        return commonSubTitle;
    }

    private ItemSubTitleVO buildCommonSubTitle(ActivityCxt context, Param param, Config config) {
        // 类目树路由策略副标题
        List<StyleTextModel> strategyTags = getStrategyTags(context, param.getProductM(), config);
        if (CollectionUtils.isNotEmpty(strategyTags)) {
            ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
            itemSubTitleVO.setJoinType(0);
            itemSubTitleVO.setTags(strategyTags);
            return itemSubTitleVO;
        }
        List<String> productTags = getProductTags(context, param.getProductM(), config);
        ItemSubTitleVO itemSubTitleVO = build4Default(productTags);
        if (config.isEnableAfterPay() && ProductMAttrUtils.timeCardIsShowAfterPayTag(param.getProductM(),context,config.getAfterPayExps())) {
            StyleTextModel afterPayTag = buildPreAfterPayTag(config);
            itemSubTitleVO = itemSubTitleVO == null ? new ItemSubTitleVO() : itemSubTitleVO;
            itemSubTitleVO.setJoinType(0);
            List<StyleTextModel> tags = itemSubTitleVO.getTags() == null ? new ArrayList<>() : itemSubTitleVO.getTags();
            tags.add(0, afterPayTag);
            itemSubTitleVO.setTags(tags);
        }
        tryQueryHighLight(context, itemSubTitleVO, config.isDisableHighlight());
        return itemSubTitleVO;
    }

    private List<StyleTextModel> getStrategyTags(ActivityCxt context, ProductM productM, Config config) {
        String strategy = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2Strategy(), String.class);
        if(StringUtils.isNotBlank(strategy)){
            ProductTagStrategy buildStrategy = productTagStrategyFactory.getProductTagStrategy(strategy);
            if (Objects.nonNull(buildStrategy)) {
                ProductTagBuildReq req = buildRequest(context, productM);
                List<StyleTextModel> build = buildStrategy.build(req);
                if (CollectionUtils.isNotEmpty(build)) {
                    return build;
                }
            }
        }
        return null;
    }

    private ItemSubTitleVO buildAnXinSubTitle(Param param, Config config) {
        if (!config.isEnableAnXin()) {
            return null;
        }
        if (!hasAnXinTag(param.getProductM(), config.getAnXinTagCodes()) && !hasAnXinProductTag(param.getProductM(), config.getAnXinProductTagCodes())) {
            return null;
        }
        ItemSubTitleVO anXinSubTitleVO = new ItemSubTitleVO();
        // 间隔样式
        anXinSubTitleVO.setJoinType(config.getAnXinJoinType());
        // 图片
        anXinSubTitleVO.setIconTag(buildAnXinIcon(config));
        // 文案说明
        if (CollectionUtils.isNotEmpty(config.getAnXinTextList())) {
            List<StyleTextModel> tags = config.getAnXinTextList().stream().map(tag -> {
                StyleTextModel styleTextModel = new StyleTextModel();
                styleTextModel.setText(tag);
                styleTextModel.setStyle(config.getAnXinTextType());
                return styleTextModel;
            }).collect(Collectors.toList());
            anXinSubTitleVO.setTags(tags);
        }
        return anXinSubTitleVO;
    }

    private List<String> getProductTags(ActivityCxt context, ProductM productM, Config config){
        List<String> result = Lists.newArrayList();
        //静态副标题
        if (StringUtils.isNotBlank(config.getStaticSubTitle())) {
            return Lists.newArrayList(config.getStaticSubTitle());
        }
        //美团放心改
        if (isFangxinChange(productM)) {
            return Lists.newArrayList(getSKUApplicableIssues(productM));
        }
        //预售单逻辑
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            String preSaleDate = PreSaleUtils.getPreSaleDateProductTag(productM);
            if(StringUtils.isNotBlank(preSaleDate)){
                return Lists.newArrayList(preSaleDate);
            }
        }
        //爆品副标题
        if (config.isEnableHotSpuTag() && ProductMAttrUtils.hotSpu(productM)) {
            List<String> hotSpuTags = ProductMAttrUtils.getHotSpuSpecialProductTags(productM);
            if (CollectionUtils.isNotEmpty(hotSpuTags)) {
                result.addAll(hotSpuTags);
                return result;
            }
        }
        //通用副标题
        if(CollectionUtils.isNotEmpty(productM.getProductTags())){
            result.addAll(productM.getProductTags());
        }
        return result;
    }

    private String getSKUApplicableIssues(ProductM productM) {
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        return allSkus.stream()
                .filter(sku -> PRODUCT_CATEGORY == sku.getProductCategory())
                .findFirst()
                .map(this::extractApplicableIssues)
                .orElse(null);
    }

    private String extractApplicableIssues(SkuItemDto skuItemDto) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        return skuItemDto.getAttrItems().stream()
                .filter(attr -> SHI_YONG_WEN_TI.equals(attr.getAttrName()))
                .findFirst()
                .map(SkuAttrItemDto::getAttrValue)
                .orElse(null);
    }

    private boolean isFangxinChange(ProductM productM) {
        return "true".equals(productM.getAttr(IS_FANGXIN_CHANGE));
    }

    /**
     * 检查商品是否有安心标签
     * @param productM 商品信息
     * @param anXinTagCodes 安心标签ID
     * @return 是否有安心标签
     */
    private boolean hasAnXinProductTag(ProductM productM, List<Long> anXinProductTagCodes) {
        if (CollectionUtils.isEmpty(anXinProductTagCodes) || StringUtils.isBlank(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS))) {
            return false;
        }
        List<Long> productTagIds = JsonCodec.converseList(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS), Long.class);
        if (CollectionUtils.isEmpty(productTagIds)) {
            return false;
        }
        return productTagIds.stream().anyMatch(anXinProductTagCodes::contains);
    }

    /**
     * 检查商品是否有安心标签
     * @param productM 商品信息
     * @param anXinTagCodes 安心标签ID
     * @return 是否有安心标签
     */
    private boolean hasAnXinTag(ProductM productM, List<Integer> anXinTagCodes) {
        String json = productM.getAttr(ATTR_GUARANTEE_TAG_CODES);
        if (StringUtils.isBlank(json)) {
            return false;
        }
        List<Integer> tagCodes = JsonCodec.converseList(json, Integer.class);
        if (CollectionUtils.isEmpty(tagCodes)) {
            return false;
        }
        if (CollectionUtils.isEmpty(anXinTagCodes)) {
            return false;
        }
        return tagCodes.stream().anyMatch(anXinTagCodes::contains);
    }

    private ProductTagBuildReq buildRequest(ActivityCxt context, ProductM productM) {
        ProductTagBuildReq req = new ProductTagBuildReq();
        req.setProductM(productM);
        req.setCtx(context);
        return req;
    }

    public StyleTextModel buildPreAfterPayTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreAfterTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }

    /**
     * 创建安心图片
     * @param config 配置
     * @return IconRichLabelModel
     */
    private com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel buildAnXinIcon(Config config) {
        if (StringUtils.isBlank(config.getAnXinIconUrl())) {
            return null;
        }
        com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel icon = new com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel();
        icon.setPicUrl(config.getAnXinIconUrl());
        icon.setAspectRadio(config.getAnXinIconAspectRatio());
        icon.setPicHeight(config.getAnXinIconHeight());
        com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel iconRichLabelModel = new com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel();
        iconRichLabelModel.setIcon(icon);
        iconRichLabelModel.setType(com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum.ICON.getType());
        return iconRichLabelModel;
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemSubTitleVP.Config {

        /**
         * 静态副标题，用于配置直接返回
         */
        private String staticSubTitle;

        private boolean forceNull;

        /**
         * 使用爆品副标题
         */
        private boolean enableHotSpuTag;

        /**
         * 根据后台类目树配置副标题策略
         * 策略类型：{@link String}
         */
        private ShopCategoryConfig backCategory2Strategy;

        /**
         * 是否支持先用后付副标题展示
         */
        private boolean enableAfterPay;

        private String preAfterTag;

        private List<String> afterPayExps;

        /**
         * 安心价保标签ID
         */
        private List<Integer> anXinTagCodes;

        /**
         * 安心商品标签ID
         */
        private List<Long> anXinProductTagCodes;

        /**
         * 是否启用安心
         */
        private boolean enableAnXin = false;

        /**
         * 安心是否覆盖原副标题
         */
        private boolean anXinOverrideOriginalSubTitle = false;

        /**
         * 安心图片URL
         */
        private String anXinIconUrl;

        /**
         * 安心图片高度
         */
        private int anXinIconHeight;

        /**
         * 安心图片宽高比
         */
        private double anXinIconAspectRatio;

        /**
         * 安心说明文案
         */
        private List<String> anXinTextList;

        /**
         * 安心文案样式
         */
        private String anXinTextType = TextStyleEnum.TEXT_GRAY_RED.getType();

        /**
         * 安心文案拼接类型
         */
        private int anXinJoinType = SubTitleJoinTypeEnum.GRAY_RED_VERTICAL_LINE.getType();
    }
}
