package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.AbstractPriceBottomTagBuildStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 优惠感知标签策略抽象类
 */
public abstract class AbstractPriceBottomPromoTagBuildStrategy extends AbstractPriceBottomTagBuildStrategy {

    public static final String COMMON_AFTER_PIC = "https://p0.meituan.net/travelcube/19618b66b73b71701ce1d62509b4fb49393.png";

    public static final String COMMON_AFTER_PIC_DP = "https://p1.meituan.net/travelcube/a58b285ac916920524ba73908bde5151474.png";

    public static final String MEMBER_AFTER_PIC = "https://p1.meituan.net/travelcube/03ff48944174e7edd6dfc5ba7cbd73a5613.png";

    public static final String MEMBER_AFTER_PIC_DP = "https://p0.meituan.net/travelcube/611f1072cf49e8a45b686ab4ff8f0511534.png";

    public static String getCommonAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return COMMON_AFTER_PIC;
        }
        return COMMON_AFTER_PIC_DP;
    }

    public static String getMemberAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return MEMBER_AFTER_PIC;
        }
        return MEMBER_AFTER_PIC_DP;
    }

    @Override
    public ShelfTagVO build(PriceBottomTagBuildReq req) {
        //优惠感知都必须要有优惠信息，这里统一校验了，特殊情况可以不继承该类
        if (req == null || req.getProductM() == null
                || CollectionUtils.isEmpty(req.getProductM().getPromoPrices())) {
            return null;
        }
        return buildTag(req);
    }

    public RichLabelModel buildCommonTagText(String text, String preText, boolean hiddenPreText) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        text = text.replace("¥", "");
        List<String> multiText = StringUtils.isNotEmpty(preText) &&!hiddenPreText ? Lists.newArrayList(preText, text) : Lists.newArrayList(text);
        return buildMultiText(RichLabelStyleEnum.ROUND_CORNER.getType(), text, multiText, ColorUtils.colorFF4B10, ColorUtils.colorFFF1EC);
    }

    public RichLabelModel buildMemberTagText(String text, String preText, boolean hiddenPreText) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        text = text.replace("¥", "");
        List<String> multiText = StringUtils.isNotEmpty(preText) && !hiddenPreText ? Lists.newArrayList(preText, text) : Lists.newArrayList(text);
        return buildMultiText(RichLabelStyleEnum.ROUND_CORNER.getType(), text, multiText, ColorUtils.color8E3C12, ColorUtils.colorFFEDDE);
    }

    public void addAfterPic(ShelfTagVO shelfTagVO, String icon) {
        if (shelfTagVO.getPromoDetail() == null) {
            return;
        }
        shelfTagVO.setAfterPic(buildPicModel(icon, 1.1, 10));
    }

    public PictureModel buildPicModel(String url, double aspectRadio, int height) {
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(url);
        pictureModel.setAspectRadio(aspectRadio);
        pictureModel.setPicHeight(height);
        return pictureModel;
    }

    public RichLabelModel buildNormalText(int style, String text, String textColor, String backgroundColor) {
        RichLabelModel label = new RichLabelModel();
        label.setText(text);
        label.setMultiText(Lists.newArrayList(text));
        label.setTextColor(textColor);
        label.setBackgroundColor(backgroundColor);
        label.setStyle(style);
        return label;
    }

    public RichLabelModel buildMultiText(int style, String text, List<String> multiText, String textColor, String backgroundColor) {
        RichLabelModel label = new RichLabelModel();
        label.setText(text);
        label.setMultiText(multiText);
        label.setTextColor(textColor);
        label.setBackgroundColor(backgroundColor);
        label.setStyle(style);
        return label;
    }
}
