package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.vp.UnifiedShelfMainTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum;
import lombok.Data;

import java.util.List;

import static com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.UnifiedShelfMainTitleBuilder.getTotalCount;

@VPointOption(name = "丽人养发预订货架主标题",
        description = "丽人养发预订货架主标题配置能力",
        code = "BeautyDealReserveShelfMainTitleOpt")
public class BeautyDealReserveShelfMainTitleOpt extends UnifiedShelfMainTitleVP<BeautyDealReserveShelfMainTitleOpt.Config> {

    @Override
    public ShelfMainTitleVO compute(ActivityCxt context, Param param, Config config) {
        ShelfMainTitleVO shelfMainTitleVO = new ShelfMainTitleVO();
        if (config == null) {// 没有配置的时候默认走兜底逻辑
            return null;
        }
        shelfMainTitleVO.setTitle(config.getTitle());
        shelfMainTitleVO.setTotalProductQty(getTotalCount(context));
        shelfMainTitleVO.setTags(buildTagList(config));
        SupernatantVO supernatantVO = new SupernatantVO();
        supernatantVO.setTitle(config.getSupernatantTitle());
        supernatantVO.setContentList(buildSupernatantList(config));
        shelfMainTitleVO.setSupernatantVO(supernatantVO);
        return shelfMainTitleVO;
    }

    /**
     * 主标题构造 预订必上门·上门前3小时随时退·{商家确认时效}
     *
     * @param config
     * @param
     * @return
     */
    private List<IconRichLabelModel> buildTagList(Config config) {
        List<IconRichLabelModel> tagList = Lists.newArrayList();
        tagList.add(buildTag(config.getTagIcon1(), config.getTagText1(), IconRichLabelTypeEnum.ICON_TEXT.getType()));
        tagList.add(buildTag(config.getTagIcon2(), null, IconRichLabelTypeEnum.ICON.getType()));
        return tagList;
    }

    private IconRichLabelModel buildTag(String tagIcon, String tagText, int type) {
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        iconRichLabelModel.setIcon(buildPictureLabel(tagIcon));
        iconRichLabelModel.setText(buildRichLabel(tagText));
        iconRichLabelModel.setType(type);
        return iconRichLabelModel;
    }

    private PictureModel buildPictureLabel(String tagIcon) {
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(tagIcon);
        return pictureModel;
    }

    private RichLabelModel buildRichLabel(String tagText) {
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(tagText);
        return richLabelModel;
    }

    /**
     * 构造浮层文案
     *
     * @param config
     * @param
     * @return
     */
    private List<SupernatantAreaVO> buildSupernatantList(Config config) {
        List<SupernatantAreaVO> tagList = Lists.newArrayList();
        tagList.add(buildAreaTag(config.getAreaTagIcon(), config.getAreaTitle1(), IconRichLabelTypeEnum.TEXT.getType(), config.getAreaText1()));
        tagList.add(buildAreaTag(config.getAreaTagIcon(), config.getAreaTitle2(), IconRichLabelTypeEnum.TEXT.getType(), config.getAreaText2()));
        return tagList;
    }

    private SupernatantAreaVO buildAreaTag(String tagIcon, String tagText, int type, List<String> areaText) {
        SupernatantAreaVO supernatantAreaVO = new SupernatantAreaVO();
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        iconRichLabelModel.setIcon(buildPictureLabel(tagIcon));
        iconRichLabelModel.setText(buildRichLabel(tagText));
        iconRichLabelModel.setType(type);
        supernatantAreaVO.setHeadLabelModel(iconRichLabelModel);
        supernatantAreaVO.setAreaTextList(areaText);
        return supernatantAreaVO;
    }


    @VPointCfg
    @Data
    public static class Config {

        private String title = "预订";

        private String tagIcon1 = "https://p0.meituan.net/ingee/532058a3a9762dbde44c34155d5b18b61269.png";
        private String tagText1 = "约得到·不等位.提前退";
        private String tagIcon2 = "https://p0.meituan.net/ingee/b9c69b35a9af93532b84146ea00e1e1a481.png";


        private String supernatantTitle = "预订说明";

        private String areaTagIcon = "https://p0.meituan.net/ingee/03eeb9ab4ca0a3ad9b1ea63600c953261435.png";

        private String areaTitle1 = "平台保证";

        private List<String> areaText1 = Lists.newArrayList("提前预订免排队，到店就能做。");

        private String areaTitle2 = "退改规则";

        private List<String> areaText2 = Lists.newArrayList("预订成功后，平台暂不支持修改订单，如您有时间调整等需求，请联系商家进行协商。", "您在预订成功后，截止预订时间30分钟前，支持随时退款，免商家审核；截止预订时间30分钟内，需要商家审核通过后才能退款。");
    }
}
