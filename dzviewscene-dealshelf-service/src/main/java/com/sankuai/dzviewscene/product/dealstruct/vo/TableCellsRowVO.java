package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/28 3:54 下午
 */
@MobileDo(id = 0xc43a)
public class TableCellsRowVO implements Serializable {
    /**
     * 表单元行，因移动之家不支持二维数组，为构造表故构造此VO
     */
    @MobileDo.MobileField(key = 0xdada)
    private List<TableCellVO> tableCellsRow;

    public List<TableCellVO> getTableCellsRow() {
        return tableCellsRow;
    }

    public void setTableCellsRow(List<TableCellVO> tableCellsRow) {
        this.tableCellsRow = tableCellsRow;
    }
}
