package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 先用后付副标题处理
 */
@Component
public class AfterPayTagProcessHandler implements AggregatePostHandler {

    private static String AFTER_PAY_TAG = "先用后付";
    // "afterPayExps":["EXP2025032100003_c"]
    private static final List<String> afterPayExps = Lists.newArrayList("EXP2025032100003_c");

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.massageTag.newStyle.expSks", defaultValue = "[]")
    private List<String> massageTagNewStyleExpSks;

    @Override
    public String getName() {
        return AfterPayTagProcessHandler.class.getSimpleName();
    }

    @Override
    public ShelfItemVO process(ActivityCxt activityCxt, AggregateHandlerContext context) {
        ShelfItemVO shelfItemVO = context.getShelfItemVO();
        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        if(CollectionUtils.isNotEmpty(massageTagNewStyleExpSks) && DouHuUtils.hitAnySk(douHuMList, massageTagNewStyleExpSks)){
            //命中新样式逻辑副标题不会再置空，不需要后置处理
            return shelfItemVO;
        }
        if(ProductHierarchyUtils.isSpuNode(shelfItemVO)){
            appendAfterPayProductTag(shelfItemVO, context.getNodeM(), activityCxt);
        }
        return shelfItemVO;
    }

    public void appendAfterPayProductTag(ShelfItemVO shelfItemVO, ProductHierarchyNodeM nodeM, ActivityCxt activityCxt) {
        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        if (!UserAfterPayPaddingHandler.getUserExposure(activityCxt) || !DouHuUtils.hitAnySk(douHuMList,afterPayExps)) {
            return;
        }
        List<ShelfItemVO> subItems = shelfItemVO.getSubItems();
        Map<Long, ProductM> childProductMMap = nodeM.getChildren().stream().filter(v -> v.getProductM() != null)
                .collect(Collectors.toMap(v -> (long)v.getProductM().getProductId(), ProductHierarchyNodeM::getProductM, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(subItems)) {
            subItems.forEach(subItem -> toAppend(subItem, childProductMMap.get(subItem.getItemId())));
        }
    }

    private void toAppend(ShelfItemVO shelfItemVO, ProductM productM) {
        if ((productM.getProductType() == ProductTypeEnum.TIME_CARD.getType() || productM.isTimesDeal()) && ProductMAttrUtils.isAfterPayProduct(productM)) {
            ItemSubTitleVO itemSubTitleVO = appendAfterPaySpuTag(shelfItemVO.getProductTags());
            shelfItemVO.setProductTags(itemSubTitleVO);
        }
    }

    public ItemSubTitleVO appendAfterPaySpuTag(ItemSubTitleVO productTags) {
        StyleTextModel afterPayTag = buildPreAfterPayTag();
        if (productTags == null || CollectionUtils.isEmpty(productTags.getTags())) {
            productTags = new ItemSubTitleVO();
            productTags.setJoinType(0);
            productTags.setTags(Lists.newArrayList(afterPayTag));
        } else {
            List<StyleTextModel> tags = productTags.getTags();
            tags.add(0, afterPayTag);
            productTags.setTags(tags);
        }
        return productTags;
    }

    public StyleTextModel buildPreAfterPayTag() {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(AFTER_PAY_TAG);
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }
}