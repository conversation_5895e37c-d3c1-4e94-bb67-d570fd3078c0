package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaMoreTextVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "商品区-默认更多展示文案",
        description = "支持format自定义前后缀，默认展示「更多X个团购」 ",
        code = DefaultProductAreaMoreTextOpt.CODE,
        isDefault = true)
public class DefaultProductAreaMoreTextOpt extends ProductAreaMoreTextVP<DefaultProductAreaMoreTextOpt.Config> {

    public static final String CODE = "DefaultProductAreaMoreTextOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        int totalNum = param.getItemAreaItemCnt();
        if (!config.isEnableIgnoreDefaultShowNum() && totalNum <= param.getDefaultShowNum()) {
            return StringUtils.EMPTY;
        }
        return config.isUseTotal() ? getTotalText(totalNum, config.getTextFormat()) : getRemainText(totalNum, param.getDefaultShowNum(), config.getTextFormat());
    }

    @VPointCfg
    @Data
    public static class Config {

        private String textFormat;

        /**
         * 是否使用全部数
         */
        private boolean useTotal;

        /**
         * 是否忽略默认展示数量
         */
        private boolean enableIgnoreDefaultShowNum;
    }
}
