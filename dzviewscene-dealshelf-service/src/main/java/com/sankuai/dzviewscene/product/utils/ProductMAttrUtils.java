package com.sankuai.dzviewscene.product.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuAttrDTO;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DataUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

/**
 * ProductM Attr 的属性工具
 * 注：不对 productM 判空
 *
 * <AUTHOR>
 * @date 2022/3/30
 */
public class ProductMAttrUtils {

    public static final String SHOP_RECOMMEND = "attr_shopRecommend";

    public static final String SHOP_RECOMMEND_NAV_ID = "attr_shopRecommend_navIds";

    public static final String TOP_DISPLAY_PRODUCT = "attr_topDisplayProduct";

    public static final String SERVICE_TYPE = "service_type";

    //standardDealGroup 是判断是不是标品的
    //standardDealGroupKey 是描述是哪种标品
    private static final String STANDARD_DEAL_GROUP_KEY = "standardDealGroupKey";
    // 判断爆品
    private static final String HOT_SPU_KEY = "topPerformingProduct";

    private static final String ATTR_VALID_PRODUCT_TAG_ID = "attr_validProductTagIds";

    /**
     * 团单类型-代金券 or 普通团单
     */
    private static final String PRODUCT_TYPE = "productType";

    public static final String DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    //价格力标签
    public static final String PRICE_POWER_TAG_ATTR = "highestPriorityPricePowerTagAttr";

    //复购标签
    public static final String REPURCHASE_TAG = "dealRepurchaseTag";

    // 可重复买标签
    public static final String REPURCHASE_TITLE_TAG = "dealRepurchaseTitleTag";

    public static final String HOT_SELL_PRODUCT = "hot_sell_product";

    public static final String WEDDING_DUITOU_PRODUCT = "wedding_duitou_product";

    /**
     * 爆品标题
     */
    public static final String HOT_SPU_SPECIAL_PRODUCT_TAGS = "attr_hot_spu_shelf_sub_tag";

    public static final String DEAL_HOT_SPU_CHECK_ATTR = "dealHotSpuCheckAttr";

    private static final String TRUE = "true";

    /**
     * 丽人不可复购标签
     */
    public static final String BEAUTY_CAN_REPEAT_PURCHASE_PRODUCT = "BeautyCantRepeatPurchaseProduct";

    /**
     * 透穿团详头图-前端预加载使用
     */
    public static final String DEAL_DEFAULT_PIC_PATH = "defaultPicPath";

    public static final String ATTR_KEY_AVAILABLE_TIME = "available_time";

    /**
     * 判断团单是否为商家推荐团单
     * 并行填充请用isShopRecommend(ProductM productM, long filterId)
     * @param productM
     * @return true：是
     */
    public static boolean isShopRecommend(ProductM productM) {
        return Boolean.valueOf(productM.getAttr(SHOP_RECOMMEND));
    }

    /**
     * 判断团单是否为导航下商家推荐团单
     * @param productM
     * @param filterId
     * @return
     */
    public static boolean isShopRecommend(ProductM productM, long filterId) {
        String navIdsStr = productM.getAttr(SHOP_RECOMMEND_NAV_ID);
        if(StringUtils.isBlank(navIdsStr) || filterId <= 0){
            return isShopRecommend(productM);
        }
        List<Long> navIds = JsonCodec.decode(navIdsStr, new TypeReference<List<Long>>() {});
        //包含Integer.MAX_VALUE代表所有导航下都推荐
        return CollectionUtils.isNotEmpty(navIds) && (navIds.contains(filterId) || navIds.contains((long)Integer.MAX_VALUE));
    }

    /**
     * 判断团单是否为联合星品团单
     * @param productM
     * @return
     */
    public static boolean isUniteStarProduct(ProductM productM){
        String uniteStarProduct = productM.getAttr("beautyUniteStarProduct");
        return BooleanUtils.toBoolean(uniteStarProduct);
    }

    /**
     * 判断商品是否支持先用后付
     * @param productM
     * @return
     */
    public static boolean isAfterPayProduct(ProductM productM){
        String afterPayProduct = productM.getAttr("pay_method");
        int value = NumberUtils.toInt(afterPayProduct, 0);
        // 4 支持先用后付
        return value == 4;
    }

    public static boolean isTimeCard(ProductM productM) {
        return productM.getProductType() == ProductTypeEnum.TIME_CARD.getType();
    }

    public static boolean timeCardIsShowAfterPayTag(ProductM productM, ActivityCxt context, List<String> afterPayExps) {
        if (productM.getProductType() != ProductTypeEnum.TIME_CARD.getType() && !productM.isTimesDeal()) {
            return false;
        }
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (isAfterPayProduct(productM) && UserAfterPayPaddingHandler.getUserExposure(context) && DouHuUtils.hitAnySk(douHuMList, afterPayExps)) {
            return true;
        }
        return false;
    }

    public static boolean dealTimesCardSupportCreditPay(ProductM productM, ActivityCxt context) {
        if (productM.getProductType() != ProductTypeEnum.TIME_CARD.getType() && !productM.isTimesDeal()) {
            return false;
        }
        String userExposureStr = context.getParam(PaddingFetcher.Params.userExposure);
        boolean userExposure = StringUtils.equals(ExposureEnum.EXPOSED.getCode(), userExposureStr);
        return isAfterPayProduct(productM) && userExposure;
    }

    /**
     * 判断团单是否为堆头商品
     *
     * @param productM
     * @return true：是
     */
    public static boolean isTopDisplayProduct(ProductM productM) {
        return Boolean.parseBoolean(productM.getAttr(TOP_DISPLAY_PRODUCT));
    }

    /**
     * 判断团单是否丽人不可复购 true-不可复购
     * @param productM
     * @return
     */
    public static boolean isBeautyCantRepeatPurchaseProduct(ProductM productM){
        return Boolean.parseBoolean(productM.getAttr(BEAUTY_CAN_REPEAT_PURCHASE_PRODUCT));
    }

    /**
     * 判断团单是否为摄影行业独立堆头商品
     *
     * @param productM
     * @return true：是
     */
    public static boolean isPhotoDuitouProduct(ProductM productM) {
        return Boolean.parseBoolean(productM.getAttr(WEDDING_DUITOU_PRODUCT));
    }

    /**
     * 判断团单是否为摄影行业堆头商品
     *
     * @param productM
     * @return true：是
     */
    public static boolean isPhotoTopDisplayProduct(ProductM productM) {
        return Boolean.parseBoolean(productM.getAttr(HOT_SELL_PRODUCT));
    }


    /**
     * 获取 serviceType 信息（即产品说的团单三级分类或服务类型）
     *
     * @param productM
     * @return
     */
    public static String getServiceType(ProductM productM) {
        return productM.getAttr(SERVICE_TYPE);
    }

    /**
     * 获取 productType 信息
     *
     * @param productM
     * @return
     */
    public static String getProductType(ProductM productM) {return productM.getAttr(PRODUCT_TYPE);}

    /**
     * 是否为标准单
     */
    public static boolean isStandardDealGroup(ProductM productM) {
        String standardValue = getStandardDealGroupValue(productM);
        return StringUtils.isNotEmpty(standardValue) && !Objects.equals(standardValue, "face_clean");
    }

    /**
     * 若为标品，则会返回
     * 美容：faceclean_standard
     * 美甲：meijia-solid-color-manicure-standard
     *
     * @return
     */
    public static String getStandardDealGroupValue(ProductM productM) {
        return productM.getAttr(STANDARD_DEAL_GROUP_KEY);
    }

    /**
     * 是否为爆品、超团、或其他类型的SPU
     */
    public static boolean standardSpu(ProductM productM) {
        return hotSpu(productM) || hotSpuCheck(productM) || superSpu(productM);
    }

    /**
     * 是否为爆品
     */
    public static boolean hotSpu(ProductM productM) {
        return hotSpu0(productM) || hotSpuCheck(productM);
    }

    /**
     * 是否为爆品
     */
    private static boolean hotSpu0(ProductM productM) {
        if (productM == null) {
            return false;
        }
        return NumberUtils.toLong(productM.getAttr(HOT_SPU_KEY)) > 0;
    }

    /**
     * 是否超级团购
     */
    public static boolean superSpu(ProductM productM) {
        if (productM == null) {
            return false;
        }
        String mtssRefSpuId = productM.getAttr("mtss_ref_spu_id");
        long spuSceneType = NumberUtils.toLong(productM.getAttr("spuSceneType"));
        long spuId = NumberUtils.toLong(mtssRefSpuId);
        return spuId>0 && 17 == spuSceneType;
    }

    private static boolean hotSpuCheck(ProductM productM){
        if (productM == null) {
            return false;
        }
        return TRUE.equalsIgnoreCase(productM.getAttr(DEAL_HOT_SPU_CHECK_ATTR));
    }

    public static String getStandardSpuName(ProductM productM) {
        if (productM == null || productM.getSpuM() == null) {
            return null;
        }
        return productM.getSpuM().getSpuName();
    }

    public static String getStandardSpuPic(ProductM productM) {
        if (productM == null || productM.getSpuM() == null) {
            return null;
        }
        return productM.getSpuM().getHeadPic();
    }

    public static List<String> getStandardSpuSpecialProductTags(ProductM productM) {
        //联合星品
        // 超团的获取方式不一样
        if (superSpu(productM)) {
            return getSuperDealProductTags(productM);
        }
        return getHotSpuSpecialProductTags(productM);
    }

    public static List<String> getSuperDealProductTags(ProductM productM) {
        if (Objects.isNull(productM) || Objects.isNull(productM.getSpuM())) {
            return Lists.newArrayList();
        }
        return productM.getSpuM().getSpuKeyInformation();
    }

    public static List<String> getHotSpuSpecialProductTags(ProductM productM) {
        if (Objects.isNull(productM) || Objects.isNull(productM.getSpuM()) || CollectionUtils.isEmpty(productM.getSpuM().getAttrList())) {
            return new ArrayList<>();
        }
        Optional<DealProductSpuAttrDTO> hotSpuProductTagAttr = productM.getSpuM().getAttrList().stream().filter(a ->
                Objects.nonNull(a) && a.getName().equals(HOT_SPU_SPECIAL_PRODUCT_TAGS)).findFirst();
        if(!hotSpuProductTagAttr.isPresent()){
            return new ArrayList<>();
        }
        return JSON.parseArray((String) hotSpuProductTagAttr.get().getValue(), String.class);
    }

    /**
     * @param productM
     * @return 团单关联的标签，返回字符串
     */
    public static String getProductTagIdStr(ProductM productM) {
        return productM.getAttr(ATTR_VALID_PRODUCT_TAG_ID);
    }

    /**
     * 团单关联的标签，返回字符串
     */
    public static List<Long> getProductTagIds(ProductM productM) {
        String productTagIdStr = getProductTagIdStr(productM);
        if (StringUtils.isEmpty(productTagIdStr)) {
            return Lists.newArrayList();
        }
        if (JSONValidator.from(productTagIdStr).validate()) {
            return JSON.parseArray(productTagIdStr, Long.class);
        }
        return DataUtils.toLongList(productTagIdStr, ",");
    }

    /**
     * @param productM
     * @return 是否为代金券，true - 是
     */
    public static boolean isVoucher(ProductM productM) {
        return Objects.equals(productM.getAttr(PRODUCT_TYPE), "voucher");
    }

    /**
     * dealAttrs中的属性key和value是否满足入参satisfyAllAttrKeyValuesMap条件
     *
     * @param dealAttrs
     * @param satisfyAllAttrKeyValuesMap
     * @return
     */
    public static boolean satisfyAllAttrKeyValue(List<AttrM> dealAttrs, Map<String, List<String>> satisfyAllAttrKeyValuesMap) {
        //satisfyAllAttrKeyValuesMap无效说明不做校验，返回true
        if (MapUtils.isEmpty(satisfyAllAttrKeyValuesMap)) {
            return true;
        }
        //商品关联的行业属性无效，返回false
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        //商品关联的行业属性命中配置的所有属性key和value才会返回true
        return satisfyAllAttrKeyValuesMap.entrySet()
                .stream()
                .filter(Objects::nonNull)
                .allMatch(entry -> {
                    String attrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, entry.getKey());
                    return StringUtils.isNotEmpty(attrValue) && CollectionUtils.isNotEmpty(entry.getValue()) && entry.getValue().contains(attrValue);
                });
    }

    /**
     * 商品的某个服务项目满足是否满足入参satisfyAllAttrKeyValuesMap条件
     *
     * @param skuItemDto
     * @param satisfySkuAllAttrKeyValuesMap
     * @return
     */
    public static boolean satisfyAllSkuAttrKeyValue(SkuItemDto skuItemDto, Map<String, List<String>> satisfySkuAllAttrKeyValuesMap) {
        //配置无效说明不做校验，返回true
        if (MapUtils.isEmpty(satisfySkuAllAttrKeyValuesMap)) {
            return true;
        }
        //商品服务项目关联的属性列表无效，返回false
        if (skuItemDto == null && CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return false;
        }
        //商品服务项目关联的属性列表命中配置的所有属性key和value才会返回true
        return satisfySkuAllAttrKeyValuesMap.entrySet()
                .stream()
                .filter(Objects::nonNull)
                .allMatch(entry -> {
                    String attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), entry.getKey());
                    return StringUtils.isNotEmpty(attrValue) && CollectionUtils.isNotEmpty(entry.getValue()) && entry.getValue().contains(attrValue);
                });
    }

    public static String getAttrValue(ProductM productM, String attrKey) {
        if (productM == null) {
            return null;
        }
        return productM.getAttr(attrKey);
    }

    public static List<String> getAttrValueList(ProductM productM, String attrKey) {
        if (productM == null) {
            return null;
        }
        return productM.getAttrList(attrKey);
    }

}
