package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

public class UnifiedShelfPromoUtils {

    /**
     * 优惠弹窗的标题
     */
    private static final String PROMO_POP_TITLE = "优惠明细";

    public static final String MAGICAL_MEMBER_TAG_NAME = "神券";

    private static final int MAGICAL_MEMBER_COUPON = 1;

    private static final int MAGICAL_MEMBER_COUPON_FREE = 2;

    private static final String EXT_DISPLAY_PROMO_TAG_KEY = "promotionDetailMagicalMemberCouponText";

    private static final String MAGICAL_MEMBER_COUPON_ICON = "https://p0.meituan.net/ingee/57d1d2275698672342dbd089a65a6e9c5444.png";

    private static final Integer DP_LIMIT_SHELF_VERSION_NORMAL = 111;
    private static final Integer MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL = 112;

    public static ShelfItemPromoDetail buildPromoDetail(ProductPromoPriceM productPromoPriceM) {
        return buildPromoDetail(null, null, productPromoPriceM);
    }

    public static ShelfItemPromoDetail buildPromoDetail(Integer platform, Integer shelfVersion, ProductPromoPriceM productPromoPriceM) {
        if (productPromoPriceM == null || CollectionUtils.isEmpty(productPromoPriceM.getPromoItemList())) {
            return null;
        }
        ShelfItemPromoDetail shelfItemPromoDetail = new ShelfItemPromoDetail();
        shelfItemPromoDetail.setTitle(PROMO_POP_TITLE);
        shelfItemPromoDetail.setMarketPrice(productPromoPriceM.getMarketPrice());
        shelfItemPromoDetail.setSalePrice(productPromoPriceM.getPromoPriceTag());
        shelfItemPromoDetail.setPromoTag(getPromoTag(productPromoPriceM));
        shelfItemPromoDetail.setTotalPromoPrice(getPriceStr(productPromoPriceM.getTotalPromoPrice()));
        shelfItemPromoDetail.setPromoItems(buildPromoItemsWithIcon(platform, shelfVersion, productPromoPriceM.getPromoItemList(), productPromoPriceM.getProductId()));
        return shelfItemPromoDetail;
    }

    private static List<PromoPerItemVO> buildPromoItemsWithIcon(Integer platform, Integer shelfVersion, List<PromoItemM> promoItemList, long productId) {
        return promoItemList.stream()
                .map(item -> promoItemM2DzPromoPerItemVO(platform, shelfVersion, item, productId)).collect(Collectors.toList());
    }

    private static PromoPerItemVO promoItemM2DzPromoPerItemVO(Integer platform, Integer shelfVersion, PromoItemM promoItemM, long productId) {
        PromoPerItemVO promoPerItemVO = new PromoPerItemVO();
        promoPerItemVO.setPromoId(promoItemM.getPromoId() + "");
        promoPerItemVO.setPromoType(promoItemM.getPromoTypeCode() + "");
        promoPerItemVO.setTitle(promoItemM.getPromoType());
        promoPerItemVO.setDesc(getPromoDesc(promoItemM));
        promoPerItemVO.setPromoPrice(promoItemM.getPromoTag());
        promoPerItemVO.setTag(buildPerItemIcon(promoItemM.getIcon()));
        promoPerItemVO.setCouponPromoItem(buildMagicMemberCoupon(platform, shelfVersion, promoItemM, productId));
        return promoPerItemVO;
    }

    private static CouponPromoItemVO buildMagicMemberCoupon(Integer platform, Integer shelfVersion, PromoItemM promoItemM, long productId) {
        if (!isMagicalMemberCoupon(promoItemM) || MapUtils.isEmpty(promoItemM.getPromotionOtherInfoMap())) {
            return null;
        }
        // 点评侧，并且shelfVersion<111，则不展示膨胀（前端之前的组件有bug，样式错误）
        if (!validateCouponShelfVersionForDp(platform, shelfVersion)) {
            return null;
        }
        // 美团小程序，并且shelfVersion<=112，则不展示膨胀（当前前端组件有bug，无法膨胀）
        if (!validateCouponShelfVersionForMtWxXCX(platform, shelfVersion)) {
            return null;
        }
        boolean canInflate = BooleanUtils.toBoolean(promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
        boolean afterInflate = BooleanUtils.toBoolean(promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
        //未膨胀且不可膨胀，和正常券一样
        if (!afterInflate && !canInflate) {
            return null;
        }
        CouponPromoItemVO couponPromoItem = afterInflate ? buildAfterInflateCouponInfo(promoItemM) : buildBeforeInflateCouponInfo(promoItemM);
        couponPromoItem.setProductId(productId);
        couponPromoItem.setInflatedStatus(afterInflate ? 2 : 1);
        couponPromoItem.setCanInflate(canInflate ? 1 : 2);
        return couponPromoItem;
    }

    private static boolean validateCouponShelfVersionForDp(Integer platform, Integer shelfVersion) {
        if (platform == null || PlatformUtil.isMT(platform)) {
            // 忽略美团
            return true;
        }
        if (shelfVersion == null || shelfVersion == 0) {
            // 没有传shelfVersion
            return true;
        }
        return shelfVersion >= DP_LIMIT_SHELF_VERSION_NORMAL;
    }

    private static boolean validateCouponShelfVersionForMtWxXCX(Integer platform, Integer shelfVersion) {
        if (platform == null || !platform.equals(VCClientTypeEnum.MT_XCX.getCode())) {
            // 忽略其他平台
            return true;
        }
        return shelfVersion != null && shelfVersion >= MT_WX_XCX_LIMIT_SHELF_VERSION_NORMAL;
    }

    private static CouponPromoItemVO buildAfterInflateCouponInfo(PromoItemM promoItemM) {
        CouponPromoItemVO couponPromoItem = new CouponPromoItemVO();
        couponPromoItem.setCouponTag(promoItemM.getPromoItemText() != null ? promoItemM.getPromoItemText().getPromoStatusText() : null);
        couponPromoItem.setCouponType(getCouponType(promoItemM));
        return couponPromoItem;
    }

    private static CouponPromoItemVO buildBeforeInflateCouponInfo(PromoItemM promoItemM) {
        String couponId = promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.TSP_COUPON_ID.getValue());
        String couponGroupId = promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue());
        String bizToken = promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.BIZ_TOKEN.getValue());
        int assetType = NumberUtils.toInt(promoItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.ASSET_TYPE.getValue()));
        CouponPromoItemVO couponPromoItem = new CouponPromoItemVO();
        //1-家店通用券，2-到店通用券
        if(assetType == 1){
            couponPromoItem.setCouponCode(couponId);
            couponPromoItem.setApplyId(couponGroupId);
        }else{
            couponPromoItem.setCouponCode(promoItemM.getCouponId());
            couponPromoItem.setApplyId(promoItemM.getCouponGroupId());
        }
        couponPromoItem.setCouponType(getCouponType(promoItemM));
        couponPromoItem.setBizToken(bizToken);
        couponPromoItem.setAssetType(assetType);
        //7-poi详情页(货架)
        couponPromoItem.setPageSource(7);
        couponPromoItem.setRequiredAmount(promoItemM.getMinConsumptionAmount() != null ? promoItemM.getMinConsumptionAmount().multiply(new BigDecimal(100)).intValue() : 0);
        couponPromoItem.setReduceAmount(promoItemM.getAmount() != null ? promoItemM.getAmount().multiply(new BigDecimal(100)).intValue() : 0);
        if (promoItemM.getPromoItemText() != null) {
            couponPromoItem.setLogoIcon(MAGICAL_MEMBER_COUPON_ICON);
            couponPromoItem.setCouponDesc(promoItemM.getPromoItemText().getAtmosphereBarText());
            couponPromoItem.setCouponButtonText(promoItemM.getPromoItemText().getAtmosphereBarButtonText());
            couponPromoItem.setCouponTag(promoItemM.getPromoItemText().getPromoStatusText());
        }
        return couponPromoItem;
    }

    public static IconRichLabelModel buildPerItemIcon(String iconUrl) {
        PictureModel icon = new PictureModel();
        icon.setPicUrl(iconUrl);
        icon.setAspectRadio(1);
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        iconRichLabelModel.setIcon(icon);
        iconRichLabelModel.setType(IconRichLabelTypeEnum.ICON.getType());
        return iconRichLabelModel;
    }

    private static String getPromoDesc(PromoItemM promoItemM){
        //神券不出优惠描述
        if(isMagicalMemberCoupon(promoItemM)){
            return null;
        }
        return promoItemM.getDesc();
    }

    private static String getPriceStr(BigDecimal price) {
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return "0";
        }
        return price.stripTrailingZeros().toPlainString();
    }

    private static String getPromoTag(ProductPromoPriceM productPromoPriceM) {
        if (MapUtils.isEmpty(productPromoPriceM.getExtendDisplayInfo())) {
            return null;
        }
        //未膨胀则不出标签
        PromoItemM couponItemM = productPromoPriceM.getPromoItemList().stream().filter(UnifiedShelfPromoUtils::isMagicalMemberCoupon).findFirst().orElse(null);
        if (couponItemM == null || MapUtils.isEmpty(couponItemM.getPromotionOtherInfoMap()) ||
                !BooleanUtils.toBoolean(couponItemM.getPromotionOtherInfoMap().get(PromotionPropertyEnum.AFTER_INFLATE.getValue()))) {
            return null;
        }
        return productPromoPriceM.getExtendDisplayInfo().get(EXT_DISPLAY_PROMO_TAG_KEY);
    }

    private static boolean isMagicalMemberCoupon(PromoItemM promoItemM) {
        if (CollectionUtils.isEmpty(promoItemM.getPromotionExplanatoryTags())) {
            return false;
        }
        return promoItemM.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())
                || promoItemM.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode());
    }

    private static int getCouponType(PromoItemM promoItemM) {
        if (CollectionUtils.isEmpty(promoItemM.getPromotionExplanatoryTags())) {
            return 0;
        }
        if (promoItemM.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())) {
            return MAGICAL_MEMBER_COUPON;
        }
        if (promoItemM.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode())) {
            return MAGICAL_MEMBER_COUPON_FREE;
        }
        return 0;
    }
}
