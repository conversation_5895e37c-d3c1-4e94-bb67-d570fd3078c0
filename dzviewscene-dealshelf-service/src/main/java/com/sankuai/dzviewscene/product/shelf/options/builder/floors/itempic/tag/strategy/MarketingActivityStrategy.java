/*
 * Create Author : liyanmin
 * Create Date : 2023-11-01
 * Project :
 * File Name : MarketingActivityStrategy.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.dianping.gmkt.activ.api.enums.ExposurePromotionTypeEnum;
import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.CommonItemFloatTagPicOpt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 功能描述: 营销彩虹配置的活动信息
 * <p>
 *
 * <AUTHOR> yanmin.li
 *         <p>
 * @version 1.0 2023-11-01
 * @since dzviewscene-dealshelf-home 1.0
 */
@Component
public class MarketingActivityStrategy extends AbstractFloatTagBuildStrategy {

    private final static Integer SINGLE_PIC_MAX_SIZE = 400;

    private final static Integer DOUBLE_PIC_MAX_HEIGHT = 200;

    private final static Integer SINGLE_SMALL_PIC_SHELF_HEIGHT = 66;

    private final static Integer DOUBLE_SMALL_PIC_CARD_SHELF_HEIGHT = 18;

    private final static Integer SINGLE_BIG_PIC_SHELF_HEIGHT = 84;
    
    private final static Integer DOUBLE_BIG_PIC_SHELF_HEIGHT = 18;

    private final static Integer SINGLE_NEW_BIG_PIC_SHELF_HEIGHT = 86;

    private final static Integer DOUBLE_NEW_BIG_PIC_SHELF_HEIGHT = 16;

    //平台级大促>特价团购>业务级大促
    private final static List<Integer> exposurePromotionTypes = Lists.newArrayList(ExposurePromotionTypeEnum.PLATFORM_PROMOTION.getType(),
            ExposurePromotionTypeEnum.COST_EFFECTIVE.getType(), ExposurePromotionTypeEnum.BUSINESS_PROMOTION.getType());

    private final static Map<Integer, String> SHELF_SHOW_TYPE_TO_PIC_KEY = new ImmutableMap.Builder<Integer, String>()
            .put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), ExposurePicUrlKeyEnum.SINGLE_ROW_SHELF_ICON.getKey())
            .put(ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF.getType(), ExposurePicUrlKeyEnum.BIG_ICON.getKey())
            .put(ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF.getType(), ExposurePicUrlKeyEnum.DOUBLE_ROW_SHELF_ICON.getKey())
            .put(ShelfShowTypeEnum.DOUBLE_SMALL_PIC_CARD_SHELF.getType(),ExposurePicUrlKeyEnum.SHELF_CARD_ICON.getKey())
            .put(ShelfShowTypeEnum.SINGLE_NEW_BIG_PIC_SHELF.getType(), ExposurePicUrlKeyEnum.BIG_ICON.getKey())
            .put(ShelfShowTypeEnum.DOUBLE_NEW_BIG_PIC_SHELF.getType(), ExposurePicUrlKeyEnum.DOUBLE_ROW_SHELF_ICON.getKey())
            .build();

    private final static Map<Integer, Integer> SHELF_SHOW_TYPE_TO_PIC_HEIGHT = new ImmutableMap.Builder<Integer, Integer>()
            .put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), SINGLE_SMALL_PIC_SHELF_HEIGHT)
            .put(ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF.getType(), SINGLE_BIG_PIC_SHELF_HEIGHT)
            .put(ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF.getType(), DOUBLE_BIG_PIC_SHELF_HEIGHT)
            .put(ShelfShowTypeEnum.DOUBLE_SMALL_PIC_CARD_SHELF.getType(), DOUBLE_SMALL_PIC_CARD_SHELF_HEIGHT)
            .put(ShelfShowTypeEnum.SINGLE_NEW_BIG_PIC_SHELF.getType(), SINGLE_NEW_BIG_PIC_SHELF_HEIGHT)
            .put(ShelfShowTypeEnum.DOUBLE_NEW_BIG_PIC_SHELF.getType(), DOUBLE_NEW_BIG_PIC_SHELF_HEIGHT)
            .build();

    @Override
    public String getName() {
        return MarketingActivityStrategy.class.getSimpleName();
    }

    @Override
    boolean isMatch(FloatTagBuildReq param, FloatTagBuildCfg config) {
        return CollectionUtils.isNotEmpty(param.getProductM().getActivities());
    }

    @Override
    FloatTagVO buildTag(FloatTagBuildReq param, FloatTagBuildCfg config) {
        List<ProductActivityM> productActivityMS = param.getProductM().getActivities();
        if (CollectionUtils.isEmpty(productActivityMS)) {
            return null;
        }
        ProductActivityM activityM = getProductActivityM(param, productActivityMS);
        if (Objects.isNull(activityM) || MapUtils.isEmpty(activityM.getActivityPicUrlMap())
                || Objects.isNull(ShelfShowTypeEnum.of(param.getShelfShowType()))) {
            return null;
        }
        // 根据样式获取图片及图片尺寸卡控
        ShelfShowTypeEnum showTypeEnum = ShelfShowTypeEnum.of(param.getShelfShowType());
        String picUrlKey = SHELF_SHOW_TYPE_TO_PIC_KEY.get(showTypeEnum.getType());
        ActivityPicUrlDTO activityPicUrlDTO = activityM.getActivityPicUrlMap().get(picUrlKey);
        if(Objects.isNull(activityPicUrlDTO)) {
            return null;
        }
        // 单列
        if ((showTypeEnum == ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF || showTypeEnum == ShelfShowTypeEnum.SINGLE_BIG_PIC_SHELF
                    || showTypeEnum == ShelfShowTypeEnum.SINGLE_NEW_BIG_PIC_SHELF)
                && Objects.nonNull(activityPicUrlDTO.getHeight())
                && activityPicUrlDTO.getHeight() > 0
                && Objects.nonNull(activityPicUrlDTO.getWidth())
                && activityPicUrlDTO.getHeight().equals(activityPicUrlDTO.getWidth())) {
            int size = Math.min(activityPicUrlDTO.getHeight(), SINGLE_PIC_MAX_SIZE);
            return getFloatTagVO(activityPicUrlDTO, showTypeEnum, size, size,FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        }
        //双列
        if ((showTypeEnum == ShelfShowTypeEnum.DOUBLE_BIG_PIC_SHELF || showTypeEnum == ShelfShowTypeEnum.DOUBLE_NEW_BIG_PIC_SHELF)
                && Objects.nonNull(activityPicUrlDTO.getHeight())
                && activityPicUrlDTO.getHeight() > 0) {
            int height = Math.min(activityPicUrlDTO.getHeight(), DOUBLE_PIC_MAX_HEIGHT);
            return getFloatTagVO(activityPicUrlDTO, showTypeEnum, 0, height,FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        }
        //双列小图卡片
        if(showTypeEnum == ShelfShowTypeEnum.DOUBLE_SMALL_PIC_CARD_SHELF && Objects.nonNull(activityPicUrlDTO.getHeight()) && activityPicUrlDTO.getHeight() > 0){
            int height = Math.min(activityPicUrlDTO.getHeight(), DOUBLE_BIG_PIC_SHELF_HEIGHT);
            return getFloatTagVO(activityPicUrlDTO, showTypeEnum, 0, height,FloatTagPositionEnums.RIGHT_TOP.getPosition());
        }
        return null;
    }

    @NotNull
    private static FloatTagVO getFloatTagVO(ActivityPicUrlDTO activityPicUrlDTO, ShelfShowTypeEnum showTypeEnum,
            int width, int height,int position) {
        FloatTagVO floatTagVO = new FloatTagVO();
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl(PictureURLBuilders.toHttpsUrl(activityPicUrlDTO.getUrl(),width,height));
        floatTagPic.setPicHeight(SHELF_SHOW_TYPE_TO_PIC_HEIGHT.get(showTypeEnum.getType()));
        floatTagPic.setAspectRadio(activityPicUrlDTO.getUrlAspectRadio());
        floatTagVO.setPosition(position);
        floatTagVO.setIcon(floatTagPic);
        return floatTagVO;
    }

    private ProductActivityM getProductActivityM(FloatTagBuildReq param, List<ProductActivityM> productActivityMS) {
        //优惠减负，营销活动收敛
        if(PromoSimplifyUtils.hitPromoSimplifyV2(param.getContext())){
            return getProductActivityM(productActivityMS);
        }
        ProductActivityM activityM = productActivityMS.stream()
                .filter(item -> MapUtils.isNotEmpty(item.getActivityPicUrlMap())).findFirst().orElse(null);
        if (Objects.nonNull(param.getActivityTagCfg())) {
            ActivityTagCfg activityTagCfg = param.getActivityTagCfg();
            // 如果有配置配置活动类型，则检验
            activityM = productActivityMS.stream()
                    .filter(item -> ((CollectionUtils.isEmpty(activityTagCfg.getActivityTypes())
                            || activityTagCfg.getActivityTypes().contains(item.getShelfActivityType()))
                            && (CollectionUtils.isEmpty(activityTagCfg.getActivityScene())
                                    || activityTagCfg.getActivityScene().contains(item.getActivityScene()))
                            && (Objects.isNull(activityTagCfg.getIsPreheat())
                                    || activityTagCfg.getIsPreheat().equals(item.isPreheat()))))
                    .findFirst().orElse(null);
        }
        return activityM;
    }

    private ProductActivityM getProductActivityM(List<ProductActivityM> productActivityMS) {
        Map<Integer, ProductActivityM> activityMap = productActivityMS.stream().collect(Collectors
                .toMap(ProductActivityM::getExposurePromotionType, item -> item, (v1, v2) -> v1));
        for(Integer promotionType : exposurePromotionTypes) {
            ProductActivityM productActivityM = activityMap.get(promotionType);
            if (Objects.nonNull(productActivityM)) {
                return productActivityM;
            }
        }
        return null;
    }
}
