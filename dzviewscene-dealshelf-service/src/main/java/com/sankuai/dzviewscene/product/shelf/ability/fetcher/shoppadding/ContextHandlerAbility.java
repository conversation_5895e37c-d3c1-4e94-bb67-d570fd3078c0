package com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.PaddingConstants;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.*;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/1/9 14:18
 */
@Ability(code = ContextHandlerAbility.CODE, name = "Model-上下文处理器", description = "异步处理上下文",
        activities = {DealShelfActivity.CODE, DealFilterListActivity.CODE, UnifiedShelfActivity.CODE},
        dependency = {ShelfDouHuFetcher.CODE})
public class ContextHandlerAbility extends PmfAbility<ContextHandlerResult, ContextHandlerAbility.Request, ContextHandlerAbility.Config> implements BeanFactoryAware {

    public static final String CODE = "ContextHandlerAbility";

    /**
     * 上下文填充管理器
     */
    private static Map<Integer, Class<? extends ContextPaddingHandler>> paddingType2PaddingHandlers = new HashMap<>();

    static {
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.shopGuaranteeTagPaddingHandler.code, ShopGuaranteeTagPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.shopReservationCountPaddingHandler.code, ShopReservationCountPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.shopCustomerRecommendWordPaddingHandler.code, ShopCustomerRecommendWordPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.shopQueryAnalyzeWordPaddingHandler.code, ShopQueryAnalyzeWordPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.lifeWashShopTimelinessPaddingHandler.code, LifeWashShopTimelinessPaddingHandler.class);
        paddingType2PaddingHandlers.put(PaddingConstants.ContextPaddingType.userAfterPayPaddingHandler.code, UserAfterPayPaddingHandler.class);
    }

    @Resource
    private BeanFactory beanFactory;

    @Override
    public CompletableFuture<ContextHandlerResult> build(ActivityCxt ctx, Request request, Config config) {
        // 上下文填充引擎
        if (MapUtils.isNotEmpty(config.getMultiParams())) {
            return getEngineRes(ctx, config);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 上下文处理能力支持多个填充器同时填充
     * 每个填充器的结果会作为 ContextHandlerResult 的一个域
     *
     * @param ctx
     * @param config
     * @return
     */
    private CompletableFuture<ContextHandlerResult> getEngineRes(ActivityCxt ctx, Config config) {
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();

        if (MapUtils.isEmpty(config.getMultiParams())) {
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        List<CompletableFuture<ContextHandlerResult>> hanlderClfList = new ArrayList<>();
        for (Map.Entry<Integer, Map<String, Object>> entry : config.getMultiParams().entrySet()) {
            hanlderClfList.add(paddingSingleShopGroup(ctx, contextHandlerResult, entry.getKey(), entry.getValue()));
        }
        return CompletableFuture.allOf(hanlderClfList.toArray(new CompletableFuture[0])).thenApply(Void -> contextHandlerResult);
    }

    private CompletableFuture<ContextHandlerResult> paddingSingleShopGroup(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Integer paddingHandlerType, Map<String, Object> paddingInputParam) {
        if (Objects.isNull(paddingHandlerType) || paddingHandlerType == 0) {
            return CompletableFuture.completedFuture(null);
        }
        // 获取上下文填充器
        Class contextPaddingHandlerClazz = paddingType2PaddingHandlers.get(paddingHandlerType);
        if (Objects.isNull(contextPaddingHandlerClazz)) {
            return CompletableFuture.completedFuture(null);
        }
        ContextPaddingHandler contextPaddingHandler = (ContextPaddingHandler) beanFactory.getBean(contextPaddingHandlerClazz);
        return contextPaddingHandler.padding(ctx, contextHandlerResult, paddingInputParam);
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    @AbilityRequest
    @Data
    public static class Request {

    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 门店填充参数
         * 一级key：填充器code
         * 一级value：填充器需要的参数
         * 示例：
         * {
         * "multiParams": {
         * "3001":{
         * "test_1":1,
         * "test_2":2
         * }
         * }
         * }
         */
        private Map<Integer, Map<String, Object>> multiParams;
    }
}
