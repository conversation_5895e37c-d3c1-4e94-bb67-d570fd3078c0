package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateHandlerContext {

    private Map<String, ProductM> productMMap;

    private ShelfItemVO shelfItemVO;
}
