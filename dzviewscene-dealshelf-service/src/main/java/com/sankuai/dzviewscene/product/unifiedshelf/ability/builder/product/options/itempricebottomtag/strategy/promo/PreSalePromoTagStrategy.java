package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfProductMPromoInfoUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PreSalePromoTagStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private final List<Integer> PRE_PROMO_TAG_TYPE = Lists.newArrayList(PromoTagTypeEnum.PreSale_Member.getCode(), PromoTagTypeEnum.PreSale_NewUser.getCode(),
            PromoTagTypeEnum.PreSale_MerchantMember.getCode(), PromoTagTypeEnum.PreSale.getCode());

    @Override
    public String getName() {
        return "预售";
    }

    @Override
    public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
        ProductPromoPriceM productPromoPriceM = PriceUtils.getUserHasPromoPrice(req.getProductM(), req.getCardM());
        if (productPromoPriceM == null || !PRE_PROMO_TAG_TYPE.contains(productPromoPriceM.getPromoTagType())) {
            return null;
        }
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setName(productPromoPriceM.getPromoTag());
        shelfTagVO.setText(buildCommonTagText(productPromoPriceM.getPromoTag(), "超值预售", req.isHiddenPreText()));
        shelfTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(productPromoPriceM));
        addAfterPic(shelfTagVO, getCommonAfterPic(req.getPlatform()));
        return shelfTagVO;
    }

}
