package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.product.unifiedshelf.enums.JumpUrlPatternEnum;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class JumpUrlUtils {
    public static final String OTHER_TYPE = "other_type";
    public static final String EMPTY_TYPE = "empty";
    public static final String PATH= "path";

    // 判断属于哪个类型：匹配其中任意一组 key - pattern 对即可归类
    public static JumpUrlPatternEnum classify(Map<String, String> params) {
        // 遍历 JumpUrlPatternEnum 枚举的所有值
        for (JumpUrlPatternEnum patternEnum : JumpUrlPatternEnum.values()) {
            // 获取当前枚举项对应的多个 Pattern Map
            for (Map<String, String> patternMap : patternEnum.getPatternMaps()) {
                boolean isMatch = true;
                // 遍历 Pattern Map 中的每个 key - pattern 对
                for (Map.Entry<String, String> entry : patternMap.entrySet()) {
                    String key = entry.getKey();
                    String pattern = entry.getValue();
                    // 检查 params 中是否包含该 key
                    if (!params.containsKey(key)) {
                        isMatch = false;
                        break;
                    }
                    String value = params.get(key);
                    // 检查 value 是否匹配对应的正则表达式
                    if (!Pattern.matches(pattern, value)) {
                        isMatch = false;
                        break;
                    }
                }
                // 如果所有 key - pattern 对都匹配成功，则返回当前枚举项
                if (isMatch) {
                    return patternEnum;
                }
            }
        }
        // 若遍历完所有枚举项都没有匹配成功，返回 null
        return null;
    }
    public static Map<String, String> extractFields(String url) {
        Map<String, String> fields = new LinkedHashMap<>();

        // 提取从开头到 ? 之前的部分
        int questionIndex = url.indexOf('?');
        if (questionIndex != -1) {
            String path = url.substring(0, questionIndex);
            fields.put(PATH, path);
        }

        // 正则匹配 ?key=value 或 &key=value
        Pattern pattern = Pattern.compile("[?&]([^=&]+)=([^&#]*)");
        Matcher matcher = pattern.matcher(url);

        while (matcher.find()) {
            String key = matcher.group(1);
            String value = matcher.group(2);
            fields.put(key, value);
        }
        return fields;
    }
    public static String extractOutlier(String url) {
        Map<String, String> fields = extractFields(url);
        int count = 0;
        StringBuilder result = new StringBuilder();

        for (Map.Entry<String, String> entry : fields.entrySet()) {
            if (count >= 4) {
                break;
            }
            if (count > 0) {
                result.append("&");
            }
            result.append(entry.getKey()).append("=").append(entry.getValue());
            count++;
        }
        return result.toString();
    }
    public static String getJumpUrlType(String Jumpurl) {
        if (Jumpurl == null) {
            return EMPTY_TYPE;
        }
        Map<String, String> SchemeMap = extractFields(Jumpurl);
        JumpUrlPatternEnum UrlType = classify(SchemeMap);
        if (UrlType != null) {
            return UrlType.name();
        }
        return OTHER_TYPE;
    }
}
