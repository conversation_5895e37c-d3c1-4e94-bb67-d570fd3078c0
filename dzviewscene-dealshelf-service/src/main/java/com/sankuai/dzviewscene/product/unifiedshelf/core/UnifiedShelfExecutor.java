package com.sankuai.dzviewscene.product.unifiedshelf.core;

import com.dianping.cat.Cat;
import com.dianping.gateway.client.debug.DEBUG;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.enums.PositionTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberMonitorUtils;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfLogUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.faulttolerance.utils.TriggerFaultTolerantUtils;
import com.sankuai.dzviewscene.shelf.framework.*;
import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.nibpt.unionlogger.UnionLoggerContext;
import com.sankuai.nibpt.unionlogger.constant.BusinessTypeEnum;
import com.sankuai.nibscp.common.flow.identify.anno.FlowDyeAnnotation;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UnifiedShelfExecutor implements Executor<ActivityContextRequest, UnifiedShelfResponse> {

    @Resource
    private UnifiedShelfActivityEngine activityEngine;

    @Override
    @FlowDyeAnnotation
    public UnifiedShelfResponse execute(ActivityContextRequest request) {
        try {
            // 全链路日志埋入userId
            UnionLoggerContext.logUserId(BusinessTypeEnum.GENERAL_POI_SHELF, String.valueOf(request.getUserId()));
            // 1. tracer开关打开
            ExceptionTracer.start();
            // 暂时不需要
            // 2.根据场景黑名单直接过滤非法请求
            // if (AntiCrawlerUtils.checkIllegalHttpRequest(request)) {return null;}
            long startTime = System.currentTimeMillis();
            // 3. 主流程
            UnifiedShelfResponse response = getExecuteResult(request);
            // 4.根据场景强制登录及兜底价格隐藏
            antiCrawler(response, request.getUserId(),request.isMiniProgramLoginStatus());
            logEmptyFields(response, request.getShopId(), request.getPlatform());
            MagicalMemberMonitorUtils.monitorTrace(getResultSceneCode(response), startTime);
            return response;
        } finally {
            //4. 清理tracer上下文
            ExceptionTracer.end();
            //清理日志埋点
            UnionLoggerContext.clear();
            MagicalMemberMonitorUtils.clear();
        }
    }

    private void antiCrawler(UnifiedShelfResponse response, long userId,boolean loginStatus) {
        try {
            if (response == null || response.getFilterIdAndProductAreas() == null) {
                return;
            }
            // 强制登录判断
            AntiCrawlerUtils.clearUnifiedShelf(response, userId, response.getSceneCode(),loginStatus);
        } catch (Exception e) {
            Cat.logError("AntiCrawlerError", e);
        }
    }

    /**
     * 团单缺失字段打点监控
     */
    private void logEmptyFields(UnifiedShelfResponse response, long shopId, int platform) {
        try {
            if (response == null || response.getFilterIdAndProductAreas() == null) {
                return;
            }
            // 团单缺失字段打点监控
            UnifiedShelfLogUtils.logMetric(response.getFilterIdAndProductAreas(), response.getSceneCode(), shopId, platform);
        } catch (Exception e) {
            //忽略
            Cat.logError(e);
        }
    }

    private String getResultSceneCode(UnifiedShelfResponse dzShelfResponseVO) {
        if (dzShelfResponseVO == null) {
            return null;
        }
        return dzShelfResponseVO.getSceneCode();
    }

    private UnifiedShelfResponse getExecuteResult(ActivityContextRequest request) {
        ActivityRequest activityRequest = buildActivityRequest(request);
        return executeByActivityEngine(activityRequest);
    }

    private UnifiedShelfResponse executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 执行活动引擎
        ActivityResponse<UnifiedShelfResponse> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }
        // 2. 框架执行trace信息加入debug
        addTrace2DEBUG(activityResponse);
        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            // 4. 执行过程中如果发生需要关注异常，则会触发容错异常
            TriggerFaultTolerantUtils.triggerFaultTolerantByCareExecuteError(activityResponse.getExecuteError());
            return null;
        }
        UnifiedShelfResponse shelfResponseVO = activityResponse.getResult().join();
        // 5. 返回结果不符合预期会触发容错
        TriggerFaultTolerantUtils.triggerFaultTolerantByUnExpectResult(activityResponse.getExecuteError(), shelfResponseVO);
        return shelfResponseVO;
    }

    private void addTrace2DEBUG(ActivityResponse<UnifiedShelfResponse> activityResponse) {
        DEBUG.log("traces", activityResponse.getTraceElements());
    }

    public static ActivityRequest buildActivityRequest(ActivityContextRequest request) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(UnifiedShelfActivity.CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.spaceKey, UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, request.getSceneCode());
        activityRequest.addParam(ShelfActivityConstants.Params.mtSIFlag, request.getMtSIFlag());
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, request.getSearchKeyword());
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, request.getShopUuid());
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, request.getPlatform());
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, request.getUnionId());
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, request.getDeviceId());
        activityRequest.addParam(ShelfActivityConstants.Params.openId, request.getOpenId());
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, request.getClient());
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, request.getVersion());
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, request.getChannel());
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(request.getPlatform()) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(request.getPlatform())) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, request.getCityId());
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, Long.valueOf(request.getShopId()).intValue());
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiIdL, request.getShopId());
            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, request.getUserId());
            //添加平台参数，去除活动上下文构造里重复调用
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, request.getDpCityId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, (int) request.getDpShopId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiIdL, request.getDpShopId());
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, request.getCityId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, Long.valueOf(request.getShopId()).intValue());
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiIdL, request.getShopId());
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, request.getUserId());
        }
        //添加加载的门店信息，去除活动上下文构造里重复调用
        activityRequest.addParam(ShelfActivityConstants.Ctx.ctxShop, request.getShopM());
        activityRequest.addParam(ShelfActivityConstants.Params.extra, request.getExtra());
        activityRequest.addParam(ShelfActivityConstants.Params.locationCityId, request.getLocationCityId());
        activityRequest.addParam(ShelfActivityConstants.Params.wttRegionId, request.getWttRegionId());
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        activityRequest.addParam(ShelfActivityConstants.Params.searchKeyword, request.getSearchKeyword());
        activityRequest.addParam(ShelfActivityConstants.Params.summaryProductIds, request.getSummaryProductIds());
        activityRequest.addParam(ShelfActivityConstants.Params.anchorGoodId, request.getAnchorGoodId());
        activityRequest.addParam(ShelfActivityConstants.Params.bizType, request.getBizType());
        activityRequest.addParam(ShelfActivityConstants.Params.shelfVersion, request.getShelfVersion());
        activityRequest.addParam(ShelfActivityConstants.Params.pageSource, request.getPageSource());
        activityRequest.addParam(ShelfActivityConstants.Params.lat, request.getLat());
        activityRequest.addParam(ShelfActivityConstants.Params.lng, request.getLng());
        activityRequest.addParam(ShelfActivityConstants.Params.coordType, request.getCoordType());
        activityRequest.addParam(ShelfActivityConstants.Params.useType, request.getUseType());
        activityRequest.addParam(ShelfActivityConstants.Params.recommendinfo, request.getRecommendinfo());
        activityRequest.addParam(ShelfActivityConstants.Params.pricecipher, request.getPricecipher());
        activityRequest.addParam(ShelfActivityConstants.Params.position, MagicMemberUtil.getPosition(request.getPlatform(), PositionTypeEnum.SHELF.getCode()));
        activityRequest.addParam(ShelfActivityConstants.Params.pagination, request.getPagination());
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterId, request.getFilterBtnId());
        if (request.getPageindex() > 0) {
            activityRequest.addParam(ShelfActivityConstants.Params.pageNo, request.getPageindex());
        }
        if (request.getPagesize() > 0) {
            activityRequest.addParam(ShelfActivityConstants.Params.pageSize, request.getPagesize());
        }
        activityRequest.addParam(ShelfActivityConstants.Params.customInfo, request.getCustomInfo());
        activityRequest.addParam(ShelfActivityConstants.Params.startTime, activityRequest.getStartTime());
        activityRequest.addParam(ShelfActivityConstants.Params.requestType, RequestTypeEnum.API_UNIFIED_SHELF.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.appId, request.getAppId());
        activityRequest.addParam(ShelfActivityConstants.Params.operatorPreviewConfigTags, request.getOperatorPreviewConfigTags());
        activityRequest.addParam(ShelfActivityConstants.Params.mockDouHuResult, request.getMockDouHuResult());
        activityRequest.addParam(ShelfActivityConstants.Params.channelType, request.getChannelType());
        return activityRequest;
    }
}
