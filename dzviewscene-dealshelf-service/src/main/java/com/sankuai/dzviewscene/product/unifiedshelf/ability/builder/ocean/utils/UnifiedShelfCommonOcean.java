package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils;

import cn.hutool.core.map.MapUtil;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.ItemShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.RegExCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.enums.OceanTypeEnums;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.utils.OceanConstantUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sig.botdefender.core.crypt.utils.SigCryptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils.MAGICAL_MEMBER_TAG_NAME;

/**
 * 货架打点工具包
 *
 * @see com.sankuai.dzviewscene.shelf.platform.shelf.ocean.ShelfCommonOcean
 */
public class UnifiedShelfCommonOcean {

    public static final String payLaterTag = "先用后付";

    public static final List<String> PRICE_PREFIX_LIST = Lists.newArrayList("神券价", "国补价");

    /**
     * 填充公共打点信息
     */
    public static ShelfOceanVO paddingCommonOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        try {
            if (UnifiedOceanConfigUtils.switchOff() || shelfComponent == null) {
                return shelfOceanVO;
            }
            shelfOceanVO = initialShelfOceanIfNull(shelfOceanVO);
            //填充整个货架Labs的打点
            paddingWholeLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一个商品区更多Labs的打点
            paddingMoreLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二个商品区更多Labs的打点
//            paddingSecondMoreLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一个商品区Item的Labs的打点
            paddingProductItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二个商品区Item的Labs的打点
//            paddingSecondProductItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第一层筛选项的Labs的打点
            paddingFilterBarLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充第二层筛选项的Labs的打点
            paddingChildrenFilterBarLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充商品区商品标签打点
            paddingProductItemTagLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //购买按钮打点
            paddingProductBuyBtnLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充子项展示收起打点
            paddingChildrenCollapseLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充子项更多打点
            paddingChildrenMoreLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充SPU卡片打点
            paddingSpuItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充套餐卡片打点
            paddingSkuItemLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充商品区商品底部标签打点
//            paddingProductBottomTagLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充筛选弹窗的Labs的打点
//            paddingFilterPickerLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充筛选项个性化Labs的公共打点
            paddingFilterLabsOcean(shelfComponent, ctx);
            //填充预订货架心智条打点
            paddingReserveMindBarLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充预订货架心智条浮层打点
            paddingReserveMindSupernatantLabsOcean(shelfOceanVO, shelfComponent, ctx);
            //填充商品域的公共打点
            paddingProductAreaOcean(shelfComponent, ctx);
            return shelfOceanVO;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return shelfOceanVO;
    }
//
//    private static void paddingFilterPickerLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
//        if (shelfComponent.getFilterTree() == null || CollectionUtils.isEmpty(shelfComponent.getFilterTree().getChildren())) {
//            return;
//        }
//        if (shelfOceanVO.getFilterPicker() == null) {
//            shelfOceanVO.setFilterPicker(new ShelfOceanEntryVO());
//        }
//        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
//        ShelfOceanEntryVO filterPickerByConfig = getFilterPickerByConfig(ctx);
//        shelfOceanVO.setFilterPicker(getAndSetOceanLabsEntry(shelfOceanVO.getFilterPicker(), productAreaComponent, shelfComponent, ctx, filterPickerByConfig, null));
//    }
//
//    private static ShelfOceanEntryVO getFilterPickerByConfig(ActivityContext ctx) {
//        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
//        if (shelfOceanByConfig == null) {
//            return null;
//        }
//        return shelfOceanByConfig.getFilterPicker();
//    }

    private static void paddingChildrenFilterBarLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if ((shelfComponent.getFilter() == null
                || shelfComponent.getFilter().getFilterRoot() == null
                || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterRoot().getChildren()))
            // && (shelfComponent.getFilterTree() == null || CollectionUtils.isEmpty(shelfComponent.getFilterTree().getChildren()))
        ) {
            return;
        }
        if (shelfOceanVO.getChildrenFilterBar() == null) {
            shelfOceanVO.setChildrenFilterBar(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO childrenFilterBarConfig = getChildrenFilterBarByConfig(ctx);
        //需要塞默认值
        Map<String, Object> oceanEntryMap = new HashMap<>();
        oceanEntryMap.put(OceanConstantUtils.ITEM_TYPE, 0);
        oceanEntryMap.put(OceanConstantUtils.SHOW_MODE, "-999");
        shelfOceanVO.setChildrenFilterBar(getAndSetOceanLabsEntry(shelfOceanVO.getChildrenFilterBar(), productAreaComponent, shelfComponent, ctx, childrenFilterBarConfig, oceanEntryMap));
    }

    private static ShelfOceanEntryVO getChildrenFilterBarByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getChildrenFilterBar();
    }

    private static void paddingFilterBarLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if ((shelfComponent.getFilter() == null
                || shelfComponent.getFilter().getFilterRoot() == null
                || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterRoot().getChildren()))
        ) {
            return;
        }
        if (shelfOceanVO.getFilterBar() == null) {
            shelfOceanVO.setFilterBar(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO filterBarConfig = getFilterBarByConfig(ctx);
        //需要塞默认值
        Map<String, Object> oceanEntryMap = new HashMap<>();
        oceanEntryMap.put(OceanConstantUtils.ITEM_TYPE, 0);
        oceanEntryMap.put(OceanConstantUtils.SHOW_MODE, "-999");
        shelfOceanVO.setFilterBar(getAndSetOceanLabsEntry(shelfOceanVO.getFilterBar(), productAreaComponent, shelfComponent, ctx, filterBarConfig, oceanEntryMap));
    }

    private static ShelfOceanEntryVO getFilterBarByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getFilterBar();
    }

    private static ShelfOceanVO initialShelfOceanIfNull(ShelfOceanVO shelfOceanVO) {
        if (shelfOceanVO == null) {
            return new ShelfOceanVO();
        }
        return shelfOceanVO;
    }

    private static void paddingProductAreaOcean(UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())) {
            return;
        }
        List<String> filterBtnIdsSelected = getFilterBtnIdsSelected(shelfComponent.getFilter());
        boolean hasMultiFilterIdAndProductAreas = shelfComponent.getFilterIdAndProductAreas().size() > 1;
        shelfComponent.getFilterIdAndProductAreas().forEach(filterIdAndProductArea -> {
            if (filterIdAndProductArea == null || CollectionUtils.isEmpty(filterIdAndProductArea.getProductAreas())) {
                return;
            }
            filterIdAndProductArea.getProductAreas().forEach(productArea -> {
                if (productArea == null) {
                    return;
                }
                paddingItemAreaComponentLabs(shelfComponent, productArea, ctx, filterIdAndProductArea.getFilterId(), filterBtnIdsSelected, hasMultiFilterIdAndProductAreas);
            });
        });
    }

    private static void paddingProductItemLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getProductItem() == null) {
            shelfOceanVO.setProductItem(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO productItemConfig = getProductItemOceanByConfig(ctx);
        shelfOceanVO.setProductItem(getAndSetOceanLabsEntry(shelfOceanVO.getProductItem(), productAreaComponent, shelfComponent, ctx, productItemConfig, null));
    }

    private static void paddingProductItemTagLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getProductItemTag() == null) {
            shelfOceanVO.setProductItemTag(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO productItemTagConfig = getProductItemTagOceanByConfig(ctx);
        shelfOceanVO.setProductItemTag(getAndSetOceanLabsEntry(shelfOceanVO.getProductItemTag(), productAreaComponent, shelfComponent, ctx, productItemTagConfig, null));
    }

    private static void paddingProductBuyBtnLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getBuyBtn() == null) {
            shelfOceanVO.setBuyBtn(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO productItemTagConfig = getProductItemBuyBtnOceanByConfig(ctx);
        shelfOceanVO.setBuyBtn(getAndSetOceanLabsEntry(shelfOceanVO.getBuyBtn(), productAreaComponent, shelfComponent, ctx, productItemTagConfig, null));
    }

    private static void paddingChildrenCollapseLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO childrenCollapseByConfig = getChildrenCollapseByConfig(ctx);
        shelfOceanVO.setChildrenCollapse(getAndSetOceanLabsEntry(shelfOceanVO.getChildrenCollapse(), productAreaComponent, shelfComponent, ctx, childrenCollapseByConfig, null));
    }

    private static void paddingChildrenMoreLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (shelfOceanVO.getChildrenMore() == null) {
            shelfOceanVO.setChildrenMore(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponentVO = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO childrenMoreOceanConfig = getChildrenMoreOceanByConfig(ctx);
        shelfOceanVO.setChildrenMore(getAndSetOceanLabsEntry(shelfOceanVO.getChildrenMore(), productAreaComponentVO, shelfComponent, ctx, childrenMoreOceanConfig, null));
    }

    private static void paddingSpuItemLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO spuItemConfig = getSpuItemByConfig(ctx);
        shelfOceanVO.setSpuItem(getAndSetOceanLabsEntry(shelfOceanVO.getSpuItem(), productAreaComponent, shelfComponent, ctx, spuItemConfig, null));
    }

    private static void paddingSkuItemLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO skuItemConfig = getSkuItemByConfig(ctx);
        shelfOceanVO.setSkuItem(getAndSetOceanLabsEntry(shelfOceanVO.getSkuItem(), productAreaComponent, shelfComponent, ctx, skuItemConfig, null));
    }

//    private static void paddingProductBottomTagLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
//        if (shelfOceanVO.getProductBottomTag() == null) {
//            shelfOceanVO.setProductBottomTag(new ShelfOceanEntryVO());
//        }
//        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
//        ShelfOceanEntryVO productBottomTagConfig = getProductBottomTagOceanByConfig(ctx);
//        shelfOceanVO.setProductBottomTag(getAndSetOceanLabsEntry(shelfOceanVO.getProductBottomTag(), productAreaComponent, shelfComponent, ctx, productBottomTagConfig, null));
//    }

//    private static void paddingSecondProductItemLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
//        if (shelfOceanVO.getSecondProductItem() == null) {
//            shelfOceanVO.setSecondProductItem(new ShelfOceanEntryVO());
//        }
//        ShelfProductAreaVO productAreaComponentVO = getSecondShelfProductAreaVO(shelfComponent);
//        ShelfOceanEntryVO secondProductItemConfig = getSecondProductItemOceanByConfig(ctx);
//        shelfOceanVO.setSecondProductItem(getAndSetOceanLabsEntry(shelfOceanVO.getSecondProductItem(), productAreaComponentVO, shelfComponent, ctx, secondProductItemConfig, null));
//    }

//    private static ShelfOceanEntryVO getSecondProductItemOceanByConfig(ActivityContext ctx) {
//        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
//        if (shelfOceanByConfig == null) {
//            return null;
//        }
//        return shelfOceanByConfig.getSecondProductItem();
//    }

    private static ShelfOceanEntryVO getProductItemOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getProductItem();
    }

    private static ShelfOceanEntryVO getProductItemTagOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getProductItemTag();
    }

    private static ShelfOceanEntryVO getProductItemBuyBtnOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getBuyBtn();
    }

    private static ShelfOceanEntryVO getChildrenCollapseByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getChildrenCollapse();
    }

    private static ShelfOceanEntryVO getSpuItemByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getSpuItem();
    }

    private static ShelfOceanEntryVO getSkuItemByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getSkuItem();
    }

//    private static ShelfOceanEntryVO getProductBottomTagOceanByConfig(ActivityContext ctx) {
//        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
//        if (shelfOceanByConfig == null) {
//            return null;
//        }
//        return shelfOceanByConfig.getProductBottomTag();
//    }

    private static void paddingMoreLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        //一级更多如果为空，则进行初始化
        if (shelfOceanVO.getMore() == null) {
            shelfOceanVO.setMore(new ShelfOceanEntryVO());
        }
        ShelfProductAreaVO productAreaComponentVO = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO moreOceanConfig = getMoreOceanByConfig(ctx);
        shelfOceanVO.setMore(getAndSetOceanLabsEntry(shelfOceanVO.getMore(), productAreaComponentVO, shelfComponent, ctx, moreOceanConfig, null));
    }

    private static ShelfOceanEntryVO getMoreOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getMore();
    }

    private static ShelfOceanEntryVO getChildrenMoreOceanByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getChildrenMore();
    }

//    private static ShelfProductAreaVO getSecondShelfProductAreaVO(UnifiedShelfResponse shelfComponent) {
//        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())
//                || shelfComponent.getFilterIdAndProductAreas().get(0) == null
//                || CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas())
//                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().size() <= 1
//                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(1) == null) {
//            return new ShelfProductAreaVO();
//        }
//        return shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(1);
//    }

//    private static void paddingSecondMoreLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
//        if (shelfOceanVO.getSecondMore() == null) {
//            shelfOceanVO.setSecondMore(new ShelfOceanEntryVO());
//        }
//        ShelfProductAreaVO productAreaComponentVO = getSecondShelfProductAreaVO(shelfComponent);
//        ShelfOceanEntryVO secondMoreOceanConfig = getSecondMoreOceanByConfig(ctx);
//        shelfOceanVO.setSecondMore(getAndSetOceanLabsEntry(shelfOceanVO.getSecondMore(), productAreaComponentVO, shelfComponent, ctx, secondMoreOceanConfig, null));
//    }

//    private static ShelfOceanEntryVO getSecondMoreOceanByConfig(ActivityContext ctx) {
//        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
//        if (shelfOceanByConfig == null) {
//            return null;
//        }
//        return shelfOceanByConfig.getSecondMore();
//    }

//
//    private static String getModuleNameByTitleAndMainTitleComponent(String sceneCode, TitleComponentVO titleComponent, MainTitleComponentVO mainTitleComponent, ActivityContext ctx) {
//        //首先返回场景自定义的标题
//        String mainTitle = UnifiedOceanConfigUtils.getMainTitleByConfig(sceneCode, ctx);
//        if (StringUtils.isNotEmpty(mainTitle)) {
//            return getTitleByFilterSpecialCharacter(mainTitle);
//        }
//        //货架主标题组件中的标题
//        String title = mainTitleComponent == null ? null : mainTitleComponent.getTitle();
//        //优先展示商品区标题
//        if (titleComponent != null && StringUtils.isNotEmpty(titleComponent.getTitle())) {
//            title = titleComponent.getTitle();
//        }
//        return getTitleByFilterSpecialCharacter(title);
//    }
//
//    private static String getModuleName(UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
//        //首先返回场景自定义的标题
//        String mainTitle = UnifiedOceanConfigUtils.getMainTitleByConfig(shelfComponent.getSceneCode(), ctx);
//        if (StringUtils.isNotEmpty(mainTitle)) {
//            return getTitleByFilterSpecialCharacter(mainTitle);
//        }
//        if (shelfComponent.getMainTitle() == null || StringUtils.isEmpty(shelfComponent.getMainTitle().getTitle())) {
//            //兜底第一个商品区的货架标题
//            return getTitleByFilterSpecialCharacter(getFirstProductAreaTitle(shelfComponent));
//        }
//        //货架主标题名称
//        return getTitleByFilterSpecialCharacter(shelfComponent.getMainTitle().getTitle());
//    }

//    private static String getTitleByFilterSpecialCharacter(String title) {
//        if (StringUtils.isEmpty(title)) {
//            return null;
//        }
//        RegExCfgBean regExCfgBean = UnifiedOceanConfigUtils.getRegExConstant();
//        if (regExCfgBean == null || StringUtils.isEmpty(regExCfgBean.getTitleRegEx())) {
//            return title;
//        }
//        return Pattern.compile(regExCfgBean.getTitleRegEx()).matcher(title).replaceAll("").trim();
//    }

    private static void paddingItemAreaComponentLabs(UnifiedShelfResponse shelfComponent, ShelfProductAreaVO productAreaComponentVO, ActivityContext ctx, String filterBtnId,
                                                     List<String> filterBtnIdsSelected, boolean hasMultiFilterIdAndProductAreas) {
        if (CollectionUtils.isEmpty(productAreaComponentVO.getItems())) {
            return;
        }
        Map<Integer, ProductM> productMMap = getProductMMap(ctx);
        AtomicInteger index = new AtomicInteger(0);
        productAreaComponentVO.getItems().forEach(productItem -> {
            if (productItem == null) {
                return;
            }
            if(productItem.getShowType() == ItemShowTypeEnum.AGGREGATE_CARD.getType()
                && CollectionUtils.isNotEmpty(productItem.getSubItems())){
                productItem.getSubItems().forEach(subItem -> paddingItemLab(shelfComponent, productAreaComponentVO, ctx, filterBtnId, filterBtnIdsSelected, hasMultiFilterIdAndProductAreas, subItem, productMMap, index));
            }
            paddingItemLab(shelfComponent, productAreaComponentVO, ctx, filterBtnId, filterBtnIdsSelected, hasMultiFilterIdAndProductAreas, productItem, productMMap, index);
        });
    }

    private static void paddingItemLab(UnifiedShelfResponse shelfComponent, ShelfProductAreaVO productAreaComponentVO, ActivityContext ctx, String filterBtnId, List<String> filterBtnIdsSelected,
                                       boolean hasMultiFilterIdAndProductAreas, ShelfItemVO productItem, Map<Integer, ProductM> productMMap, AtomicInteger index) {
        if(productItem.getItemType() == ProductTypeEnum.SPT_SPU.getType()
            || productItem.getItemType() == ProductTypeEnum.SKU.getType()){
            return;
        }
        int currentIndex = index.getAndIncrement();
        String productType = getType(shelfComponent.getSceneCode(), ctx);
        Map<String, Object> labsOceanMap = getLabsOceanMapByLab(productItem.getLabs());
        labsOceanMap.put(OceanConstantUtils.STATUS, getStatus(productAreaComponentVO.getDefaultShowNum(), currentIndex, filterBtnId, filterBtnIdsSelected, ctx, hasMultiFilterIdAndProductAreas));
        labsOceanMap.put(OceanConstantUtils.INDEX, currentIndex);
        labsOceanMap.put(OceanConstantUtils.PRICE, getPrice(productItem));
        labsOceanMap.put(OceanConstantUtils.PRICE_TITLE, getPriceTitle(productItem));
        // 先用后付标签上报
        labsOceanMap.put(OceanConstantUtils.LABEL_NAME, getLabelName(labsOceanMap, productItem));
        //到综搜推核心展位实时特征打点
        labsOceanMap.put(OceanConstantUtils.DZ_ABTEST, getRecRealTimeInfos(productMMap, productItem.getItemId()));
        //图片URL的上报
        labsOceanMap.put(OceanConstantUtils.PIC_URL, getPicUrl(productItem));
        //神会员打点上报
        paddingMagicalMemberLabs(labsOceanMap, productItem);
        //团购的商品ID，用deal_id承接商品打点
        if (StringUtils.isNotEmpty(productType) && productType.contains(OceanTypeEnums.DEAL.getDesc())) {
            labsOceanMap.put(OceanConstantUtils.DEAL_ID, productItem.getItemId());
        }else{
            //除了团购的其他商品用product_id承接商品打点
            labsOceanMap.put(OceanConstantUtils.PRODUCT_ID, productItem.getItemId());
        }
        productItem.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
    }

    private static Object getPriceTitle(ShelfItemVO itemVO) {
        String pricePrefix = itemVO.getSalePricePrefix();
        if (StringUtils.isNotEmpty(pricePrefix) && PRICE_PREFIX_LIST.contains(pricePrefix)) {
            return pricePrefix;
        }
        return StringUtils.EMPTY;
    }

    private static String getLabelName(Map<String, Object> labsOceanMap, ShelfItemVO productItem) {
        Object labelNameObj = labsOceanMap.get(OceanConstantUtils.LABEL_NAME);
        String labelName = labelNameObj != null ? String.valueOf(labelNameObj) : "";

        if (productItem == null || Objects.isNull(productItem.getProductTags()) || CollectionUtils.isEmpty(productItem.getProductTags().getTags())) {
            return labelName;
        }
        boolean hasPayLaterTag = productItem.getProductTags().getTags().stream().filter(Objects::nonNull).anyMatch(tag -> tag.getText().contains(payLaterTag));
        if (hasPayLaterTag) {
            return StringUtils.isNotEmpty(labelName) && !"-999".equals(labelName) ? labelName + "," + payLaterTag : payLaterTag;
        }
        return labelName;
    }

    public static List<Map<String, String>> getRecRealTimeInfos(Map<Integer, ProductM> productMMap, long itemId) {
        if (productMMap == null) {
            return Collections.emptyList();
        }
        ProductM productM = productMMap.get((int) itemId);
        if (productM == null) {
            return Collections.emptyList();
        }
        String recRealTimeInfos = productM.getAttr("recRealTimeInfos");
        if (StringUtils.isEmpty(recRealTimeInfos)) {
            return Collections.emptyList();
        }
        return Arrays.stream(recRealTimeInfos.split(","))
                .map(info -> info.split("#"))
                .filter(split -> split.length >= 2)
                .map(split -> {
                    Map<String, String> infoMap = new HashMap<>();
                    infoMap.put("dz_biz_id", split[0]);
                    infoMap.put("dz_query_id", split[1]);
                    return infoMap;
                })
                .collect(Collectors.toList());
    }

    public static Map<Integer, ProductM> getProductMMap(ActivityContext ctx) {
        CompletableFuture<ShelfGroupM> shelfGroupMCompletableFuture = ctx.getMainData();
        Map<String, ProductGroupM> productGroupMap = Optional.ofNullable(shelfGroupMCompletableFuture.join()).map(ShelfGroupM::getProductGroupMs).orElse(null);
        if (Objects.isNull(productGroupMap)) {
            return null;
        }
        int productType = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfType);
        ProductGroupM productGroupM = productGroupMap.get(getGroupName(productType));
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return null;
        }
        return productGroupM.getProducts().stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductM::getProductId, Function.identity(), (o1, o2) -> o1));
    }

    protected static String getGroupName(int productType) {
        return ShelfTypeEnums.getByType(productType).getDesc();
    }

    private static String getPicUrl(ShelfItemVO productItem) {
        if (Objects.isNull(productItem.getHeadPic()) || Objects.isNull(productItem.getHeadPic().getPic())) {
            return StringUtils.EMPTY;
        }
        return productItem.getHeadPic().getPic().getPicUrl();
    }

    private static void paddingMagicalMemberLabs(Map<String, Object> labsOceanMap, ShelfItemVO productItem) {
        if (CollectionUtils.isEmpty(productItem.getPriceBottomTags())) {
            return;
        }
        ShelfTagVO shelfTagVO = productItem.getPriceBottomTags()
                .stream()
                .filter(Objects::nonNull)
                .filter(tag -> MAGICAL_MEMBER_TAG_NAME.equals(tag.getName())).findFirst()
                .orElse(null);
        if (shelfTagVO != null) {
            labsOceanMap.put("god_label", 1);
            labsOceanMap.put("promotion_title", "神券" + (shelfTagVO.getPreText() != null ? shelfTagVO.getPreText().getText() : "")
                    + (shelfTagVO.getText() != null ? shelfTagVO.getText().getText() : ""));
        }
    }

    private static List<String> getFilterBtnIdsSelected(ShelfFilterVO filter) {
        if (filter == null
                || filter.getFilterRoot() == null
                || CollectionUtils.isEmpty(filter.getFilterRoot().getChildren())) {
            return Lists.newArrayList();
        }
        List<String> filterBtnIdsSelected = new ArrayList<>();
        filter.getFilterRoot().getChildren().forEach(filterBtn -> {
            //一级筛选项未选中直接返回
            if (!filterBtn.isSelected()) {
                return;
            }
            filterBtnIdsSelected.add(filterBtn.getFilterId());
            //添加孩子节点选中的筛选ID列表
            setChildrenFilterBtnIdsSelected(filterBtn.getChildren(), filterBtnIdsSelected);
        });
        //没有选中，默认选中第一个筛选项
        if (CollectionUtils.isEmpty(filterBtnIdsSelected)) {
            return buildFirstFilterBtnIdsSelected(filter.getFilterRoot().getChildren().get(0));
        }
        return filterBtnIdsSelected;
    }

    private static List<String> buildFirstFilterBtnIdsSelected(ShelfFilterNodeVO filterButtonVO) {
        if (filterButtonVO == null) {
            return new ArrayList<>();
        }
        List<String> filterBtnIdsSelected = new ArrayList<>();
        filterBtnIdsSelected.add(filterButtonVO.getFilterId());
        setChildrenFilterBtnIdsSelected(filterButtonVO.getChildren(), filterBtnIdsSelected);
        return filterBtnIdsSelected;
    }

    private static void setChildrenFilterBtnIdsSelected(List<ShelfFilterNodeVO> children, List<String> filterBtnIdsSelected) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        AtomicBoolean childrenFilterSelected = new AtomicBoolean(false);
        children.forEach(filterBtn -> {
//          // 一级筛选项未选中直接返回
            if (!filterBtn.isSelected()) {
                return;
            }
            childrenFilterSelected.set(true);
            filterBtnIdsSelected.add(filterBtn.getFilterId());
            //添加孩子节点选中的筛选ID列表
            setChildrenFilterBtnIdsSelected(filterBtn.getChildren(), filterBtnIdsSelected);
        });
        //孩子节点选中或第一个孩子节点无效返回
        if (childrenFilterSelected.get() || children.get(0) == null) {
            return;
        }
        //孩子节点未选中，默认选中第一个孩子节点
        filterBtnIdsSelected.add(children.get(0).getFilterId());
    }

    /**
     * 商品是否是默认展示，0-不是、1-是
     */
    private static int getStatus(int defaultShowNum, int index, String filterBtnId, List<String> filterBtnIdsSelected, ActivityContext ctx, boolean hasMultiFilterIdAndProductAreas) {
        //1. 非首屏直接返回0
        if (!shelfFirstLoad(ctx)) {
            return 0;
        }
        //2. filterBtnIdsSelected为空说明没有筛选项，默认列表页展示，此时根据展示个数和当前索引决定状态
        //3. filterBtnIdsSelected不为空说明有筛选项，只有选中的筛选根据展示个数和当前索引决定状态
        if (CollectionUtils.isEmpty(filterBtnIdsSelected) || filterBtnIdsSelected.contains(filterBtnId)) {
            return getStatusByShowNumAndIndex(defaultShowNum, index);
        }
        if (hasMultiFilterIdAndProductAreas) {
            return 0;
        }
        //4. 默认兜底：首屏选中的筛选项与"筛选按钮和商品区关系FilterIdAndProductAreas"中的filterId不匹配的场景
        return getStatusByShowNumAndIndex(defaultShowNum, index);
    }

    private static boolean shelfFirstLoad(ActivityContext ctx) {
        String currentChannel = ctx.getParam(ShelfActivityConstants.Params.channel);
        return ShelfActivityConstants.ChannelType.dealShelfFirstLoad.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.bookingFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.productFilter.equals(currentChannel);
    }

    private static int getStatusByShowNumAndIndex(int defaultShowNum, int index) {
        if (defaultShowNum > index) {
            return 1;
        }
        return 0;
    }

    private static String getPrice(ShelfItemVO itemVO) {
        String price = itemVO.getSalePrice();
        if (StringUtils.isNotEmpty(itemVO.getSalePrice())) {
            price = itemVO.getSalePrice();
        }
        if (StringUtils.isEmpty(price)) {
            return StringUtils.EMPTY;
        }
        return getPriceByFilterSpecialCharacter(price);
    }

    private static String getPriceByFilterSpecialCharacter(String price) {
        RegExCfgBean regExCfgBean = UnifiedOceanConfigUtils.getRegExConstant();
        if (regExCfgBean == null || StringUtils.isEmpty(regExCfgBean.getPriceRegEx())) {
            return price;
        }
        return Pattern.compile(regExCfgBean.getPriceRegEx()).matcher(price).replaceAll("").trim();
    }

    private static void paddingFilterLabsOcean(UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        if (shelfComponent.getFilter() == null || CollectionUtils.isEmpty(shelfComponent.getFilter().getFilterRoot().getChildren())) {
            return;
        }
        AtomicInteger index = new AtomicInteger(0);
        shelfComponent.getFilter().getFilterRoot().getChildren().forEach(filterBtn -> {
            if (filterBtn == null) {
                return;
            }
            int currentIndex = index.getAndIncrement();
            //填充二级筛选项
            paddingSecondFilter(filterBtn.getChildren(), ctx, shelfComponent, filterBtn, currentIndex);
        });
    }

    private static void paddingSecondFilter(List<ShelfFilterNodeVO> children, ActivityContext ctx, UnifiedShelfResponse shelfComponent, ShelfFilterNodeVO parentFilterBtn, int parentFilterIndex) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        children.forEach(childFilterBtn -> {
            if (childFilterBtn == null) {
                return;
            }
            Map<String, Object> labsOceanMap = getLabsOceanMapByLab(childFilterBtn.getLabs());
            labsOceanMap.put(OceanConstantUtils.TAB_NAME, getTitle(parentFilterBtn));
            labsOceanMap.put(OceanConstantUtils.TAB_INDEX, parentFilterIndex);
            childFilterBtn.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        });
    }

    private static String getTitle(ShelfFilterNodeVO filterButton) {
        String title = getFilterText(filterButton.getTitle());
        if (StringUtils.isEmpty(title)) {
            return StringUtils.EMPTY;
        }
        return title;
    }

    private static String getFilterText(IconRichLabelModel richLabelVO) {
        if (richLabelVO == null || richLabelVO.getText() == null) {
            return StringUtils.EMPTY;
        }
        return richLabelVO.getText().getText();
    }

    private static String getFilterImg(IconRichLabelModel richLabelVO) {
        if (richLabelVO == null || richLabelVO.getIcon() == null) {
            return StringUtils.EMPTY;
        }
        return richLabelVO.getIcon().getPicUrl();
    }

    private static void paddingWholeLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        //整个货架打点如果为空，则进行初始化
        if (shelfOceanVO.getWholeShelf() == null) {
            shelfOceanVO.setWholeShelf(new ShelfOceanEntryVO());
        }
        //获取个性化labs数据
        Map<String, Object> labsOceanMap = getWholeLabsOceanMap(shelfOceanVO.getWholeShelf(), shelfComponent, ctx);
        shelfOceanVO.getWholeShelf().setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        ShelfOceanVO shelfOcean = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOcean != null && shelfOcean.getWholeShelf() != null) {
            shelfOceanVO.getWholeShelf().setCategory(shelfOcean.getWholeShelf().getCategory());
            shelfOceanVO.getWholeShelf().setBidClick(shelfOcean.getWholeShelf().getBidClick());
            shelfOceanVO.getWholeShelf().setBidView(shelfOcean.getWholeShelf().getBidView());
        }
    }

    private static Map<String, Object> getWholeLabsOceanMap(ShelfOceanEntryVO shelfOceanEntryVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        //获取个性化labs数据
        Map<String, Object> labsOceanMap = getLabsOceanMap(shelfOceanEntryVO);
        //设置公共打点数据
        labsOceanMap.put(OceanConstantUtils.POI_ID, getPoiId(ctx));
        // poiid加密 有开关
        if (Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE) == null || !"false".equals(Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE))) {

            if (getPoiId(ctx) > 0 && !LionConfigHelper.isSigDegrade()) {
                Long poiId = getPoiId(ctx);
                labsOceanMap.put(OceanConstantUtils.POI_ID_ENCRYPT, SigCryptUtils.encryptPoiId(poiId));
            }
        }
        String type = getType(shelfComponent.getSceneCode(), ctx);
        labsOceanMap.put(OceanConstantUtils.MODULE_NAME, getModuleName(shelfComponent, type));
        labsOceanMap.put(OceanConstantUtils.TYPE, type);
        labsOceanMap.put(OceanConstantUtils.SPU_TYPE, getSpuType(shelfComponent.getFilterIdAndProductAreas()));
        labsOceanMap.put(OceanConstantUtils.CHIMERA_COMMON, buildChimeraCommonLab(shelfOceanEntryVO, shelfComponent.getSceneCode()));
        return labsOceanMap;
    }

    private static String buildChimeraCommonLab(ShelfOceanEntryVO shelfOceanEntryVO, String sceneCode) {
        Map<String, String> chimeraCommonLabMap = new HashMap<>();
        chimeraCommonLabMap.put(OceanConstantUtils.SCENE_CODE, sceneCode);
        if (StringUtils.isNotEmpty(shelfOceanEntryVO.getBidClick())) {
            chimeraCommonLabMap.put(OceanConstantUtils.OLD_BID_MC, shelfOceanEntryVO.getBidClick());
        }
        if (StringUtils.isNotEmpty(shelfOceanEntryVO.getBidView())) {
            chimeraCommonLabMap.put(OceanConstantUtils.OLD_BID_MV, shelfOceanEntryVO.getBidView());
        }
        return JsonCodec.encode(chimeraCommonLabMap);
    }

    private static ShelfOceanEntryVO getAndSetOceanLabsEntry(ShelfOceanEntryVO shelfOceanEntryVO, ShelfProductAreaVO productAreaComponentVO, UnifiedShelfResponse shelfComponent,
                                                             ActivityContext ctx, ShelfOceanEntryVO shelfOceanEntryConfig, Map<String, Object> oceanEntryMap) {
        if (shelfOceanEntryVO == null) {
            shelfOceanEntryVO = new ShelfOceanEntryVO();
        }
        Map<String, Object> labsOceanMap = getLabsOceanMapByLab(shelfOceanEntryVO.getLabs());
        if (MapUtils.isNotEmpty(oceanEntryMap)) {
            labsOceanMap.putAll(oceanEntryMap);
        }
        labsOceanMap.put(OceanConstantUtils.POI_ID, getPoiId(ctx));
        // poiid加密 有开关
        if (Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE) == null || !"false".equals(Tracer.getContext(PoiIdUtil.POIID_CRYPTO_ENABLE))) {
            if (getPoiId(ctx) > 0 && !LionConfigHelper.isSigDegrade()) {
                Long poiId = Long.valueOf(getPoiId(ctx));
                labsOceanMap.put(OceanConstantUtils.POI_ID_ENCRYPT, SigCryptUtils.encryptPoiId(poiId));
            }
        }
        labsOceanMap.put(OceanConstantUtils.CITY_ID, getCityId(ctx));
        String type = getType(shelfComponent.getSceneCode(), ctx);
        labsOceanMap.put(OceanConstantUtils.TYPE, type);
        labsOceanMap.put(OceanConstantUtils.MODULE_NAME, getModuleName(shelfComponent, type));
        labsOceanMap.put(OceanConstantUtils.CHIMERA_COMMON, buildChimeraCommonLab(shelfOceanEntryVO, shelfComponent.getSceneCode()));
        ShelfOceanEntryVO currentShelfOcean = new ShelfOceanEntryVO();
        currentShelfOcean.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
        currentShelfOcean.setAbtest(shelfOceanEntryVO.getAbtest());
        if (shelfOceanEntryConfig == null) {
            currentShelfOcean.setBidView(shelfOceanEntryVO.getBidView());
            currentShelfOcean.setBidClick(shelfOceanEntryVO.getBidClick());
            currentShelfOcean.setCategory(shelfOceanEntryVO.getCategory());
            return currentShelfOcean;
        }
        currentShelfOcean.setBidView(shelfOceanEntryConfig.getBidView());
        currentShelfOcean.setBidClick(shelfOceanEntryConfig.getBidClick());
        currentShelfOcean.setCategory(shelfOceanEntryConfig.getCategory());
        return currentShelfOcean;
    }

    private static String getType(String sceneCode, ActivityContext ctx) {
        //首先返回场景自定义的类型
        String type = UnifiedOceanConfigUtils.getTypeByConfig(sceneCode);
        if (StringUtils.isNotEmpty(type)) {
            return type;
        }
        String currentChannel = ctx.getParam(ShelfActivityConstants.Params.channel);
        //团购
        if (ShelfActivityConstants.ChannelType.dealShelfFirstLoad.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.dealShelfListForTab.equals(currentChannel)) {
            return OceanTypeEnums.DEAL.getDesc();
        }
        //预付
        if (ShelfActivityConstants.ChannelType.productFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.productProducts.equals(currentChannel)) {
            return OceanTypeEnums.PREPAY.getDesc();
        }
        //预定
        if (ShelfActivityConstants.ChannelType.bookingFilter.equals(currentChannel)
                || ShelfActivityConstants.ChannelType.bookingProducts.equals(currentChannel)) {
            return OceanTypeEnums.BOOK.getDesc();
        }
        return null;
    }

    private static String getModuleName(UnifiedShelfResponse shelfComponent, String type) {
        if (shelfComponent.getMainTitle() == null || StringUtils.isEmpty(shelfComponent.getMainTitle().getTitle())) {
            //兜底商品类型
            return type;
        }
        //货架主标题名称
        return shelfComponent.getMainTitle().getTitle().trim();
    }

    private static int getSpuType(List<ShelfFilterProductAreaVO> filterProductAreaVOS) {
        if (CollectionUtils.isEmpty(filterProductAreaVOS)) {
            return 0;
        }
        boolean hasSpuCard = filterProductAreaVOS.stream().flatMap(filterProductAreaVO -> filterProductAreaVO.getProductAreas().stream())
                .filter(productAreaVO -> CollectionUtils.isNotEmpty(productAreaVO.getItems()))
                .flatMap(productAreaVO -> productAreaVO.getItems().stream())
                .anyMatch(productItem -> productItem.getItemType() == ProductTypeEnum.SPT_SPU.getType()
                || (productItem.getItemType() == ProductTypeEnum.DEAL.getType() && CollectionUtils.isNotEmpty(productItem.getSubItems())));
        return hasSpuCard ? 1 : 0;
    }

    private static ShelfProductAreaVO getFirstShelfProductAreaVO(UnifiedShelfResponse shelfComponent) {
        if (CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0) == null
                || CollectionUtils.isEmpty(shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas())
                || shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(0) == null) {
            return new ShelfProductAreaVO();
        }
        return shelfComponent.getFilterIdAndProductAreas().get(0).getProductAreas().get(0);
    }

    public static long getPoiId(ActivityContext ctx) {
        return ParamsUtil.getLongShopIdForShelfActivity(ctx);
    }

    private static int getCityId(ActivityContext ctx) {
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform))) {
            return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.mtCityId);
        }
        return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.dpCityId);
    }

    private static Map<String, Object> getLabsOceanMap(ShelfOceanEntryVO shelfOceanEntryVO) {
        if (shelfOceanEntryVO == null) {
            return new HashMap<>();
        }
        return getLabsOceanMapByLab(shelfOceanEntryVO.getLabs());
    }

    private static Map<String, Object> getLabsOceanMapByLab(String labs) {
        Map<String, Object> map = JsonCodec.decode(labs, new TypeReference<Map<String, Object>>() {
        });
        if (MapUtil.isEmpty(map)) {
            return new HashMap<>();
        }
        return map;
    }

    private static void paddingReserveMindBarLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO reserveMindBarByConfig = getReserveMindBarByConfig(ctx);
        shelfOceanVO.setReserveMindBar(getAndSetOceanLabsEntry(shelfOceanVO.getReserveMindBar(), productAreaComponent, shelfComponent, ctx, reserveMindBarByConfig, null));
    }

    private static void paddingReserveMindSupernatantLabsOcean(ShelfOceanVO shelfOceanVO, UnifiedShelfResponse shelfComponent, ActivityContext ctx) {
        ShelfProductAreaVO productAreaComponent = getFirstShelfProductAreaVO(shelfComponent);
        ShelfOceanEntryVO reserveMindSupernatantByConfig = getReserveMindSupernatantByConfig(ctx);
        shelfOceanVO.setReserveMindSupernatant(getAndSetOceanLabsEntry(shelfOceanVO.getReserveMindSupernatant(), productAreaComponent, shelfComponent, ctx, reserveMindSupernatantByConfig, null));
    }

    private static ShelfOceanEntryVO getReserveMindBarByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getReserveMindBar();
    }

    private static ShelfOceanEntryVO getReserveMindSupernatantByConfig(ActivityContext ctx) {
        ShelfOceanVO shelfOceanByConfig = UnifiedOceanConfigUtils.getShelfOceanByPlatform(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform));
        if (shelfOceanByConfig == null) {
            return null;
        }
        return shelfOceanByConfig.getReserveMindSupernatant();
    }



}