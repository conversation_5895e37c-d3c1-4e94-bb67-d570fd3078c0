package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPriceBottomTagVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@VPointOption(name = "商品-配置化价格下方标签",
        description = "标准化扩展选项，根据配置策略组合标签",
        code = ConfigItemPriceBottomTagOpt.CODE)
public class ConfigItemPriceBottomTagOpt extends UnifiedShelfItemPriceBottomTagVP<ConfigItemPriceBottomTagOpt.Config> {

    public static final String CODE = "ConfigItemPriceBottomTagOpt";

    @Autowired
    private PriceBottomTagStrategyFactory strategyFactory;

    @Override
    public List<ShelfTagVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getStrategies())) {
            return null;
        }
        return buildTagList(context, param, config);
    }

    private List<ShelfTagVO> buildTagList(ActivityCxt context, Param param, Config config) {
        List<ShelfTagVO> dzTagVOS = Lists.newArrayList();
        for (List<String> strategies : config.getStrategies()) {
            ShelfTagVO dzTagVO = null;
            for (String strategy : strategies) {
                PriceBottomTagStrategy buildStrategy = strategyFactory.getBuildStrategy(strategy);
                if (buildStrategy == null) {
                    continue;
                }
                PriceBottomTagBuildReq req = buildReq(strategy, param, config, context);
                dzTagVO = buildStrategy.build(req);
                if (dzTagVO != null) {
                    break;
                }
            }
            if (dzTagVO != null) {
                dzTagVOS.add(dzTagVO);
            }
        }
        return dzTagVOS;
    }

    private PriceBottomTagBuildReq buildReq(String strategy, Param param, Config config, ActivityCxt context) {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(param.getProductM());
        req.setCardM(param.getCardM());
        req.setPlatform(param.getPlatform());
        req.setSalePrice(param.getSalePrice());
        if (MapUtils.isNotEmpty(config.getStrategyConfig())) {
            req.setCfg(config.getStrategyConfig().get(strategy));
        }
        req.setContext(context);
        req.setHiddenPreText(hitFShelfAndShopCategory(context, config));
        return req;
    }

    private static boolean hitFShelfAndShopCategory(ActivityCxt context, Config config) {
        ShopM shop = context.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shop == null || CollectionUtils.isEmpty(shop.getBackCategory())
                || CollectionUtils.isEmpty(config.getHiddenPreTextBackCategoryInFShelf())) {
            return false;
        }

        return isShopCategorySupported(shop.getBackCategory(), config.getHiddenPreTextBackCategoryInFShelf())
                && ParamsUtil.getBooleanSafely(context.getParameters(), ShelfActivityConstants.Params.isFShelf);
    }

    private static boolean isShopCategorySupported(List<Integer> shopBackCategories,
            List<Integer> supportedCategories) {
        return shopBackCategories.stream().anyMatch(supportedCategories::contains);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 策略列表，一维为标签位，二维为对应策略优先级
         * 格式：[["a","b"],["c","d"]]，表示第一个标签位按a、b策略优先级构造，第二个标签位按c、d策略优先级构造
         */
        private List<List<String>> strategies;

        /**
         * 策略构造标签配置
         */
        private Map<String, PriceBottomTagBuildCfg> strategyConfig;

        /**
         * 需要在F型货架隐藏preText的后台类目
         */
        private List<Integer> hiddenPreTextBackCategoryInFShelf;
    }
}