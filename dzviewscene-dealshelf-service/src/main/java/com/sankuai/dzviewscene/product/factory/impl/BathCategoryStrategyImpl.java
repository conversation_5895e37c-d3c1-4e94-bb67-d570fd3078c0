package com.sankuai.dzviewscene.product.factory.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BathServiceFacilityUtils;
import com.sankuai.dzviewscene.product.factory.AbstractDealCategoryStrategy;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/16 14:08
 */
@Service("bathCategoryStrategyImpl")
public class BathCategoryStrategyImpl extends AbstractDealCategoryStrategy {

    @Override
    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        if (CollectionUtils.isEmpty(assembleCfg.getNewModuleList())) {
            return assembleCfg.getModuleList();
        }
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (dealDetailInfoModel != null
                && CollectionUtils.isNotEmpty(BathServiceFacilityUtils.parseServiceFacility(dealDetailInfoModel.getDealAttrs()))) {
            return assembleCfg.getNewModuleList();
        }
        if (hitAbForManySks(activityCxt, assembleCfg)) {
            return assembleCfg.getNewModuleList();
        }
        return assembleCfg.getModuleList();
    }

}
