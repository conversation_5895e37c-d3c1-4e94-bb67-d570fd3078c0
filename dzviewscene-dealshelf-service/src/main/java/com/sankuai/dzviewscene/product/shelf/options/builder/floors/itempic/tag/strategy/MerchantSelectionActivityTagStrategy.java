package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class MerchantSelectionActivityTagStrategy extends AbstractFloatTagBuildStrategy {
    private static final String MEDICAL_CORNER_LABEL = "medicalCornerLabel";

    @Override
    public String getName() {
        return MerchantSelectionActivityTagStrategy.class.getSimpleName();
    }

    @Override
    boolean isMatch(FloatTagBuildReq param, FloatTagBuildCfg config) {
        String attr = param.getProductM().getAttr(MEDICAL_CORNER_LABEL);
        return StringUtils.isNotBlank(attr);
    }

    @Override
    FloatTagVO buildTag(FloatTagBuildReq param, FloatTagBuildCfg config) {
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        String attr = param.getProductM().getAttr(MEDICAL_CORNER_LABEL);
        floatTagPic.setPicUrl(attr);
        floatTagPic.setAspectRadio(config.getPicAspectRadio() > 0 ? config.getPicAspectRadio() : 2.77778);
        if (config.getHeight() > 0) {
            floatTagPic.setPicHeight(config.getHeight());
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        return floatTagVO;
    }
}
