package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.carouselmsg;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.ButtonCarouselMsgStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel.CarouselBuilderContext;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemCarouselMsgVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "聚合卡片-轮播信息",
        description = "聚合卡片-轮播信息",
        code = CommonAggregateItemCarouselMsgOpt.CODE,
        isDefault = true)
public class CommonAggregateItemCarouselMsgOpt extends AggregateItemCarouselMsgVP<CommonAggregateItemCarouselMsgOpt.Config> {

    public static final String CODE = "CommonAggregateItemCarouselMsgOpt";

    @Autowired
    private ButtonCarouselMsgStrategyFactory carouselMsgStrategyFactory;

    @Override
    public List<CarouselMsg> compute(ActivityCxt activityCxt, Param param, Config config) {
        if(Objects.isNull(param) || CollectionUtils.isEmpty(config.getCarouselStrategy())){
            return null;
        }
        ProductM productM = param.getProductMMap().get(param.getNodeM().getIdentityKey());
        List<ProductM> children = param.getNodeM().getChildren().stream()
                .map(childNode -> param.getProductMMap().get(childNode.getIdentityKey()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<CarouselMsg> results = Lists.newArrayList();
        CarouselBuilderContext builderContext = new CarouselBuilderContext();
        builderContext.setProductM(productM);
        builderContext.setChildrenProductM(children);
        for(String strategyName : config.getCarouselStrategy()){
            ButtonCarouselMsgStrategy carouselMsgStrategy = carouselMsgStrategyFactory.getStrategy(strategyName);
            if(Objects.isNull(carouselMsgStrategy)){
                continue;
            }
            CarouselMsg carouselMsg = carouselMsgStrategy.unifiedBuild(builderContext);
            if(Objects.nonNull(carouselMsg)){
                results.add(carouselMsg);
            }
        }
        return results;
    }

    @VPointCfg
    @Data
    public static class Config {

        //默认累计销量
        private List<String> carouselStrategy = Lists.newArrayList("AggregateSaleMsgStrategy");
    }
}
