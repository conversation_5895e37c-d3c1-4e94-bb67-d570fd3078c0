/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-13
 * Project        :
 * File Name      : CarouselMsgTypeEnum.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.unifiedshelf.enums;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-13
 * @since dzviewscene-dealshelf-home 1.0
 */
public enum CarouselMsgTypeEnum {

    DEFAULT(0, "默认"),

    SALE_MSG(1,"销量信息"),
    
    PURCHASE_MSG(2,"购买信息"),

    COUNTDOWN_MSG(3,"倒计时信息");


    private int type;

    private String desc;
    
    CarouselMsgTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }
}
