package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@VPointOption(name = "代金券副标题",
        description = "代金券副标题",
        code = UnifiedShelfItemVoucherSubTitleOpt.CODE)
public class UnifiedShelfItemVoucherSubTitleOpt extends UnifiedShelfItemSubTitleVP<UnifiedShelfItemVoucherSubTitleOpt.Config> {

    public static final String CODE = "UnifiedShelfItemVoucherSubTitleOpt";

    private static final String VOUCHER_ATTR_AVAILABLE_TIME = "voucherAttrAvailableTime";

    private static final String VOUCHER_ATTR_USING_LIMIT = "voucherAttrUsingLimit";

    private static final String VOUCHER_ATTR_ALL_PRODUCT = "voucherAttrAllProduct";

    private static final String VOUCHER_ATTR_RESERVATION = "voucherAttrReservation";

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt activityCxt, Param param, Config config) {
        return build4Default(buildVoucherTag(param.getProductM()));
    }

    private List<String> buildVoucherTag(ProductM productM) {
        return Lists.newArrayList(productM.getAttr(VOUCHER_ATTR_AVAILABLE_TIME),
                productM.getAttr(VOUCHER_ATTR_USING_LIMIT),
                productM.getAttr(VOUCHER_ATTR_ALL_PRODUCT),
                productM.getAttr(VOUCHER_ATTR_RESERVATION)).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemSubTitleVP.Config {
    }
}
