package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.title;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateItemTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "默认-聚合卡片标题",
        description = "spu卡片取spu标题，其他情况取item标题",
        code = DefaultAggregateItemTitleOpt.CODE,
        isDefault = true)
public class DefaultAggregateItemTitleOpt extends AggregateItemTitleVP<DefaultAggregateItemTitleOpt.Config> {

    public static final String CODE = "DefaultAggregateItemTitleOpt";

    @Override
    public List<StyleTextModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            return buildSpuTitle(param.getNodeM(), param.getProductMMap());
        }
        ProductM productM = param.getProductMMap().get(param.getNodeM().getIdentityKey());
        if(productM == null || StringUtils.isBlank(productM.getTitle())) {
            return null;
        }
        return build4text(productM.getTitle());
    }

    private List<StyleTextModel> buildSpuTitle(ProductHierarchyNodeM nodeM, Map<String, ProductM> productMMap) {
        DealProductSpuDTO dealProductSpuDTO = ProductHierarchyUtils.getSpuNodeModel(nodeM, productMMap);
        if(dealProductSpuDTO == null || StringUtils.isBlank(dealProductSpuDTO.getSpuName())){
            return null;
        }
        return build4text(dealProductSpuDTO.getSpuName());
    }

    public static List<StyleTextModel> build4text(String title) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(title);
        return Lists.newArrayList(styleTextModel);
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}