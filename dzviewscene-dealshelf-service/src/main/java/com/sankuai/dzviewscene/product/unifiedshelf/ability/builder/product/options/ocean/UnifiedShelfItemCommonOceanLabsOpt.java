package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy.MarketingActivityStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ShopUserProfileUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "通用-通用商品埋点",
        description = "返回通用的商品埋点",
        code = "UnifiedShelfItemCommonOceanLabsOpt",
        isDefault = true)
public class UnifiedShelfItemCommonOceanLabsOpt extends UnifiedShelfItemOceanLabsVP<UnifiedShelfItemCommonOceanLabsOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            return buildSpuLabs(context, param, config);
        }
        if(ProductHierarchyUtils.isSkuNode(param.getNodeM())){
            return buildSkuLabs(context, param, config);
        }
        return buildProductLabs(context, param, config);
    }

    private String buildSpuLabs(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = getSpuOcean(param);
        //副标题标签类型
        paddingLabelType(oceanMap, param.getProductM(), config,param.getShelfItemVO());
        return JsonCodec.encode(oceanMap);
    }

    private String buildSkuLabs(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = getSkuOcean(context, param);
        //副标题标签类型
        paddingLabelType(oceanMap, param.getProductM(), config,param.getShelfItemVO());
        return JsonCodec.encode(oceanMap);
    }

    private String buildProductLabs(ActivityCxt context, Param param, Config config){
        Map<String, Object> oceanMap = getCommonOcean(context, param);
        if (config.isEnableTopDisplayInfoLabs()) {
            paddingTopDisplayInfo(oceanMap, param.getProductM());
        }
        if (config.isEnableCardLabs()) {
            paddingCardLab(oceanMap, param.getCardM());
        }
        //特色标签
        paddingSpecialTag(oceanMap, param.getShelfItemVO(), param.getProductM());
        //是否spu商品
        paddingSpuType(oceanMap, param);
        //副标题标签类型
        paddingLabelType(oceanMap, param.getProductM(), config,param.getShelfItemVO());
        return JsonCodec.encode(oceanMap);
    }

    public void paddingLabelType(Map<String, Object> oceanMap, ProductM productM, Config config,ShelfItemVO shelfItemVO) {
        if (!config.isEnableLabelTypeLabs() || productM == null || shelfItemVO == null) {
            return;
        }
        //营销标签，商品属性，ugc推荐语
        String activity = getProductActivityM(productM);
        List<String> labelType = Lists.newArrayList();
        String special = Strings.EMPTY;
        //ugc标签
        List<String> ugcTags = PriceAboveTagsUtils.getRecommendTag(productM);
        String ugcTag = CollectionUtils.isNotEmpty(ugcTags) ? ugcTags.get(0) : null;
        //竞争圈标签
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(productM);
        if(StringUtils.isNotEmpty(tradeRateTag)){
            //ugc/竞争圈标签
            special = tradeRateTag;
        } else if(StringUtils.isNotEmpty(ugcTag)){
            special = ugcTag;
        }
        ItemSubTitleVO productTags = shelfItemVO.getProductTags();
        if(productTags.getIconTag() != null && productTags.getIconTag().getIcon() != null){
            String picUrl = productTags.getIconTag().getIcon().getPicUrl();
            if(StringUtils.isNotBlank(picUrl) && picUrl.equals(activity)){
                labelType.add("营销标签");
            }
        }
        List<String> tags = productM.getProductTags();
        if(CollectionUtils.isEmpty(tags) && CollectionUtils.isNotEmpty(labelType)){
            oceanMap.put("label_type", String.join(",", labelType));
            return;
        }
        for(String tag : tags){
            if(tag.equals(activity)){
                labelType.add("营销标签");
            }
            if(tag.equals(special)){
                labelType.add("推荐语");
                break;
            } else {
                labelType.add("商品属性");
            }
        }
        if(CollectionUtils.isEmpty(labelType)){
            return;
        }
        oceanMap.put("label_type", String.join(",", labelType));

    }

    public String getProductActivityM(ProductM productM) {
        List<ProductActivityM> productActivityMS = productM.getActivities();
        if (CollectionUtils.isEmpty(productActivityMS)) {
            return Strings.EMPTY;
        }
        ProductActivityM activityM = getProductActivityM(productActivityMS);
        if (activityM == null) {
            return Strings.EMPTY;
        }
        Map<String, ActivityPicUrlDTO> activityPicUrlMap = activityM.getActivityPicUrlMap();
        if (MapUtils.isNotEmpty(activityPicUrlMap)) {
            ActivityPicUrlDTO activityPicUrlDTO = activityPicUrlMap.get(ExposurePicUrlKeyEnum.ICON.getKey());
            if (activityPicUrlDTO != null && StringUtils.isNotBlank(activityPicUrlDTO.getUrl())) {
                // 图片
                return activityPicUrlDTO.getUrl();
            }
            Map<String, String> textMap = activityM.getTextMap();
            // 业务级大促使用
            String labelText = textMap.get("labelText");
            if (StringUtils.isNotBlank(labelText)) {
                return labelText;
            }
        }
        return Strings.EMPTY;
    }

    public ProductActivityM getProductActivityM(List<ProductActivityM> productActivityMS) {
        Map<Integer, ProductActivityM> activityMap = productActivityMS.stream().collect(Collectors
                .toMap(ProductActivityM::getExposurePromotionType, item -> item, (v1, v2) -> v1));
        for(Integer promotionType : MarketingActivityStrategy.exposurePromotionTypes) {
            ProductActivityM productActivityM = activityMap.get(promotionType);
            if (Objects.nonNull(productActivityM)) {
                return productActivityM;
            }
        }
        return null;
    }

    /**
     * 特色标签
     *
     * @param source
     * @param shelfItemVO
     * @param productM
     */
    private void paddingSpecialTag(Map<String, Object> source, ShelfItemVO shelfItemVO, ProductM productM) {
        if (shelfItemVO.getSpecialTags() == null || CollectionUtils.isEmpty(shelfItemVO.getSpecialTags().getTags())) {
            source.put("UGC_tag", -999);
            source.put("trade_rate_tag", -999);
            return;
        }
        RichLabelModel specialTag = shelfItemVO.getSpecialTags().getTags().get(0).getText();
        if (specialTag == null || StringUtils.isBlank(specialTag.getText())) {
            return;
        }
        //ugc标签
        List<String> ugcTags = PriceAboveTagsUtils.getRecommendTag(productM);
        String ugcTag = CollectionUtils.isNotEmpty(ugcTags) ? ugcTags.get(0) : null;
        source.put("UGC_tag", specialTag.getText().equals(ugcTag) ? 0 : -999);
        //竞争圈标签
        String tradeRateTag = PriceAboveTagsUtils.getTradeRateTag(productM);
        source.put("trade_rate_tag", specialTag.getText().equals(tradeRateTag) ? 0 : -999);
    }

    private void paddingSpuType(Map<String, Object> source, Param param) {
        if(param.getNodeM() == null || param.getNodeM().getParent() == null){
            return;
        }
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM().getParent())){
            source.put("spu_type", 1);
        }
    }

    /**
     * 折扣和比价标签
     *
     * @param source
     * @param productM
     */
    public void paddingDiscountAndComparePriceLab(Map<String, Object> source, ProductM productM) {
        String discountTag = productM.getAttr(PriceDisplayUtils.ITEM_DISCOUNT_TAG);
        if (org.apache.commons.lang.StringUtils.isNotBlank(discountTag)) {
            source.put("count", discountTag);
        }
        String comparePriceTag = productM.getAttr(PriceDisplayUtils.ITEM_PRICE_POWER_TAG);
        if (org.apache.commons.lang.StringUtils.isNotBlank(comparePriceTag)) {
            source.put("label_text", comparePriceTag);
        }
    }

    private void paddingTopDisplayInfo(Map<String, Object> oceanMap, ProductM productM) {
        //是否堆头
        boolean isTopDisplay = ProductMAttrUtils.isTopDisplayProduct(productM);
        //0-普通团购，1-堆头
        oceanMap.put("module_show_type", isTopDisplay ? "1" : "0");
        oceanMap.put("type", ProductTypeEnum.getByType(productM.getProductType()).getDesc());
        oceanMap.put("module_name", "团购货架");
    }

    private void paddingCardLab(Map<String, Object> source, CardM cardM) {
        if (cardM == null) {
            return;
        }
        source.put("membercard_type", ShopUserProfileUtils.buildProfile(cardM.getShopCardList()));
        source.put("u_profile", ShopUserProfileUtils.buildProfile(cardM.getUserCardList()));
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 是否启用「堆头商品」打点
         */
        private boolean enableTopDisplayInfoLabs = false;

        /**
         * 是否启用会员卡信息打点
         */
        private boolean enableCardLabs = false;

        /**
         * spu货架实验
         */
        private List<String> spuExps;

        /**
         * 标签类型
         */
        private boolean enableLabelTypeLabs = false;
    }
}
