package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffix;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePriceSuffixVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "价格后缀-根据不同条件返回",
        description = "根据不同条件返回价格后缀，如/3次",
        code = ConditionSalePriceSuffixOpt.CODE)
public class ConditionSalePriceSuffixOpt extends UnifiedShelfSalePriceSuffixVP<ConditionSalePriceSuffixOpt.Config> {

    public static final String CODE = "ConditionSalePriceSuffixOpt";

    private static final String TIMES_KEY = "sys_multi_sale_number";

    private static final String CAMPAIGN_PRICE_DESC = "campaginPriceDesc";

    private static final String IS_MULTI_SKU_KEY = "dealMultiSkuFieldAttr";

    private static final Map<String, SuffixTextBuilder> builderMap = Maps.newHashMap();

    static {
        builderMap.put(SuffixType.TIMES.getCode(), getTimesDesc());
        builderMap.put(SuffixType.ADDITIONAL.getCode(), getAdditionalDesc());
        builderMap.put(SuffixType.CAMPAIGN_DESC.getCode(), getCampaignPriceDesc());
        builderMap.put(SuffixType.MULTI_SKU.getCode(), getMultiSkuDesc());
        builderMap.put(SuffixType.MINIPROGRAM.getCode(), getMiniprogramDesc());
        builderMap.put(SuffixType.CONTINUOUS_MONTHLY.getCode(), getContinuousMonthlyDesc());
    }

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getSuffixTypes())) {
            return null;
        }
        for (String suffixType : config.getSuffixTypes()) {
            SuffixTextBuilder suffixTextBuilder = builderMap.get(suffixType);
            if (suffixTextBuilder == null) {
                continue;
            }
            String suffix = suffixTextBuilder.build(context, param);
            if (StringUtils.isNotBlank(suffix)) {
                return suffix;
            }
        }
        return null;
    }

    /**
     * 加项后缀
     */
    private static SuffixTextBuilder getAdditionalDesc(){
        return (context, param) -> {
            return param.getProductM().isAdditionalDeal() ? "起" : null;
        };
    }

    /**
     * 次数
     */
    private static SuffixTextBuilder getTimesDesc(){
        return (context, param) -> {
            String attr = param.getProductM().getAttr(TIMES_KEY);
            if (StringUtils.isBlank(attr) || !NumberUtils.isDigits(attr)) {
                return null;
            }
            return String.format("/%s次", attr);
        };
    }

    /**
     * 团建后缀
     */
    private static SuffixTextBuilder getCampaignPriceDesc() {
        return (context, param) -> {
            return param.getProductM().getAttr(CAMPAIGN_PRICE_DESC);
        };
    }

    /**
     * 多SKU价格后缀
     */
    private static SuffixTextBuilder getMultiSkuDesc() {
        return (context, param) -> {
            String isMultiSku = param.getProductM().getAttr(IS_MULTI_SKU_KEY);
            return "true".equals(isMultiSku) ? "起" : null;
        };
    }

    /**
     * 接小程序商品价格后缀
     * @return
     */
    private static SuffixTextBuilder getMiniprogramDesc() {
        return (context, param) -> {
            return ApplianceShelfUtils.isMiniprogramLinked(param.getProductM()) ? "起" : null;
        };
    }
    
    /**
     * 连续包月价格后缀
     * @return
     */
    private static SuffixTextBuilder getContinuousMonthlyDesc() {
        return (context, param) -> {
            if (TimesDealUtil.isContinuousMonthly(param.getProductM())) {
                return "/月";
            }
            return null;
        };
    }

    @FunctionalInterface
    public interface SuffixTextBuilder {
        String build(ActivityCxt context, Param param);
    }

    public enum SuffixType {
        ADDITIONAL("additional", "加项团单"),
        TIMES("times",  "多次卡次数"),
        CAMPAIGN_DESC("campaign", "团建后缀"),
        MULTI_SKU("multiSku", "多SKU后缀"),
        MINIPROGRAM("miniprogram", "接小程序后缀"),
        CONTINUOUS_MONTHLY("continuousMonthly", "连续包月后缀")
        ;

        @Getter
        private String code;
        private String desc;

        SuffixType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 后缀类型，按顺序配置优先级，配置值见SuffixType枚举
         */
        private List<String> suffixTypes;
    }
}
