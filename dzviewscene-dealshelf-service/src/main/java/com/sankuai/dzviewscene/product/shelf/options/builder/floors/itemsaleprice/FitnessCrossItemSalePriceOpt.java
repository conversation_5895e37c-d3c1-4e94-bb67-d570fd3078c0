package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "运健-健身通团购价格",
        description = "返回：0",
        code = "FitnessCrossItemSalePriceVP")
public class FitnessCrossItemSalePriceOpt extends ItemSalePriceVP<FitnessCrossItemSalePriceOpt.Config> {

    private static final String DEFAULT_TEXT = "0";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return StringUtils.isEmpty(config.getText()) ? DEFAULT_TEXT : config.getText();
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 价格文案：“0”
         */
        private String text;

    }

}
