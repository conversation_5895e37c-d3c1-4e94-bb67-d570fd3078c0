package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "价格前缀-根据不同条件返回",
        description = "根据不同条件返回价格前缀，如「焕新价」",
        code = ConditionSalePricePrefixOpt.CODE)
public class ConditionSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<ConditionSalePricePrefixOpt.Config> {

    public static final String CODE = "ConditionSalePricePrefixOpt";

    private static final Map<String, PrefixTextBuilder> builderMap = Maps.newHashMap();

    static {
        builderMap.put(PrefixType.MINIPROGRAM.getCode(), getMiniprogramDesc());
        builderMap.put(PrefixType.USED_NATIONAL_SUBSIDY.getCode(), getUsedNationalSubsidyDesc());
    }

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getPrefixTypes())) {
            return null;
        }
        for (String prefixType : config.getPrefixTypes()) {
            PrefixTextBuilder prefixTextBuilder = builderMap.get(prefixType);
            if (prefixTextBuilder == null) {
                continue;
            }
            String prefix = prefixTextBuilder.build(activityCxt, param);
            if (StringUtils.isNotBlank(prefix)) {
                return prefix;
            }
        }
        return null;
    }

    /**
     * 接小程序商品价格前缀
     * @return
     */
    private static PrefixTextBuilder getMiniprogramDesc() {
        return (context, param) -> {
            return ApplianceShelfUtils.isMiniprogramLinked(param.getProductM()) ? "焕新价" : null;
        };
    }
    
    /**
     * 使用国补优惠的商品价格前缀
     * @return
     */
    private static PrefixTextBuilder getUsedNationalSubsidyDesc() {
        return (context, param) -> {
            return PriceUtils.isSupportDp4NationalSubsidy(
                    ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform))
                    && PriceUtils.usedNationalSubsidy(param.getProductM()) ? "国补价" : null;
        };
    }

    @FunctionalInterface
    public interface PrefixTextBuilder {
        String build(ActivityCxt context, Param param);
    }

    public enum PrefixType {
        MINIPROGRAM("miniprogram", "接小程序前缀"),
        USED_NATIONAL_SUBSIDY("usedNationalSubsidy", "使用国补前缀")
        ;

        @Getter
        private String code;
        private String desc;

        PrefixType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 前缀类型，按顺序配置优先级，配置值见PrefixType枚举
         */
        private List<String> prefixTypes;
    }
}
