package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;

import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "价格前缀-根据不同条件返回",
        description = "根据不同条件返回价格前缀，如「焕新价」",
        code = ConditionSalePricePrefixOpt.CODE)
public class ConditionSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<ConditionSalePricePrefixOpt.Config> {

    public static final String CODE = "ConditionSalePricePrefixOpt";

    @ConfigValue(
            key = "com.sankuai.dzviewscene.dealshelf.unifiedshelf.condition.price.prefix.default.strategies",
            defaultValue = "[\"miniprogram\",\"timesCard\",\"usedNationalSubsidy\",\"newCustomerPrice\",\"paidMemberPrice\",\"magicMember\"]"
    )
    private static List<String> defaultPricePrefixStrategies;
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.unifiedshelf.condition.price.prefix.default.strategy.config", defaultValue = "{}")
    private static Map<String,StrategyConfig> defaultStrategyConfigMap;

    private static final Map<String, PrefixTextBuilder> builderMap = Maps.newHashMap();

    static {
        builderMap.put(PrefixType.MINIPROGRAM.getCode(), getMiniprogramDesc());
        builderMap.put(PrefixType.USED_NATIONAL_SUBSIDY.getCode(), getUsedNationalSubsidyDesc());
        builderMap.put(PrefixType.NEW_CUSTOMER_PRICE.getCode(), getNewCustomerPriceDesc());
        builderMap.put(PrefixType.PAID_MEMBER_PRICE.getCode(), getPaidMemberPriceDesc());
        builderMap.put(PrefixType.FREE_MEMBER_PRICE.getCode(), getFreeMemberPriceDesc());
        builderMap.put(PrefixType.TIMES_CARD.getCode(), getTimesCardDesc());
        builderMap.put(PrefixType.MAGIC_MEMBER.getCode(), getMagicMemberDesc());
    }

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getPrefixTypes()) && CollectionUtils.isEmpty(defaultPricePrefixStrategies)) {
            return null;
        }
        List<String> prefixTypes = CollectionUtils.isEmpty(config.getPrefixTypes()) ? defaultPricePrefixStrategies : config.getPrefixTypes();
        Map<String, StrategyConfig> strategyConfigMap = MapUtils.isEmpty(config.getStrategyConfigMap()) ? defaultStrategyConfigMap : config.getStrategyConfigMap();
        for (String prefixType : prefixTypes) {
            PrefixTextBuilder prefixTextBuilder = builderMap.get(prefixType);
            if (prefixTextBuilder == null) {
                continue;
            }
            StrategyConfig strategyConfig = MapUtils.isNotEmpty(strategyConfigMap) ? strategyConfigMap.getOrDefault(prefixType, null) : null;;
            String prefix = buildPrefixIfApplicable(activityCxt, param, prefixTextBuilder, strategyConfig);
            if (StringUtils.isNotBlank(prefix)) {
                return prefix;
            }
        }
        return null;
    }
    /**
     * 如果满足条件则构建前缀
     */
    private String buildPrefixIfApplicable(ActivityCxt activityCxt, Param param,
                                          PrefixTextBuilder prefixTextBuilder, StrategyConfig strategyConfig) {
        // 如果没有实验约束或命中配置实验组
        if (isExperimentEligible(activityCxt, strategyConfig)) {
            return prefixTextBuilder.build(activityCxt, param);
        }
        return "";
    }
    /**
     * 判断是否满足实验条件（无实验约束或命中实验组）
     */
    private boolean isExperimentEligible(ActivityCxt activityCxt, StrategyConfig strategyConfig) {
        return strategyConfig == null || CollectionUtils.isEmpty(strategyConfig.getExpIds()) ||
                DouHuUtils.hitAnySk(activityCxt.getParam(ShelfActivityConstants.Params.douHus), strategyConfig.getExpIds());
    }

    /**
     * 接小程序商品价格前缀
     * @return
     */
    private static PrefixTextBuilder getMiniprogramDesc() {
        return (context, param) -> {
            return ApplianceShelfUtils.isMiniprogramLinked(param.getProductM()) ? "焕新价" : null;
        };
    }

    /**
     * 使用国补优惠的商品价格前缀
     * @return
     */
    private static PrefixTextBuilder getUsedNationalSubsidyDesc() {
        return (context, param) -> {
            return PriceUtils.isSupportDp4NationalSubsidy(
                    ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform))
                    && PriceUtils.usedNationalSubsidy(param.getProductM()) ? "国补价" : null;
        };
    }

    /**
     * 使用付费会员优惠的价格前缀
     */
    private static PrefixTextBuilder getPaidMemberPriceDesc() {
        return (context, param) -> {
            CardM cardM = context.getSource(CardFetcher.CODE);
            return MerchantMemberPromoUtils.usedPaidMerchantMemberDiscount(param.getProductM(), cardM) ? "会员价" : null;
        };
    }

    /**
     * 使用免费会员优惠的价格前缀
     */
    private static PrefixTextBuilder getFreeMemberPriceDesc() {
        return (context, param) -> {
            CardM cardM = context.getSource(CardFetcher.CODE);
            return MerchantMemberPromoUtils.usedFreeMerchantMemberDiscount(param.getProductM(), cardM) ? "会员价" : null;
        };
    }

    /**
     * 使用新客优惠的价格前缀
     */
    private static PrefixTextBuilder getNewCustomerPriceDesc() {
        return (context, param) -> {
            return PriceUtils.usedNewCustomerDiscount(param.getProductM()) ? "新客价" : null;
        };
    }

    private static PrefixTextBuilder getTimesCardDesc() {
        return (context, param) -> {
            // 泛商品次卡
            if (param.getProductM().getProductType() == ProductTypeEnum.TIME_CARD.getType()) {
                String timesStr = ProductMAttrUtils.getAttrValue(param.getProductM(), TimesDealUtil.TIMES_CARD_TIMES);
                int times = NumberUtils.toInt(timesStr, 0);
                if (times <= 0) {
                    return null;
                }
                return String.format("共%s次", times);
            }
            // 团购次卡
            if (param.getProductM().isTimesDeal() && !TimesDealUtil.isContinuousMonthly(param.getProductM())) {
                return "单次";
            }
            return null;
        };
    }

    private static PrefixTextBuilder getMagicMemberDesc() {
        return (context, param) -> {
            return getMagicMemberPricePrefix(context, param);
        };
    }

    @FunctionalInterface
    public interface PrefixTextBuilder {
        String build(ActivityCxt context, Param param);
    }

    public enum PrefixType {
        MINIPROGRAM("miniprogram", "接小程序前缀"),
        TIMES_CARD("timesCard", "次卡前缀"),
        MAGIC_MEMBER("magicMember", "神券价前缀"),
        USED_NATIONAL_SUBSIDY("usedNationalSubsidy", "使用国补前缀"),
        PAID_MEMBER_PRICE("paidMemberPrice", "付费会员价前缀"),
        FREE_MEMBER_PRICE("freeMemberPrice", "免费会员价前缀"),
        NEW_CUSTOMER_PRICE("newCustomerPrice", "新客价前缀")
        ;

        @Getter
        private String code;
        private String desc;

        PrefixType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 前缀类型，按顺序配置优先级，配置值见PrefixType枚举
         */
        private List<String> prefixTypes;

        /**
         * 策略对应的配置
         */
        private Map<String, StrategyConfig> strategyConfigMap;
    }

    @Data
    public static class StrategyConfig {
        private List<String> expIds;
    }
}
