package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gm.marketing.times.card.api.enums.TimesCardTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "丽人三美-RichTags形式的副标题构造",
        description = "",
        code = "BeautyShelItemProductRichTagsOpt")
public class BeautyShelItemProductRichTagsOpt extends ItemProductRichTagsVP<BeautyShelItemProductRichTagsOpt.Config> {

    @Override
    public List<RichLabelVO> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        // 丽人标准化团单的特殊处理
        if (config.isCalcBySpu()
                && ProductMAttrUtils.hotSpu(param.getProductM())
                && Objects.nonNull(productM.getSpuM())
                && Objects.nonNull(productM.getSpuAttr(SpuAttrEnum.ATTR_HOT_SPU_SHELF_SUB_TAG.getKey()))
                && StringUtils.isNotBlank((String) productM.getSpuAttr(SpuAttrEnum.ATTR_HOT_SPU_SHELF_SUB_TAG.getKey()).getValue())) {
            List<String> hotSpuTags = JSONObject.parseArray((String) productM.getSpuAttr(SpuAttrEnum.ATTR_HOT_SPU_SHELF_SUB_TAG.getKey()).getValue(), String.class);
            return buildProductRichLabelTags(hotSpuTags);
        }
        // 预售单逻辑处理
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            return getPreSaleProductRichTags(productM, config);
        }
        // 次卡逻辑处理
        if(isTimesCard(productM)){
            List<RichLabelVO> timesCardProductRichTags = getTimesCardProductRichTags(productM, config);
            if (ProductMAttrUtils.timeCardIsShowAfterPayTag(productM,context,config.getAfterPayExps())) {
                RichLabelVO afterPayTag = buildPreAfterPayTag(config);
                timesCardProductRichTags.add(0, afterPayTag);
            }
            return timesCardProductRichTags;
        }
        // 超级团购处理
        if (ProductMAttrUtils.superSpu(productM)) {
            List<RichLabelVO> superDealProductRichTags = getSuperDealProductRichTags(productM);
            if (CollectionUtils.isNotEmpty(superDealProductRichTags)) {
                return superDealProductRichTags;
            }
        }

        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        List<RichLabelVO> productTags = buildProductRichLabelTags(param.getProductM().getProductTags());
        //先用后付款
        if(ProductMAttrUtils.timeCardIsShowAfterPayTag(productM,context,config.getAfterPayExps())){
            RichLabelVO afterPayTag = buildPreAfterPayTag(config);
            richLabelVOS.add(afterPayTag);
        }
        //联合星品
        if (ProductMAttrUtils.isUniteStarProduct(productM)) {
            RichLabelVO uniteStarTag = buildPreUniteStarTag(config);
            richLabelVOS.add(uniteStarTag);
        }
        RichLabelVO repurchaseTag = buildRepurchaseTag(param.getProductM(), config);
        if(repurchaseTag != null){
            richLabelVOS.add(repurchaseTag);
        } else {
            //复购人数标签>[可重复买]标签
            RichLabelVO repurchaseTitleTag = buildRepurchaseTitleTag(param.getProductM(), config);
            if(repurchaseTitleTag != null){
                richLabelVOS.add(repurchaseTitleTag);
            }
        }
        RichLabelVO meiJiaKuanShiRenXuanTag = buildMeiJiaKuanShiRenXuanTag(param.getProductM(),config);
        if (meiJiaKuanShiRenXuanTag != null) {
            richLabelVOS.add(meiJiaKuanShiRenXuanTag);
        }
        richLabelVOS.addAll(productTags);
        return richLabelVOS;
    }

    public List<RichLabelVO> getSuperDealProductRichTags(ProductM productM) {
        if (Objects.isNull(productM) || Objects.isNull(productM.getSpuM()) || CollectionUtils.isEmpty(productM.getSpuM().getSpuKeyInformation())) {
            return Lists.newArrayList();
        }
        List<String> productTags = productM.getSpuM().getSpuKeyInformation();

        return productTags.stream().filter(StringUtils::isNotEmpty)
                .map(tag -> new RichLabelVO(FrontSizeUtils.front12, ColorUtils.color777777, tag))
                .collect(Collectors.toList());
    }

    private RichLabelVO buildRepurchaseTitleTag(ProductM productM, Config config) {
        String repurchaseTitleTag = ProductMAttrUtils.getAttrValue(productM, ProductMAttrUtils.REPURCHASE_TITLE_TAG);
        if(StringUtils.isEmpty(repurchaseTitleTag) || !config.isShowRepurchaseTitleTag()){
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, repurchaseTitleTag, "#FF4B10",  "#FFF1EC");
        richLabelVO.setCssPadding("0,3,0,3");
        richLabelVO.setCssBorderRadius("3,3,3,3");
        return richLabelVO;
    }

    private boolean isTimesCard(ProductM productM){
        return productM.getProductType() == ProductTypeEnum.TIME_CARD.getType();
    }

    private List<RichLabelVO> getTimesCardProductRichTags(ProductM productM, Config config){
        List<RichLabelVO> productTags = buildProductRichLabelTags(productM.getProductTags());
        if(CollectionUtils.isEmpty(productTags)){
            return Lists.newArrayList();
        }
        int timesCardType = NumberUtils.toInt(ProductMAttrUtils.getAttrValue(productM, "times_card_type"));
        if(timesCardType == TimesCardTypeEnum.PLAIN_TIMESCARD.getCode()
            || timesCardType == TimesCardTypeEnum.FLEXIBLE_TIMESCARD.getCode()) {
            String countTemplate = timesCardType == TimesCardTypeEnum.PLAIN_TIMESCARD.getCode() ? "%d项" : "%d项任选";
            String countTag = String.format(countTemplate, productTags.size());
            RichLabelVO countRichLabel = new RichLabelVO(FrontSizeUtils.front12, ColorUtils.color777777, countTag);
            //限制前3项
            productTags = productTags.subList(0, Math.min(productTags.size(), 3));
            //加上统计项标签
            productTags.add(0, countRichLabel);
        }
        return productTags;
    }

    /**
     * 处理预售单
     *
     * @param productM
     * @param config
     * @return
     */
    private List<RichLabelVO> getPreSaleProductRichTags(ProductM productM, Config config) {
        String preSaleDate = productM.getAttr(config.getPreSaleDateAttrKey());
        List<String> productTags = Lists.newArrayList();
        if (StringUtils.isBlank(config.getPreSaleTemplate()) || StringUtils.isBlank(preSaleDate)) {
            productTags = productM.getProductTags();
        } else {
            productTags = Lists.newArrayList(String.format(config.getPreSaleTemplate(), preSaleDate));
        }

        return productTags.stream().filter(StringUtils::isNotEmpty)
                .map(tag -> new RichLabelVO(FrontSizeUtils.front12, ColorUtils.color777777, tag))
                .collect(Collectors.toList());
    }

    private RichLabelVO buildMeiJiaKuanShiRenXuanTag(ProductM productM, Config config) {
        //美甲团单，包含款式任选， https://km.sankuai.com/collabpage/1681432462
        String tagIdStr = ProductMAttrUtils.getProductTagIdStr(productM);
        if(CollectionUtils.isNotEmpty(config.getStyleTag()) && StringUtils.isNotEmpty(tagIdStr)){
            for(String styleTag : config.getStyleTag()){
               if(tagIdStr.contains(styleTag) && MapUtils.isNotEmpty(config.getStyleTagText())
                       && config.getStyleTagText().containsKey(styleTag)){
                   return new RichLabelVO(FrontSizeUtils.front12, ColorUtils.colorFF6633, config.getStyleTagText().get(styleTag));
               }
            }
        }
        return null;
    }

    private RichLabelVO buildRepurchaseTag(ProductM productM, Config config) {
        String repurchaseTag = ProductMAttrUtils.getAttrValue(productM, ProductMAttrUtils.REPURCHASE_TAG);
        if(StringUtils.isEmpty(repurchaseTag) || !config.isShowRepurchaseTag()){
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, repurchaseTag, "#FF4B10",  "#FFF1EC");
        richLabelVO.setCssPadding("0,3,0,3");
        richLabelVO.setCssBorderRadius("3,3,3,3");
        return richLabelVO;
    }

    private List<RichLabelVO> buildProductRichLabelTags(List<String> productTags) {
        if (CollectionUtils.isEmpty(productTags)) {
            return Lists.newArrayList();
        }
        return productTags.stream().filter(StringUtils::isNotEmpty)
                .map(tag -> new RichLabelVO(FrontSizeUtils.front12, ColorUtils.color777777, tag))
                .collect(Collectors.toList());
    }

    public RichLabelVO buildPreUniteStarTag(Config config) {
        RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, config.getPreUniteStarTag(), "#FF4B10",  "#FFF1EC");
        richLabelVO.setCssPadding("0,3,0,3");
        richLabelVO.setCssBorderRadius("3,3,3,3");
        return richLabelVO;
    }

    public RichLabelVO buildPreAfterPayTag(Config config) {
        RichLabelVO richLabelVO = new RichLabelVO(config.getFrontSize(), config.getPreAfterTag(), config.getTextColor(),  config.getBackgroundColor());
        richLabelVO.setCssPadding("0,3,0,3");
        richLabelVO.setCssBorderRadius("3,3,3,3");
        return richLabelVO;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 是否基于 SPU 进行转化
         */
        private boolean calcBySpu;
        /**
         * 丽人标准团单识别 key
         */
        private String expByProductAttr;

        private Map<String, List<String>> standardKeySubTitle;

        /**
         * 预售时间Key
         */
        private String preSaleDateAttrKey = "preSaleStartDate";

        private String preSaleTemplate = "%s后可用";

        private List<String> styleTag;

        private Map<String, String> styleTagText;

        /**
         * 是否展示复购标签
         */
        private boolean showRepurchaseTag;

        /**
         * 是否展示可重复买标签
         */
        private boolean showRepurchaseTitleTag = true;

        private String preUniteStarTag = "品牌官方保障 美团联合出品";

        private String preAfterTag = "先用后付";

        private int frontSize = 10;

        private String textColor = "#FF4B10";

        private String backgroundColor = "#FFFFFF";

        private List<String> afterPayExps;
    }
}
