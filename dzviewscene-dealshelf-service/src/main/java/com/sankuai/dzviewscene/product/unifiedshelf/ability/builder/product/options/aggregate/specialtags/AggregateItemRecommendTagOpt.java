package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.specialtags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.ProductHierarchyUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregateSpecialTagVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

@VPointOption(name = "聚合卡片特色标签",
        description = "聚合卡片特色标签",
        code = AggregateItemRecommendTagOpt.CODE,
        isDefault = true)
public class AggregateItemRecommendTagOpt extends AggregateSpecialTagVP<AggregateItemRecommendTagOpt.Config> {

    public static final String CODE = "AggregateItemRecommendTagOpt";
    private static final String BOOKING_TIME_KEY = "available_time";

    @Override
    public ItemSpecialTagVO compute(ActivityCxt activityCxt, Param param, Config config) {
        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        if (DouHuUtils.hitAnySk(douHuMList, config.getMassageTagExpSks())) {
            return null;
        }
        ProductM productM = param.getNodeM().getProductM();
        if(ProductHierarchyUtils.isSpuNode(param.getNodeM())){
            productM = ProductHierarchyUtils.getMaxSaleProductM(param.getNodeM());
        }
        List<ShelfTagVO> recommendTags = getRecommendTags(productM);
        if (CollectionUtils.isEmpty(recommendTags)) {
            return null;
        }
        ItemSpecialTagVO itemSpecialTagVO = new ItemSpecialTagVO();
        itemSpecialTagVO.setTags(recommendTags);
        return itemSpecialTagVO;
    }

    private List<ShelfTagVO> getRecommendTags(ProductM productM) {
        if (productM == null) {
            return Lists.newArrayList();
        }
        // UGC标签
        List<String> recommendTags = PriceAboveTagsUtils.getRecommendTag(productM);
        if (CollectionUtils.isNotEmpty(recommendTags)) {
            return Lists.newArrayList(buildShelfTagVO("“" + recommendTags.get(0) + "”", ColorUtils.colorF4F4F4, ColorUtils.color666666));
        }
        return Lists.newArrayList();
    }

    private ShelfTagVO buildShelfTagVO(String recommendTag, String background, String textColor) {
        RichLabelModel richLabelText = new RichLabelModel();
        richLabelText.setText(recommendTag);
        richLabelText.setMultiText(Lists.newArrayList(recommendTag));
        richLabelText.setTextColor(textColor);
        richLabelText.setBackgroundColor(background);
        richLabelText.setStyle(RichLabelStyleEnum.BUBBLE.getType());
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(richLabelText);
        return shelfTagVO;
    }

    @VPointCfg
    @Data
    public static class Config {
        private List<String> massageTagExpSks;
    }
}
