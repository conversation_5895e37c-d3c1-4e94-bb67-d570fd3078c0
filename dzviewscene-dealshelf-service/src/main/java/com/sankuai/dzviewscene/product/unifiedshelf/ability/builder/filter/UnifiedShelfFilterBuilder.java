package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterNodeVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterVO;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpactivity.ProductActivitiesFetcher;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfExtraVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterShowTypeVP;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterBtnLabsVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.FilterNodeShowTypeEnum;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Ability(code = UnifiedShelfFilterBuilder.CODE,
        name = "VO-筛选栏构造能力",
        description = "筛选栏构造能力（前置）。构造 Map<String, ShelfFilterVO>",
        activities = {UnifiedShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE}
)
public class UnifiedShelfFilterBuilder extends PmfAbility<Map<String, ShelfFilterVO>, UnifiedShelfFilterBuilder.Request, UnifiedShelfFilterBuilder.Config> {

    public static final String CODE = "UnifiedShelfFilterBuilder";
    private static final String ALL_FILTER = "全部";

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.filter.secondtab.config", defaultValue = "{}")
    public static LionConfigHelper.FilterSecondTabConfig filterSecondTabConfig;

    @Override
    public CompletableFuture<Map<String, ShelfFilterVO>> build(ActivityCxt ctx, Request request, Config config) {
        Map<String, ShelfFilterVO> engineRes = getEngineRes(ctx, request, config);
        return CompletableFuture.completedFuture(engineRes);
    }

    private Map<String, ShelfFilterVO> getEngineRes(ActivityCxt ctx, Request request, Config config) {
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null) {
            return Maps.newHashMap();
        }
        if (ModelUtils.hasNoProducts(shelfGroupM) || MapUtils.isEmpty(shelfGroupM.getFilterMs())) {
            return Maps.newHashMap();
        }
        // 多层货架则生成多个 ShelfFilterVO
        Map<String, ShelfFilterVO> map = Maps.newHashMap();
        for (Map.Entry<String, FilterM> entry : shelfGroupM.getFilterMs().entrySet()) {
            map.put(entry.getKey(), buildFilterVO(ctx, request, config, entry.getValue()));
        }
        return map;
    }

    private ShelfFilterVO buildFilterVO(ActivityCxt ctx, Request request, Config config, FilterM filterM) {
        ShelfFilterVO shelfFilterVO = new ShelfFilterVO();
        ShelfFilterNodeVO filterNodeVO = new ShelfFilterNodeVO();
        // 设置minShowNum
        filterNodeVO.setMinShowNum(config.getMinShowNum());
        // 设置筛选
        filterNodeVO.setChildren(buildFilterButtons(ctx, request, config, filterM));
        shelfFilterVO.setFilterRoot(filterNodeVO);
        return shelfFilterVO;
    }

    private List<ShelfFilterNodeVO> buildFilterButtons(ActivityCxt activityCxt, Request request, Config config, FilterM filterM) {
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return Lists.newArrayList();
        }
        int layer = 1;
        AtomicInteger index = new AtomicInteger(1);
        List<FilterBtnM> allFilter = Lists.newArrayList(filterM.getFilters());
        return filterM.getFilters()
                .stream()
                .map(filterButtonM -> builderFilterButtonVO(activityCxt, request, config, filterButtonM, layer, index.getAndIncrement(), allFilter))
                .collect(Collectors.toList());
    }

    private ShelfFilterNodeVO builderFilterButtonVO(ActivityCxt activityCxt, Request request, Config config, FilterBtnM filterBtnM, int layer, int index, List<FilterBtnM> allFilter) {
        ShelfFilterNodeVO filterButtonVO = new ShelfFilterNodeVO();
        try {
            String filterId = String.valueOf(filterBtnM.getFilterId());
            List<ProductActivityM> activities = activityCxt.getSource(ProductActivitiesFetcher.CODE);
            // 1. 标准字段, 直接设置
            filterButtonVO.setFilterId(filterId);
            filterButtonVO.setSelected(filterBtnM.isSelected());

            // 2. 非标字段, 通过扩展点设置
            filterButtonVO.setShowType(buildShowType(activityCxt, filterId, false));
            filterButtonVO.setMinShowNum(config.getChildrenMinShowNum());
            allChildrenMinShowNum(activityCxt, config, filterButtonVO);
            filterButtonVO.setMultiSelect(config.isChildrenMultiSelect());
            filterButtonVO.setTitle(title(activityCxt, filterBtnM, layer, index, activities, allFilter));
            // 需要放在 title 后面
            filterButtonVO.setEchoSelectedLeafNode(echoSelectedLeafNode(filterButtonVO, layer));
            filterButtonVO.setExtra(extra(activityCxt, filterBtnM));
            filterButtonVO.setLabs(labs(activityCxt, request, filterBtnM, index));
            filterButtonVO.setChildren(buildChildren(activityCxt, request, config, filterBtnM.getChildren(), layer + 1, allFilter));
            // 3. 兼容
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return filterButtonVO;
    }

    public static void allChildrenMinShowNum(ActivityCxt activityCxt, Config config, ShelfFilterNodeVO filterButtonVO) {
        if (Objects.nonNull(filterSecondTabConfig) && filterSecondTabConfig.isFilterSwitch() && config.getChildrenMinShowNum() <= 0) {
            boolean isInBlackSceneCode = CollectionUtils.isNotEmpty(filterSecondTabConfig.getBlackSceneCodeList())
                    && filterSecondTabConfig.getBlackSceneCodeList().contains(activityCxt.getSceneCode());
            if (!isInBlackSceneCode) {
                filterButtonVO.setMinShowNum(filterSecondTabConfig.getChildrenMinShowNum());
            }
        }
    }

    /**
     * 非全部Tab需要增加回显能力
     */
    private boolean echoSelectedLeafNode(ShelfFilterNodeVO filterButtonVO, int layer) {
        // 第一层的TAB不需要向上回显
        if (layer == 1) {
            return false;
        }
        if (filterButtonVO.getTitle() != null && filterButtonVO.getTitle().getText() != null) {
            RichLabelModel title = filterButtonVO.getTitle().getText();
            if (ALL_FILTER.equals(title.getText())) {
                return false;
            }
            if (CollectionUtils.isNotEmpty(title.getMultiText()) && title.getMultiText().contains(ALL_FILTER)) {
                return false;
            }
        }
        return true;
    }

    private List<ShelfFilterNodeVO> buildChildren(ActivityCxt activityCxt, Request request, Config config, List<FilterBtnM> children, int layer, List<FilterBtnM> allFilter) {
        if (CollectionUtils.isEmpty(children)) {
            return Lists.newArrayList();
        }
        AtomicInteger index = new AtomicInteger(1);
        return children.stream()
                .map(filterButtonM -> builderFilterButtonVO(activityCxt, request, config, filterButtonM, layer, index.getAndIncrement(), allFilter))
                .collect(Collectors.toList());
    }

    private IconRichLabelModel title(ActivityCxt activityCxt, FilterBtnM filterBtnM, int layer, int index, List<ProductActivityM> activities, List<FilterBtnM> allFilter) {
        try {
            UnifiedShelfFilterTitleVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfFilterTitleVP.CODE);
            return vPoint.execute(activityCxt, UnifiedShelfFilterTitleVP.Param.builder()
                    .filterBtnM(filterBtnM)
                    .layer(layer)
                    .index(index)
                    .activities(activities)
                    .allFilter(allFilter)
                    .build()
            );
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 获取筛选项展示类型
     */
    private int buildShowType(ActivityCxt activityCxt, String identityName, boolean isOptionNode) {
        try {
            UnifiedShelfFilterShowTypeVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfFilterShowTypeVP.CODE);
            Integer showType = vPoint.execute(activityCxt, UnifiedShelfFilterShowTypeVP.Param.builder()
                    .identityName(identityName)
                    .isOptionNode(isOptionNode)
                    .build()
            );
            if(showType != null && FilterNodeShowTypeEnum.from(showType) != null){
                return showType;
            }
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return isOptionNode ? FilterNodeShowTypeEnum.FILTER_GRID_LAYER.getType() : FilterNodeShowTypeEnum.FILTER_ADAPTIVE_GRID_LAYER.getType();
    }

    private String extra(ActivityCxt activityCxt, FilterBtnM filterBtnM) {
        try {
            UnifiedShelfExtraVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfExtraVP.CODE);
            return vPoint.execute(activityCxt, UnifiedShelfExtraVP.Param.builder().filterBtnM(filterBtnM)
                    .build()
            );
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private String labs(ActivityCxt activityCxt, Request request, FilterBtnM filterBtnM, int index) {
        try {
            UnifiedShelfFilterBtnLabsVP<?> vPoint = findVPoint(activityCxt, UnifiedShelfFilterBtnLabsVP.CODE);
            return vPoint.execute(activityCxt, UnifiedShelfFilterBtnLabsVP.Param.builder()
                    .btn(filterBtnM).dpPoiId(PoiIdUtil.getDpPoiIdL(request)).mtPoiId(PoiIdUtil.getMtPoiIdL(request))
                    .platform(request.getPlatform()).shop(request.getCtxShop()).index(index)
                    .build()
            );
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 最少展示个数
         */
        private int minShowNum;
        /**
         * 子标签最少展示个数
         */
        private int childrenMinShowNum;
        /**
         * 子项是否可多选
         */
        private boolean childrenMultiSelect;
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private int dpPoiId;
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private int mtPoiId;
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Ctx#ctxShop}
         */
        private ShopM ctxShop;
    }
}