package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAreaDefaultShowNumVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "商品区-默认展示数量", description = "商品区-默认展示数量", code = ProductAreaDefaultShowNumVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class ProductAreaDefaultShowNumVP<T> extends PmfVPoint<Integer, ProductAreaDefaultShowNumVP.Param, T> {

    public static final String CODE = "ProductAreaDefaultShowNumVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> products;
        private List<DouHuM> douHuList;
        private long filterId;
        private String groupName;
    }
}
