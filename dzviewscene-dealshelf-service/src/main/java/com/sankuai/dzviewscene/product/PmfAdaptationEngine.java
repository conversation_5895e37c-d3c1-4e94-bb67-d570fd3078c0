package com.sankuai.dzviewscene.product;

import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;

/**
 * Created by float.lu on 2021/10/18.
 */
public interface PmfAdaptationEngine {

    boolean shouldDiff(String sceneCode);

    void diffWithPmfEngine(ActivityRequest activityRequest, ActivityResponse response);

    boolean usePmfEngine(ActivityContext activityContext);

    <T> ActivityResponse<T> executeWithPmfEngine(ActivityContext activityContext);
}
