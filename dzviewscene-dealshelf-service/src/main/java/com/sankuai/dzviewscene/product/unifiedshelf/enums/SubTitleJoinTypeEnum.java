package com.sankuai.dzviewscene.product.unifiedshelf.enums;

public enum SubTitleJoinTypeEnum {

    /**
     * 圆点
     */
    DOT(0, "圆点"),

    /**
     * 灰色竖线
     */
    GRAY_VERTICAL_LINE(1, "灰色竖线"),

    /**
     * 灰红色竖线
     */
    GRAY_RED_VERTICAL_LINE(2, "灰红色竖线");

    private final int type;

    private final String desc;

    SubTitleJoinTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
