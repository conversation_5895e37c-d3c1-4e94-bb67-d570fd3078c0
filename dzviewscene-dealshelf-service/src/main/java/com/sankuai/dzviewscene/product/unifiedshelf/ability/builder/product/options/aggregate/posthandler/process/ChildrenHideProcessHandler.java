package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 多子项隐藏信息
 */
@Component
public class ChildrenHideProcessHandler implements AggregatePostHandler{

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.massageTag.newStyle.expSks", defaultValue = "[]")
    private List<String> massageTagNewStyleExpSks;

    @Override
    public String getName() {
        return ChildrenHideProcessHandler.class.getSimpleName();
    }

    @Override
    public ShelfItemVO process(ActivityCxt activityCxt, AggregateHandlerContext context) {
        ShelfItemVO shelfItemVO = context.getShelfItemVO();
        if(CollectionUtils.isEmpty(shelfItemVO.getSubItems())){
            return shelfItemVO;
        }
        //非多子项
        List<DouHuM> douHuMList = activityCxt.getParam(ShelfActivityConstants.Params.douHus);
        boolean needTag = CollectionUtils.isNotEmpty(massageTagNewStyleExpSks) && DouHuUtils.hitAnySk(douHuMList, massageTagNewStyleExpSks);
        if(shelfItemVO.getSubItems().size() == 1){
            if(needTag){
                return shelfItemVO;
            }
            shelfItemVO.getSubItems().forEach(t -> t.setProductTags(null));
            return shelfItemVO;
        }
        shelfItemVO.getSubItems().forEach(t->hideInfo(activityCxt,t,needTag));
        return shelfItemVO;
    }

    private void hideInfo(ActivityCxt activityCxt,ShelfItemVO shelfItemVO,boolean needTag){
        shelfItemVO.setHeadPic(null);
        if(!needTag && !ActivityCtxtUtils.checkMultiState(activityCxt)){
            shelfItemVO.setProductTags(null);
        }
    }
}
