package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 多子项隐藏信息
 */
@Component
public class ChildrenHideProcessHandler implements AggregatePostHandler{

    @Override
    public String getName() {
        return ChildrenHideProcessHandler.class.getSimpleName();
    }

    @Override
    public ShelfItemVO process(ActivityCxt activityCxt, AggregateHandlerContext context) {
        ShelfItemVO shelfItemVO = context.getShelfItemVO();
        if(CollectionUtils.isEmpty(shelfItemVO.getSubItems())){
            return shelfItemVO;
        }
        //非多子项
        if(shelfItemVO.getSubItems().size() == 1){
            shelfItemVO.getSubItems().forEach(t -> t.setProductTags(null));
            return shelfItemVO;
        }
        shelfItemVO.getSubItems().forEach(this::hideInfo);
        return shelfItemVO;
    }

    private void hideInfo(ShelfItemVO shelfItemVO){
        shelfItemVO.setHeadPic(null);
        shelfItemVO.setProductTags(null);
    }
}
