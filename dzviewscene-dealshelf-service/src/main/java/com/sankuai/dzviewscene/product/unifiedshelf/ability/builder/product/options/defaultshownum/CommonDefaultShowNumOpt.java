package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.defaultshownum;

import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaDefaultShowNumVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "商品区-默认展示数量",
        description = "支持配置控制展示默认数量的逻辑",
        code = CommonDefaultShowNumOpt.CODE,
        isDefault = true)
public class CommonDefaultShowNumOpt extends ProductAreaDefaultShowNumVP<CommonDefaultShowNumOpt.Config> {

    public static final String CODE = "CommonDefaultShowNumOpt";

    private static final String CONDITION_SPLIT = ";";

    private static final String KV_SPLIT = ":";

    private static final Map<String, CalculateRule> calRuleMap = Maps.newHashMap();

    static {
        calRuleMap.put(RuleType.PRODUCT_TYPE_RULE.getCode(), productTypeRule());
        calRuleMap.put(RuleType.DOU_HU_EXP_RULE.getCode(), douHuExpRule());
        calRuleMap.put(RuleType.FILTER_ID_RULE.getCode(), filterIdRule());
        calRuleMap.put(RuleType.TIMES_DEAL_RULE.getCode(), timesDealRule());
        calRuleMap.put(RuleType.PRESS_ON_NAIL_RULE.getCode(), pressOnNailRule());
        calRuleMap.put(RuleType.UNAVAILABLE_PRODUCT_RULE.getCode(), unavailableProductRule());
        calRuleMap.put(RuleType.PAGE_RULE.getCode(), pageRule());
        calRuleMap.put(RuleType.F_SHELF.getCode(), fShelfRule());
    }

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        if (MapUtils.isEmpty(config.getRuleShowNumCfg())) {
            return config.getDefaultShowNum();
        }
        for (Map.Entry<String, String> cfg : config.getRuleShowNumCfg().entrySet()) {
            CalculateRule calculateRule = calRuleMap.get(cfg.getKey());
            if (calculateRule == null) {
                continue;
            }
            int ruleShowNum = calculateRule.cal(context, param, cfg.getValue());
            if (ruleShowNum > 0) {
                return ruleShowNum;
            }
        }
        return config.getDefaultShowNum();
    }

    private static CalculateRule productTypeRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            Map<String, Integer> conditionShowNum = convertToMap(rawShowNum);
            for (ProductM productM : param.getProducts()) {
                Integer showNum = conditionShowNum.get(String.valueOf(productM.getProductType()));
                if (showNum != null && showNum > 0) {
                    return showNum;
                }
            }
            return 0;
        };
    }

    private static CalculateRule douHuExpRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            Map<String, Integer> conditionShowNum = convertToMap(rawShowNum);
            for (Map.Entry<String, Integer> entry : conditionShowNum.entrySet()) {
                if (DouHuUtils.hitAnySk(param.getDouHuList(), entry.getKey())
                        && entry.getValue() != null
                        && entry.getValue() > 0) {
                    return entry.getValue();
                }
            }
            return 0;
        };
    }

    private static CalculateRule filterIdRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            Map<String, Integer> conditionShowNum = convertToMap(rawShowNum);
            Integer showNum = conditionShowNum.get(String.valueOf(param.getFilterId()));
            return showNum != null ? showNum : 0;
        };
    }

    private static CalculateRule timesDealRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            boolean isTimesDeal = param.getProducts().stream().filter(Objects::nonNull).anyMatch(ProductM::isTimesDeal);
            return isTimesDeal ? NumberUtils.toInt(rawShowNum) : 0;
        };
    }

    private static CalculateRule pressOnNailRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            return PressOnNailUtils.checkExclusive(ctx) ? NumberUtils.toInt(rawShowNum) : 0;
        };
    }

    private static CalculateRule unavailableProductRule() {
        return (ctx, param, rawShowNum) -> {
            if (StringUtils.isEmpty(rawShowNum)) {
                return 0;
            }
            int unavailableProductSum = (int) param.getProducts().stream().filter(Objects::nonNull)
                    .filter(ProductMAttrUtils::isBeautyCantRepeatPurchaseProduct).count();
            if (unavailableProductSum == 0){
                return 0;
            }
            if (unavailableProductSum == param.getProducts().size()) {
                return 1;
            }
            return Math.min(param.getProducts().size() - unavailableProductSum, NumberUtils.toInt(rawShowNum));
        };
    }

    private static CalculateRule pageRule() {
        return (ctx, param, rawShowNum) -> {
            int configNum = StringUtils.isNotEmpty(rawShowNum) ? NumberUtils.toInt(rawShowNum) : 0;
            if(ParamsUtil.judgeDealShelfHasPage(ctx)) {
                return configNum > 0 ? configNum : ParamsUtil.getIntSafely(ctx, PmfConstants.Params.pageSize);
            }
            return  configNum;
        };
    }

    private static CalculateRule fShelfRule() {
        return (ctx, param, rawShowNum) -> {
            int configNum = StringUtils.isNotEmpty(rawShowNum) ? NumberUtils.toInt(rawShowNum) : 0;
            if (ParamsUtil.getBooleanSafely(ctx.getParameters(), ShelfActivityConstants.Params.isFShelf)) {
                return configNum;
            }
            return 0;
        };
    }

    public static Map<String, Integer> convertToMap(String rawNum) {
        return Arrays.stream(rawNum.split(CONDITION_SPLIT))
                .map(s -> s.split(KV_SPLIT))
                .filter(array -> array.length == 2)
                .collect(Collectors.toMap(
                        array -> array[0],
                        array -> NumberUtils.toInt(array[1])
                ));
    }

    @FunctionalInterface
    public interface CalculateRule {
        int cal(ActivityCxt context, Param param, String rawShowNum);
    }

    public enum RuleType {
        PRODUCT_TYPE_RULE("productTypeRule", "multi", "根据商品类型"),
        DOU_HU_EXP_RULE("douHuExpRule", "multi", "根据斗斛实验结果"),
        FILTER_ID_RULE("filterIdRule", "multi", "根据筛选id"),
        TIMES_DEAL_RULE("timesDealRule", "plain", "含次卡交易类型规则"),
        PRESS_ON_NAIL_RULE("pressOnNailRule", "plain", "穿戴甲规则"),
        UNAVAILABLE_PRODUCT_RULE("unavailableProductRule", "plain", "不可购商品规则"),
        PAGE_RULE("pageRule", "plain", "分页展示规则"),
        F_SHELF("fShelf", "plain", "F型货架展示规则")
        ;

        @Getter
        private String code;
        private String type;
        private String desc;

        RuleType(String code, String type, String desc) {
            this.code = code;
            this.type = type;
            this.desc = desc;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 默认展示商品数，无规则或规则未命中时默认该数量
         */
        private int defaultShowNum = 3;

        /**
         * 配置规则对应默认展示数，多个规则可按优先级顺序配置
         * key为规则名，即RuleType枚举的code。
         * value为默认展示数量，朴素条件类(plain)规则配置数字即可，多条件类(multi)规则按条件:数字kv形式配置，分号分隔
         * 示例：{"douHuExpRule":"exp002066_a:4;exp002066_b:5","pressOnNailRule":"3"}
         *
         * @see RuleType
         */
        private LinkedHashMap<String, String> ruleShowNumCfg;
    }
}