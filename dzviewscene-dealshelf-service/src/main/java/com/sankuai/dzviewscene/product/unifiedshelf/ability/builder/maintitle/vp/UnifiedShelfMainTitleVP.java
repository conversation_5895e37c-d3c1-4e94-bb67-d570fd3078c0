package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfMainTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.UnifiedShelfMainTitleBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * VP 的优先级高于 UnifiedShelfMainTitleBuilder 配置
 */
@VPoint(name = "主标题变化点", description = "主标题变化点", code = UnifiedShelfMainTitleVP.CODE, ability = UnifiedShelfMainTitleBuilder.CODE)
public abstract class UnifiedShelfMainTitleVP<T> extends PmfVPoint<ShelfMainTitleVO, UnifiedShelfMainTitleVP.Param, T> {

    public static final String CODE = "UnifiedShelfMainTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private Map<String, ProductGroupM> productGroupMs;
        private Map<String, FilterM> filterMs;
        private int platform;
    }
}
