package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2019-08-08
 */
public interface ConstantUtils {
    //商户最大在线团单个数
    int SHOP_DEAL_LIMIT = 500;

    int SCENE = 200000;

    /**
     * 价格治理后统一用的 scene
     */
    int STANDARD_SCENE = 400000;

    /**
     * 足疗货架场景：卡融合取最优
     */
    int MASSAGE_SCENE = 200103;

    //批量获取团单的个数
    int BATCH_SHOP_DEAL_LIMIT = 50;

    //批量获取团单活动的个数
    int BATCH_SHOP_DEAL_ACTIVITY_LIMIT = 20;

    //批量获取团单后台分类
    int BATCH_CATEGORY_IDS_LIMIT = 200;

    //批量获取美团点评映射关系
    int BATCH_MT_DP_DG_IDS_LIMIT = 100;

    //tab下商品展示的最少数量
    int PRODUCT_IDS_LIMIT_MIN = 2;

    //tab展示最小数量，包括全部tab
    int TAB_DISPLAY_LIMIT_MIN = 3;

    //默认选中的tabID
    int DEFALT_SELECTED_TAG_ID = 0;

    // 团单属性key：cash_type，属性值域：充值套餐，单次代金券
    String CASH_TYPE = "cash_type";

    // 充值套餐
    String CASH_TYPE_RECHARGEABLE_SUIT = "充值套餐";

    // 全部名称
    String ALL_TAB_NAME = "全部";

    int ACTIVITY_NAV_ID = 100;

    int PERFECT_ACTIVITY_NAV_ID = 100;

    // 足疗-Tab-其他
    String TAB_NAME_OTHER_ZU_LIAO = "其他";

    // 足疗-Tab-其他
    int TAB_NAV_ID_OTHER_ZU_LIAO = 999;

    String DEFAULT_PLAN_ID = "10000007";

    int PIC_WIDTH = 300;
    int PIC_HEIGHT = 300;

    double ASPECT_RADIO = 1.0;

    double PIC_TAG_ASPECT_RADIO = 2.77778;

    double EDU_PIC_TAG_ASPECT_RADIO = 2.22222;

    int ONE_SCALE = 1;

    String PRICE_ZERO = "0";

    /**
     * 核心部位
     */
    String PEDICURE_CORE_REGIONS = "pedicure_core_regions";

    String PRE_SALE_TAG = "preSaleTag";

    int PRODUCT_TAG_SIZE = 2;

    /**
     * 更多点击BID
     */
    String MORE_BID_CLICK = "b_dianping_nova_dnxvy7nq_mc";

    /**
     * 更多曝光BID
     */
    String MORE_BID_VIEW = "b_dianping_nova_dnxvy7nq_mv";

    /**
     * 整体曝光BID
     */
    String WHOLE_BID_VIEW = "b_dianping_nova_9m04u0qz_mv";

    /**
     * 玩乐卡icon
     */
    String PLAY_CARD_ICON = "https://p1.meituan.net/dprainbow/05dd5b2bd33f763e19ff75456ecd59d79073.png";

    /**
     * 玩乐卡icon宽高比
     */
    double PLAY_CARD_ICON_ASPECT_RADIO = 3.03125;

    /**
     * 单个item点击BID
     */
    String ITEM_BID_CLICK = "b_dianping_nova_lv186vim_mc";

    /**
     * 单个item曝光BID
     */
    String ITEM_BID_VIEW = "b_dianping_nova_lv186vim_mv";

    /**
     * 美团category
     */
    String MT_CATEGORY = "dianping_nova";

    /**
     * 筛选tab点击BID
     */
    String FILTER_BID_CLICK = "b_dianping_nova_gq1dsrcp_mc";

    /**
     * 筛选tab曝光BID
     */
    String FILTER_BID_VIEW = "b_dianping_nova_gq1dsrcp_mv";

    /**
     * 点评category
     */
    String DP_CATEGORY = "dianping_nova";

    /**
     * 商户推荐Icon背景图
     */
    String SHOP_RECOMMEND_BACKGROUND = "https://p1.meituan.net/dprainbow/6b8ad53cc2d030a93b85d0c1d34bc8bd8626.png";

    String OTHER_BACKGROUND = "https://p0.meituan.net/dprainbow/6c37829e30a253efe93ffe274a5e31e35944.png";

    /**
     * 灰色背景
     */
    String GRAY_BACKGROUND = "https://p0.meituan.net/travelcube/d3acf6d11a1e43c6c1fad24e2cc6167e576.png";

    double RECOMMEND_BACKGROUND_ICON_ASPECT_RADIO = 2.77778;

    String MEMBER_CARD_ICON = "https://p0.meituan.net/dprainbow/eab8b32cc671b149b61da475920ef5c53859.png";

    double MEMBER_CARD_ICON_ASPECT_RADIO = 2.375;

    String MEMBER_DAY_ICON = "https://p0.meituan.net/dprainbow/6337987ad4a33009e3d5750d60033aca7991.png";

    double MEMBER_DAY_ICON_ASPECT_RADIO = 3;

    List<Integer> CATEGORY_IDS = Lists.newArrayList(141);

    /**
     * 教培Filter返回结果的课程下挂key
     */
    String EDU_COURSE_PRODUCT_COM_KEY = "课程";

    /**
     * 教培Filter返回结果的团单下挂key
     */
    String EDU_DEAL_PRODUCT_COM_KEY = "团购";

    /**
     * 选课中心图标
     */
    String EDU_CENTER_ICON = "https://p0.meituan.net/dprainbow/a3633b3dcf7ab2136d3f44beb549d8cf1273.png";

    String DP_BOOKING_TITLE_ICON = "https://p0.meituan.net/scarlett/4189aa0bf7c45d73ef3656f8dc2893f81319.png";
    String MT_BOOKING_TITLE_ICON = "https://p0.meituan.net/scarlett/1ad578d37e9f4f2a051bf12583ee2b541284.png";

    /**
     * 过期退、随时退等 绿色的✔️
     */
    String CHECK_MARK_ICON_GREEN = "https://p0.meituan.net/dpnewvc/c2c506fecb3e5ece3560bf8044c959251537.png";

    /**
     * 过期退、随时退等 灰色的✔️
     */
    String CHECK_MARK_ICON_GRAY = "https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png";

    /**
     * 点评的精选商品的icon
     */
    String SELECTED_PRODUCT_ICON_DP = "https://p0.meituan.net/dprainbow/9f1cc30576071a61028285c85b54fb271230.png";

    /**
     * 美团的的精选商品的icon
     */
    String SELECTED_PRODUCT_ICON_MT = "https://p0.meituan.net/scarlett/4fda8b57b4fc7567fbef8237df661fd52042.png";

    /**
     * 团单主题给的结构化详情字段名
     */
    String ATTR_STRUCT_DETAIL = "attr_deal_struct_detail";


    // 秒杀角标
    String SEC_KILL_PROMO_ICON = "https://p0.meituan.net/travelcube/791144b80ea225a95b094f984fb065185985.png";

    String RANK_LABEL_NAME_KEY = "rank_label_name";

    String RANK_LABEL_LINK_KEY = "rank_label_link";
}
