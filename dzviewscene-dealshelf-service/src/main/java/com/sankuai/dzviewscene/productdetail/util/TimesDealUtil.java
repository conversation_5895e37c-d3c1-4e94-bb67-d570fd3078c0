package com.sankuai.dzviewscene.productdetail.util;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @author: created by hang.yu on 2024/1/24 15:13
 */
@Slf4j
public class TimesDealUtil {

    public static final String SYS_MULTI_SALE_NUMBER = "sys_multi_sale_number";

    public static final String TIMES_CARD_TIMES = "times_card_times";

    public static final String ONCE = "1";

    public static final String TIMES_DEAL_QUERY_FLAG = "timesDealQueryFlag";

    private static final String REFUND_INFO_EXPS_RESULT = "com.sankuai.dzviewscene.dealshelf.timescard.refund.info.optimize.exps";

    private static final String CREDIT_PAY_EXPS_RESULT = "com.sankuai.dzviewscene.dealshelf.timescard.credit.pay.exps";

    /**
     * 售卖类型key
     */
    private static final String CONTINUOUS_MONTHLY_TYPE_KEY = "continuousMonthlySubscription";
    
    /**
     * 连续包月类型值
     */
    private static final String CONTINUOUS_MONTHLY_TYPE_VALUE = "是";

    /**
     * 保价attr
     */
    public static final String ATTR_GUARANTEE_TAG_CODES = "attr_guaranteeTagCodes";

    /**
     * 先用后付标签attr
     */
    public static final String ATTR_CREDIT_PAY_PRE_AUTH_TAG = "attr_creditPayPreAuthTag";
    /**
     * 商品标签attr
     */
    public static final String ATTR_VALID_PRODUCT_TAG_IDS = "attr_validProductTagIds";

    /**
     * 判断是否为团购次卡
     */
    public static boolean isTimesDeal(DealDetailInfoModel dealDetailInfoModel) {
        return dealDetailInfoModel != null && Objects.equals(dealDetailInfoModel.getTradeType(), 19);
    }

    /**
     * 获取 团购次卡的标题
     */
    public static String getTimesTitle(List<AttrM> dealAttrs) {
        String times = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SYS_MULTI_SALE_NUMBER);
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return null;
        }
        return String.format("每次套餐详情（共%s次）", times);
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static String getSinglePrice(BigDecimal salePrice, String times) {
        if (salePrice == null) {
            return null;
        }
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return null;
        }
        // 取到手价/可用次数  向上取整
        try {
            return salePrice.divide(new BigDecimal(times), 2, RoundingMode.UP).stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return null;
        }
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static BigDecimal getProductSinglePrice(ProductM productM) {
        if (!productM.isTimesDeal()) {
            return null;
        }
        // 获取多次卡的次数
        String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return null;
        }
        try {
            return productM.getSalePrice().divide(new BigDecimal(times), 2, RoundingMode.UP);
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return null;
        }
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static BigDecimal getProductSinglePrice(ProductM productM, int times) {
        if (!productM.isTimesDeal()) {
            return productM.getSalePrice();
        }
        try {
            return productM.getSalePrice().divide(new BigDecimal(times), 2, RoundingMode.UP);
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return productM.getSalePrice();
        }
    }

    /**
     * 命中当前线上样式，即显示 总价/次数  单次价格
     */
    public static boolean hitTimesStyle1(ProductM timesDealProductM, String sk) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        // 当前样式做兜底
        if (StringUtils.isBlank(sk)) {
            return true;
        }
        return sk.contains("c");
    }

    /**
     * 命中次卡展示的新样式 即显示 单次￥单次价格  次数及总价
     */
    public static boolean hitTimesStyle2(ProductM timesDealProductM, String sk, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        if (StringUtils.isBlank(sk)) {
            return false;
        }
        if (sk.contains("a")) {
            return true;
        }
        // 命中实验b && (无价格锚点 || 多次卡单价发生倒挂）
        if (sk.contains("b")) {
            return CollectionUtils.isEmpty(timesDealProductM.getRelatedTimesDeal()) || !containsSingleTimeDeal(timesDealProductM, allProducts)
                    || timesDealHangUpDown(timesDealProductM, allProducts);
        }
        return false;
    }

    /**
     * 命中次卡展示的新样式 即显示 vs模块
     * 多次卡团单有价格锚点 且 不存在倒挂
     */
    public static boolean hitTimesStyle3(ProductM timesDealProductM, String sk, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        if (StringUtils.isBlank(sk)) {
            return false;
        }
        // 命中实验b && 有价格锚点 && 多次卡单价不发生倒挂
        return sk.contains("b")
                && CollectionUtils.isNotEmpty(timesDealProductM.getRelatedTimesDeal())
                && containsSingleTimeDeal(timesDealProductM, allProducts)
                && !timesDealHangUpDown(timesDealProductM, allProducts);
    }

    /**
     * 多次卡团单倒挂： 多次卡的单价 >= 关联单次卡的到手价
     */
    public static boolean timesDealHangUpDown(ProductM timesDealProductM, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        // 获取对应的单次卡
        ProductM singleTimeDeal = getRelatedSingleProductM(timesDealProductM, allProducts);
        ProductM multiTimesDeal = allProducts.get(timesDealProductM.getProductId());
        if (singleTimeDeal == null || multiTimesDeal == null) {
            return false;
        }
        BigDecimal productSinglePrice = getProductSinglePrice(multiTimesDeal);
        if (productSinglePrice == null || singleTimeDeal.getSalePrice() == null) {
            return false;
        }
        // 多次卡的单价 大于或等于 单次卡到手价。
        return productSinglePrice.compareTo(singleTimeDeal.getSalePrice()) >= 0;
    }

    public static boolean containsSingleTimeDeal(ProductM timesDealProductM, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        // 获取对应的单次卡
        ProductM singleTimeDeal = getRelatedSingleProductM(timesDealProductM, allProducts);
        return singleTimeDeal != null;
    }

    /**
     * 相似团购的团单发生倒挂
     * 当前团单的次数 > 关联团单的次数   当前的单次卡价格>=关联团单的单次卡价格就倒挂
     * 当前团单的次数 < 关联团单的次数   当前的单次卡价格<=关联团单的单次卡价格就倒挂
     * 当前团单的次数 = 关联团单的次数   当前的单次卡价格>=关联团单的单次卡价格就倒挂
     */
    public static boolean timesDealHangUpDown(ProductM currentProductM, ProductM relatedProductM) {
        int currentDealTimes = getDealTimes(currentProductM);
        int relatedDealTimes = getDealTimes(relatedProductM);
        BigDecimal currentSinglePrice = getProductSinglePrice(currentProductM, currentDealTimes);
        BigDecimal relatedSinglePrice = getProductSinglePrice(relatedProductM, relatedDealTimes);
        if (currentDealTimes > relatedDealTimes) {
            return currentSinglePrice.compareTo(relatedSinglePrice) >= 0;
        } else if (currentDealTimes < relatedDealTimes) {
            return currentSinglePrice.compareTo(relatedSinglePrice) <= 0;
        } else {
            return currentSinglePrice.compareTo(relatedSinglePrice) >= 0;
        }
    }

    public static int getDealTimes(ProductM productM) {
        // 非多次卡返回1
        if (!productM.isTimesDeal()) {
            return 1;
        }
        // 获取多次卡的次数
        String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        return NumberUtils.toInt(times, 1);
    }

    /**
     * 获取多次卡关联的单次卡团单
     */
    public static ProductM getRelatedSingleProductM(ProductM timesDealProductM, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return null;
        }
        // 获取对应的单次卡
        return timesDealProductM.getRelatedTimesDeal().stream().map(allProducts::get).filter(Objects::nonNull).filter(productM -> !productM.isTimesDeal()).findFirst().orElse(null);
    }

    /**
     * 获取先用后付斗斛实验结果,根据结果确定返回的具体文案
     */
    public static boolean creditPayDouHuResult(ActivityCxt context) {
        List<DouHuM> douHuMList = context.getSource(ShelfDouHuFetcher.CODE);
        List<String> expectResults = Lion.getList(Environment.getAppName(), CREDIT_PAY_EXPS_RESULT, String.class);
        return DouHuUtils.hitAnySk(douHuMList, expectResults) && LionConfigHelper.timesDealCreditPaySwitch();
    }

    /**
     * 获取斗斛实验结果,根据结果确定返回的具体文案
     */
    public static boolean timesCardExpressDouHuResult(ActivityCxt context) {
        List<DouHuM> douHuMList = context.getSource(ShelfDouHuFetcher.CODE);
        List<String> expectResults = Lion.getList(Environment.getAppName(), REFUND_INFO_EXPS_RESULT, String.class);
        return DouHuUtils.hitAnySk(douHuMList, expectResults) && LionConfigHelper.timesDealExpressOptimizeSwitch();
    }

    public static String genMultiTimesDealRichTitle(ActivityCxt context, ProductM productM, String unit) {
        String times = Optional.ofNullable(productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER))
                .orElse(StringUtils.EMPTY);
        String singlePrice = TimesDealUtil.getSinglePrice(productM.getSalePrice(), times);
        String salePrice = Optional.ofNullable(productM.getSalePrice()).map(BigDecimal::stripTrailingZeros)
                .map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
        if (timesCardExpressDouHuResult(context)) {
            // 新的逻辑
            productM.setExpressOptimize(true);
            return getResultMap(times, PromoTagTypeEnum.getDescByCode(productM.getPromoTagType()), singlePrice, unit,
                    salePrice);
        }
        return StringUtils.EMPTY;
    }

    public static String genSingleTimesDealRichTitle(ProductM productM, ActivityCxt context) {
        String singlePrice = Optional.ofNullable(productM.getSalePrice()).map(BigDecimal::stripTrailingZeros)
                .map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
        if (timesCardExpressDouHuResult(context)) {
            // 新的逻辑
            productM.setExpressOptimize(true);
            return getResultMap(singlePrice);
        }
        return StringUtils.EMPTY;
    }

    private static String getResultMap(String salePrice) {
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("title", "单次服务"); // 买几次 / 单独购买
        resultMap.put("symbol","¥");
        resultMap.put("salePrice",salePrice); // 到手价
        return com.dianping.vc.sdk.codec.JsonCodec.encodeWithUTF8(resultMap);
    }

    private static String getResultMap(String times,String priceTag,String perPrice,String unit,String salePrice) {
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("title",String.format("%s次卡",times)); // 买几次 / 单独购买
        resultMap.put("priceTag",priceTag); // 性价比高
        resultMap.put("symbol","¥");
        resultMap.put("perPrice",perPrice); // 单次价格
        resultMap.put("unit",unit); // /次 或者 /节
        resultMap.put("text","到手"); // 到手
        resultMap.put("salePrice",salePrice); // 到手价
        return com.dianping.vc.sdk.codec.JsonCodec.encodeWithUTF8(resultMap);
    }

    public static void setPricePromoTagFlag(List<ProductM> productMS, ActivityCxt activityCxt, List<String> expKeys) {
        try {
            if (CollectionUtils.isEmpty(productMS)) {
                return;
            }
            boolean hit = TimesDealUtil.timesCardExpressDouHuResult(activityCxt);
            if (!hit) {
                return;
            }
            // 性价比高标签
            productMS.stream().filter(Objects::nonNull).filter(ProductM::isTimesDealQueryFlag) // 必须是次卡召回的
                    .filter(ProductM::isTimesDeal) // 必须是团购次卡
                    .filter(productM -> productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER) != null)
                    .min(Comparator.comparingInt(o -> NumberUtils.toInt(o.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER),9999)))
                    .ifPresent(productM -> productM.setPromoTagType(PromoTagTypeEnum.COST_EFFECTIVENESS.getCode()));

            boolean hitCreditPay = TimesDealUtil.creditPayDouHuResult(activityCxt);
            if (!hitCreditPay) {
                return;
            }
            // 先用后付标签(优先级 > 性价比高)
            productMS.forEach(product -> {
                boolean supportCreditPay = ProductMAttrUtils.dealTimesCardSupportCreditPay(product, activityCxt);
                if (supportCreditPay) {
                    product.setPromoTagType(PromoTagTypeEnum.CREDIT_USE.getCode());
                }
            });

        } catch (Exception e) {
            log.error(String.format("setPricePromoTagFlag error,sceneCode: %s , parameter: %s",
                    activityCxt.getSceneCode(),
                    com.dianping.vc.sdk.codec.JsonCodec.encodeWithUTF8(activityCxt.getParameters())), e);
        }
    }

    /**
     * 是否是连续包月
     * @param productM
     * @return
     */
    public static boolean isContinuousMonthly(ProductM productM) {
        if (productM == null) {
            return false;
        }
        String multiSaleType = productM.getAttr(CONTINUOUS_MONTHLY_TYPE_KEY);
        return CONTINUOUS_MONTHLY_TYPE_VALUE.equals(multiSaleType);
    }

    /**
     * 判断是否是安心学次卡且支持先用后付
     *
     * @param productM 商品信息
     * @param afterPayTagList 先用后付标签列表
     * @param anXinTagCodes 安心学标签ID列表
     * @param anXinProductTagId 安心练的商品标签ID，选配。不配置优先走价保attr
     * @return 是否满足条件
     */
    public static boolean isAfterPayAnXinXueTimeCard(ProductM productM, List<String> afterPayTagList,
                                                     List<Integer> anXinTagCodes, long anXinProductTagId) {
        if (productM == null) {
            return false;
        }
        // 条件1：当前商品可以使用先用后付
        boolean isAfterPayProduct = canUseAfterPay(productM, afterPayTagList);
        // 条件2：当前团购为次卡
        boolean isTimeCard = productM.getProductType() == ProductTypeEnum.TIME_CARD.getType() || productM.isTimesDeal();
        // 条件3：当前团购为安心学团购
        boolean isAnXinXue = hasAnXinTag(productM, anXinTagCodes, anXinProductTagId);
        return isAfterPayProduct && isTimeCard && isAnXinXue;
    }

    /**
     * 当前商品可以使用先用后付
     */
    private static boolean canUseAfterPay(ProductM productM, List<String> afterPayTagList) {
        if (CollectionUtils.isEmpty(afterPayTagList)) {
            return false;
        }
        String creditPayPreAuthTag = productM.getAttr(ATTR_CREDIT_PAY_PRE_AUTH_TAG);
        if (StringUtils.isBlank(creditPayPreAuthTag)) {
            return false;
        }
        return afterPayTagList.contains(creditPayPreAuthTag);
    }

    /**
     * 检查商品是否有安心标签
     *
     * @param productM 商品信息
     * @param anXinTagCodes 安心标签ID列表
     * @Param anXinProductTagId 安心练的商品标签ID，选配。不配置优先走价保attr
     * @return 是否有安心标签
     */
    private static boolean hasAnXinTag(ProductM productM, List<Integer> anXinTagCodes, long anXinProductTagId) {
        if (CollectionUtils.isEmpty(anXinTagCodes)) {
            return hasAnXinTagAsProductTag(productM, anXinProductTagId);
        }
        String json = productM.getAttr(ATTR_GUARANTEE_TAG_CODES);
        if (StringUtils.isBlank(json)) {
            return false;
        }
        List<Integer> tagCodes = JsonCodec.converseList(json, Integer.class);
        if (CollectionUtils.isEmpty(tagCodes)) {
            return false;
        }
        return tagCodes.stream().anyMatch(anXinTagCodes::contains);
    }

    private static boolean hasAnXinTagAsProductTag(ProductM productM, long anXinProductTagId) {
        if (anXinProductTagId == 0L || StringUtils.isBlank(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS))) {
            return false;
        }
        List<Long> productTagIds = JsonCodec.converseList(productM.getAttr(ATTR_VALID_PRODUCT_TAG_IDS), Long.class);
        return productTagIds.contains(anXinProductTagId);
    }

}
