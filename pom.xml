<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.8.5.4</version>
    </parent>
    <groupId>com.sankuai.mdp</groupId>
    <artifactId>dzviewscene-dealshelf-home</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>dzviewscene-dealshelf-home</name>
    <modules>
        <module>dzviewscene-dealshelf-service</module>
        <module>dzviewscene-dealshelf-api</module>
    </modules>
    <properties>
        <orika-core.version>1.5.4</orika-core.version>
        <dztrade-common.version>1.9.4</dztrade-common.version>
        <gson.version>2.8.6</gson.version>
        <gateway-framework.version>3.1.5</gateway-framework.version>
        <merchant-common-filter.version>1.1.5</merchant-common-filter.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.nib.mkt</groupId>
                <artifactId>magic-member-degrade</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.nib.price.operation</groupId>
                <artifactId>price-operation-api</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.zdc</groupId>
                <artifactId>zdc-tag-apply-api</artifactId>
                <version>1.3.10</version>
            </dependency>
            <!--直播召回-->
            <dependency>
                <groupId>com.sankuai.mpcontent.feeds</groupId>
                <artifactId>mpcontent-feeds-thrift</artifactId>
                <version>0.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-resource-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpproduct.publish</groupId>
                <artifactId>mpproduct-publish-common</artifactId>
                <version>1.3.55</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.swan.udqs</groupId>
                <artifactId>Swan-udqs-api</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-meta-tag-manage-api</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmctexhibit</groupId>
                <artifactId>mpmctexhibit-query-thrift</artifactId>
                <version>0.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gateway-framework-client3</artifactId>
                <version>${gateway-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>bird-product-api</artifactId>
                <version>0.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>tpfun-refund-api</artifactId>
                <version>1.7.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>tpfun-checkout-api</artifactId>
                <version>0.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-privilege-user-api</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.10.8</version>
            </dependency>
            <dependency>
                <groupId>com.maoyan</groupId>
                <artifactId>maoyan-shplatform-content-api</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.maoyan</groupId>
                <artifactId>maoyan-show-common</artifactId>
                <version>2.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-stocklogic-api</artifactId>
                <version>1.1.24</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.joynav</groupId>
                <artifactId>joynav-rb-api</artifactId>
                <version>1.0.11</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>tpfun-product-api</artifactId>
                <version>3.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.adpseadbiz.bizer</groupId>
                <artifactId>bizer-client</artifactId>
                <version>0.0.7</version>
            </dependency>
            <!--美团商品广告-->
            <dependency>
                <groupId>com.meituan.adp.ads.fe</groupId>
                <artifactId>fe-client</artifactId>
                <version>1.0.35</version>
            </dependency>
            <!--美团搜索广告工具-->
            <dependency>
                <groupId>com.meituan.dataapp.ads</groupId>
                <artifactId>adsapilib</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>home-shop-fusion-api</artifactId>
                <version>1.1.49</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-category-process-api</artifactId>
                <version>1.2.52</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dzviewscene-productdisplay-api</artifactId>
                <version>0.0.31</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.ugccontent</groupId>
                        <artifactId>ugccontent-write-module-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-stock-api</artifactId>
                <version>1.0.12</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-solution-api</artifactId>
                <version>1.1.13</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.dzopen</groupId>
                <artifactId>dzopen-aggregate-api</artifactId>
                <version>1.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzcard</groupId>
                <artifactId>dzcard-navigation-api</artifactId>
                <version>0.0.28</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dztheme-generalproduct-api</artifactId>
                <version>1.0.40</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mdp</groupId>
                <artifactId>dztheme-massagebook-api</artifactId>
                <version>0.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-activity-api</artifactId>
                <version>2.2.72</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>hodor-api</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.user</groupId>
                <artifactId>account-api</artifactId>
                <version>1.3.11</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-activ-api</artifactId>
                <version>1.2.77</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.finance</groupId>
                <artifactId>xy-trade-installment-thrift-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>product-shelf-query-api</artifactId>
                <version>1.9.13</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>product-shelf-common</artifactId>
                <version>1.9.30</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-sdk-shopuuid</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>ch.hsr</groupId>
                <artifactId>geohash</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.dp.search</groupId>
                <artifactId>mainshop-client</artifactId>
                <version>1.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dp.arts</groupId>
                        <artifactId>arts-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>baby-operation-api</artifactId>
                <version>0.0.2.64</version>
            </dependency>
            <dependency>
                <groupId>com.dp.arts</groupId>
                <artifactId>arts-client</artifactId>
                <version>6.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dp.arts</groupId>
                <artifactId>arts-utils</artifactId>
                <version>3.2.7.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-ugc-nr</artifactId>
                <version>0.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzcard</groupId>
                <artifactId>joycard-navigation-api</artifactId>
                <version>0.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gateway-framework-web</artifactId>
                <version>${gateway-framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.mobile</groupId>
                        <artifactId>mapi-shell</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gateway-framework-common</artifactId>
                <version>${gateway-framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.mobile</groupId>
                        <artifactId>mapi-shell</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.dp</groupId>
                <artifactId>gm-marketing-member-card-api</artifactId>
                <version>0.1.87</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-product-nr</artifactId>
                <version>0.0.24</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.joy</groupId>
                        <artifactId>joy-common-helper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.tpfun</groupId>
                        <artifactId>sku-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.dp</groupId>
                        <artifactId>gm-bonus-exposure-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-test</artifactId>
                <version>0.0.19</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-mbop-bom</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.jvm.sandbox</groupId>
                        <artifactId>sandbox-runtime</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mbop</groupId>
                        <artifactId>mbop-sandbox-timeout-obtain</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mapi-abtest-component</artifactId>
                <version>1.2.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.mobile</groupId>
                        <artifactId>mapi-log-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping</groupId>
                        <artifactId>account-validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.hawk</groupId>
                        <artifactId>hawk-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.thoughtworks.xstream</groupId>
                        <artifactId>xstream</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.mobile</groupId>
                        <artifactId>mobile-api-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.appkit</groupId>
                        <artifactId>appkit-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mapi-log-util</artifactId>
                <version>2.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-validation-api</artifactId>
                <version>3.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-mtshoplist-api</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-shop-api</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.cloudstone</groupId>
                <artifactId>cloudstone-client</artifactId>
                <version>0.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-bcp-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-trade-sale-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.merchantcard</groupId>
                <artifactId>timescard-exposure-api</artifactId>
                <version>0.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-distance-common</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-acl-api</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-privilege-api</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mobile-base-datatypes</artifactId>
                <version>0.3.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.mobile</groupId>
                        <artifactId>mobile-api-base</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-gis-api</artifactId>
                <version>0.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-search-api</artifactId>
                <version>0.0.13</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.campaign</groupId>
                <artifactId>proxy-thrift</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.mms</groupId>
                        <artifactId>mms-boot</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.service.mobile</groupId>
                        <artifactId>mtthrift</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-shelfContext</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mtrace</groupId>
                        <artifactId>mtrace</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.midas</groupId>
                <artifactId>baymax-ad-bizer-api</artifactId>
                <version>1.6.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.platform</groupId>
                        <artifactId>platform-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.midas</groupId>
                <artifactId>baymax-mt-bizer-api-pigeon</artifactId>
                <version>0.0.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.platform</groupId>
                        <artifactId>platform-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>shop-api</artifactId>
                <version>0.5.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache</groupId>
                        <artifactId>libthrift</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dp.search</groupId>
                <artifactId>search-tohome-interface</artifactId>
                <version>0.1.21</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi.flow</groupId>
                <artifactId>poi-main-api</artifactId>
                <version>0.2.19</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-extend</artifactId>
                <version>1.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-display-api</artifactId>
                <version>0.3.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-stock-query-api</artifactId>
                <version>2.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-stock-display-api</artifactId>
                <version>2.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-base-api</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-privilege-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-attribute-api</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-cateproperty-api</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.merchant</groupId>
                <artifactId>merchant-product-common-api</artifactId>
                <version>0.0.28</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.dp</groupId>
                        <artifactId>data-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.maoyan.mtrace</groupId>
                <artifactId>mtrace-http</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-common</artifactId>
                <version>1.1.69</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pay-common</artifactId>
                <version>1.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.prometheus</groupId>
                <artifactId>deal-client</artifactId>
                <version>1.6.1.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>org.springframework.jms</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-asm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-sale-api</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-common-util</artifactId>
                <version>1.0.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-idmapper-api</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>geoinfo-api</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>carnation-biz-api</artifactId>
                <version>2.0.78</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.medicine</groupId>
                <artifactId>carnation-corepath-api</artifactId>
                <version>0.1.95</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wpt.user.merge</groupId>
                <artifactId>user-merge-query-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-product-api</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-proxy-fitness-api</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-order-api</artifactId>
                <version>1.9.14</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.dzopen</groupId>
                <artifactId>dzopen-gateway-api</artifactId>
                <version>2.0.19</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>dztrade-refund-api</artifactId>
                <version>0.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>dztrade-order-business-api</artifactId>
                <version>0.6.8</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>easylife-remote</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.dataapp.search.dealtag</groupId>
                <artifactId>searchdealtag-client</artifactId>
                <version>0.1.5</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.message</groupId>
                <artifactId>grouppoi</artifactId>
                <version>0.1.35</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.stid</groupId>
                <artifactId>stid</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.pay</groupId>
                <artifactId>pay-promo-display-octo-api</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>customerinfo-api</artifactId>
                <version>2.0.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>aqc-license-api</artifactId>
                <version>1.8.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.merchant</groupId>
                <artifactId>merchant-account-api</artifactId>
                <version>0.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.poi</groupId>
                <artifactId>sinai.client</artifactId>
                <version>3.1.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.meituan.scribe</groupId>
                        <artifactId>scribe-log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.scala-lang</groupId>
                        <artifactId>scala-library</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.service.mobile</groupId>
                        <artifactId>junglepoi.client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>junglepoi.client</artifactId>
                <version>1.0.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile.group</groupId>
                <artifactId>groupbase</artifactId>
                <version>0.7.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>search-adx-api</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-shoplist-api</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-trade-platform-api</artifactId>
                <version>1.0.28</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-feature-api</artifactId>
                <version>0.1.4</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>6.14.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-sales-common</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-interface</artifactId>
                <version>0.0.34</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>communitylife-api</artifactId>
                <version>0.8.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.poi</groupId>
                <artifactId>poi-relation-service-api</artifactId>
                <version>1.1.10</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-info-api</artifactId>
                <version>1.0.12</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-outer-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>merchant-member-api</artifactId>
                <version>2.0.20</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-data-api</artifactId>
                <version>2.2.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.poi</groupId>
                        <artifactId>poi-shopcateprop-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-operate-logger</artifactId>
                <version>0.0.26</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-merchant-activity-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>tp-deal-logic-api</artifactId>
                <version>1.2.8</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-marketing-nr</artifactId>
                <version>0.0.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-mq-client</artifactId>
                <version>0.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-plan-client</artifactId>
                <version>0.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>vc-deal-change-spi</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>deal-volume-query-api</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-pagoda-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tgc</groupId>
                <artifactId>tgc-base-remote</artifactId>
                <version>0.1.51</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.spurs</groupId>
                        <artifactId>spurs-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.hawk</groupId>
                        <artifactId>hawk-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-style-spi</artifactId>
                <version>1.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-testing-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.douhu</groupId>
                <artifactId>douhu-absdk</artifactId>
                <version>2.10.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.scala-lang</groupId>
                        <artifactId>scala-library</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>mafka-client_2.9</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.joynav</groupId>
                <artifactId>joynav-navigation-api</artifactId>
                <version>1.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-bom</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.gmkt</groupId>
                <artifactId>gmkt-common</artifactId>
                <version>1.1.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.tpfun</groupId>
                <artifactId>sku-underlayer-api</artifactId>
                <version>1.1.32</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-poi-nr</artifactId>
                <version>0.0.23</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-publish-category-api</artifactId>
                <version>1.1.5</version>
            </dependency>
            <!--货架关键词搜索-->
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-rule-api</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.vc</groupId>
                <artifactId>dztheme-deal-api</artifactId>
                <version>1.1.15</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>joy-general-api</artifactId>
                <version>0.0.68</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-tag-query-api</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>general-unified-search-api</artifactId>
                <version>1.7.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.haima</groupId>
                <artifactId>haima-client</artifactId>
                <version>1.1.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>jcl-over-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.cache</groupId>
                        <artifactId>redis-cluster-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.cache</groupId>
                <artifactId>redis-cluster-client</artifactId>
                <version>2.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gmm-investment-data-tool-api</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>athena-client</artifactId>
                <version>0.0.45</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-sdk</artifactId>
                <version>1.11.47</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mortbay.jetty</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>vc-operate-sdk</artifactId>
                <version>0.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-common-validation</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.struts</groupId>
                        <artifactId>struts2-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping</groupId>
                        <artifactId>avatar-dao</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.platform</groupId>
                        <artifactId>platform-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>account-utils</artifactId>
                <version>1.7.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.dianping.platform</groupId>
                        <artifactId>platform-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjlib</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pigeon-octo</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-client</artifactId>
                <version>0.5.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-common</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-consumerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.swallow</groupId>
                <artifactId>swallow-producerclient</artifactId>
                <version>0.8.11.9</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dzviewscene-productshelf-api</artifactId>
                <version>1.1.81</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.mobile</groupId>
                <artifactId>mapi-index-api</artifactId>
                <version>0.1.3</version>
            </dependency>
            <!--点评前台城市商圈接口-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gis-api</artifactId>
                <version>1.3.10</version>
            </dependency>
            <!--美团前台城市商圈接口-->
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>groupgeo</artifactId>
                <version>0.4.2.13</version>
            </dependency>
            <!--医美方案查询接口-->
            <dependency>
                <groupId>carnation-corepath-server</groupId>
                <artifactId>carnation-industry-api</artifactId>
                <version>0.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-guesslike-spi</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>dztheme-shop-api</artifactId>
                <version>0.0.96</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>beautycontent.creator.api</artifactId>
                <version>0.0.21</version>
            </dependency>
            <!--收藏-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>user-remote</artifactId>
                <version>2.3.19</version>
            </dependency>
            <!--ugc-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>review-api</artifactId>
                <version>5.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>user-base-remote</artifactId>
                <version>2.1.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>ugc-proxy-api</artifactId>
                <version>1.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.education</groupId>
                <artifactId>education-teacher-domain-api</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>education-api</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>education-admin-api</artifactId>
                <version>1.2.7</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>beauty-zone-common</artifactId>
                <version>1.1.28</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>beauty-zone-biz-api</artifactId>
                <version>1.1.43</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.beauty</groupId>
                <artifactId>beauty-zone-annotation</artifactId>
                <version>1.0.27</version>
            </dependency>
            <!--IM信息-->
            <dependency>
                <groupId>com.sankuai.dzim</groupId>
                <artifactId>cliententry-api</artifactId>
                <version>0.0.33</version>
            </dependency>
            <!--squirrel-async-client- -->
            <!--squirrel-async-client- -->
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>5.2.2.2.DP</version>
            </dependency>
            <!--商户详情-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>wed-business-api</artifactId>
                <version>1.0.3.57</version>
            </dependency>
            <!--商户头图-->
            <dependency>
                <groupId>com.sankuai.nibmp</groupId>
                <artifactId>decorate-media-query-thrift</artifactId>
                <version>1.24.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>health-sc-api-client</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.takeaway</groupId>
                <artifactId>takeaway-open-api</artifactId>
                <version>0.0.57</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sankuai.meituan</groupId>
                        <artifactId>mt-config-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!--交易后端api B端查询用-->
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-booking-process-api</artifactId>
                <version>1.1.20.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>apollo-brand-proposal-external-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mtstore</groupId>
                <artifactId>mtstore-core-thrift</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mtstore</groupId>
                <artifactId>mtstore-aggregate-thrift</artifactId>
                <version>0.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mtstore</groupId>
                <artifactId>mtstore-aggregate-common</artifactId>
                <version>0.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mppack.product</groupId>
                <artifactId>mppack-api-client</artifactId>
                <version>1.1.16</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.carnation</groupId>
                <artifactId>carnation-distribution-api</artifactId>
                <version>0.0.27</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.joy</groupId>
                <artifactId>joy-fitness-api</artifactId>
                <version>1.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.order.route</groupId>
                <artifactId>stenv-routing</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.dztrade</groupId>
                <artifactId>dztrade-common</artifactId>
                <version>${dztrade-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.3.3</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>jna</artifactId>
                <version>4.4.0-1</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>securesm</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.10.19</version>
            </dependency>
            <!--mockito-core-->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.8.0</version>
            </dependency>
            <!-- 团单类目ID转换 -->
            <dependency>
                <groupId>com.meituan.nibscp.domain</groupId>
                <artifactId>scp-accessory-utils</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.meituan.nibscp.common</groupId>
                        <artifactId>scp-common-utils</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <artifactId>dzviewscene-unified-shelf-operator-api</artifactId>
                <groupId>com.sankuai.dzviewscene</groupId>
                <version>0.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.merchant</groupId>
                <artifactId>merchant-common-filter</artifactId>
                <version>${merchant-common-filter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.medicalcosmetology</groupId>
                <artifactId>offline-code-api</artifactId>
                <version>1.0.37</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>intention-api</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>apollo-brand-proposal-external-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.ugccontent</groupId>
            <artifactId>ugccontent-write-module-api</artifactId>
            <version>0.9.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mapi-shell</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.tagservice</groupId>
            <artifactId>mpproduct-tagservice-cost-effective-sdk</artifactId>
            <version>1.0.1-RC1</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <!--sig spring aop，依赖 sig core 模块-->
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-adapter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-core</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.guardian</groupId>
            <artifactId>sig-botdefender-adapter-spring-aop</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-style-api</artifactId>
            <version>1.1.21</version>
        </dependency>
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-changelog-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <workspace>${project.basedir}/vc-deal-service</workspace>
                        <workspaceCodeStylesURL>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-eclipse-plugin/src/optional/eclipse-config/maven-styles.xml</workspaceCodeStylesURL>
                        <downloadSources>true</downloadSources>
                        <downloadJavadocs>false</downloadJavadocs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.12.2</version>
                    <configuration>
                        <showSuccess>false</showSuccess>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.societegenerale.commons</groupId>
                    <artifactId>arch-unit-maven-plugin</artifactId>
                    <version>2.3.0</version>
                    <executions>
                        <execution>
                            <phase>test</phase>
                            <goals>
                                <goal>arch-test</goal>
                            </goals>
                        </execution>
                    </executions>
                    <dependencies>
                        <dependency>
                            <groupId>com.sankuai</groupId>
                            <artifactId>athena-arch-rules</artifactId>
                            <version>0.0.4</version>
                            <scope>compile</scope>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <projectPath>${project.basedir}/dzviewscene-dealshelf-service/target</projectPath>
                        <rules>
                            <configurableRules>
                                <configurableRule>
                                    <rule>com.sankuai.athena.arch.ArchRules</rule>
                                    <applyOn>
                                        <packageName>com.sankuai.dzviewscene</packageName>
                                        <scope>main</scope>
                                    </applyOn>
                                    <checks>
                                        <check>CYCLOMATIC_COMPLEXITY_5_CHECKER</check>
                                        <check>LAYER_CHECKER</check>
                                    </checks>
                                </configurableRule>
                            </configurableRules>
                        </rules>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
