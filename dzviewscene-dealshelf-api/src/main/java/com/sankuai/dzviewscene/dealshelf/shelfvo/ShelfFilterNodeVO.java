/*
 * Create Author  : liya<PERSON><PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShelfFilterNodeVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfFilterNodeVO implements Serializable {
    /**
     * 筛选项文案，支持图标和文本
     */
    @MobileDo.MobileField
    private IconRichLabelModel title;

    /**
     * 子筛选项列表
     */
    @MobileDo.MobileField
    private List<ShelfFilterNodeVO> children;

    /**
     * 打点数据，json格式
     */
    @MobileDo.MobileField
    private String labs;

    /**
     * 子项筛选最少展示个数
     */
    @MobileDo.MobileField
    private int minShowNum;

    /**
     * 子项是否可多选
     */
    @MobileDo.MobileField
    private boolean multiSelect;

    /**
     * 是否选中
     */
    @MobileDo.MobileField
    private boolean selected;

    /**
     * 是否回显选中的叶节点
     */
    @MobileDo.MobileField
    private boolean echoSelectedLeafNode;

    /**
     * 扩展信息
     */
    @MobileDo.MobileField
    private String extra;

    /**
     * 筛选展示样式，控制子项样式
     */
    @MobileDo.MobileField
    private int showType;

    /**
     * 筛选项id
     */
    @MobileDo.MobileField
    private String filterId;

}
