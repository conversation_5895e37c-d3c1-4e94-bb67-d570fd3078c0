/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ShelfButtonVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfButtonVO implements Serializable {

    /**
     * 按钮置灰
     */
    @MobileDo.MobileField
    private boolean disable;

    /**
     * 类型，0-普通按钮，1-秒杀按钮
     */
    @MobileDo.MobileField
    private int type;

    /**
     * 按钮跳转链接
     */
    @MobileDo.MobileField
    private String jumpUrl;

    /**
     * 按钮名
     */
    @MobileDo.MobileField
    private String name;

    public ShelfButtonVO(){}

    public ShelfButtonVO(int type, boolean disable, String jumpUrl, String name) {
        this.type = type;
        this.disable = disable;
        this.jumpUrl = jumpUrl;
        this.name = name;
    }

}
