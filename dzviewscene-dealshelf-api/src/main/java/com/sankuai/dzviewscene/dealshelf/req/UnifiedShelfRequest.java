/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : UnifiedShelfRequest.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class UnifiedShelfRequest implements Serializable {

    /**
     * 网关层解析参数信息，比如用户信息，设备信息等
     * 别用继承，继承容易造成参数的冲突
     */
    private ShepherdGatewayParam gatewayParam;

    /**
     * 定位的关键字
     */
    private String searchkeyword;

    /**
     * 城市Id
     */
    private Integer cityid;

    /**
     * 用户定位城市id
     */
    private Integer locationcityid;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经纬度坐标类型，货架默认为GCJ02
     */
    private String coordType = "GCJ02";

    /**
     * 商户Id
     */
    private long shopid; // poiMigrate

    /**
     * 来自后端分配，这里仅对需要管理的 sceneCode 进行定义
     * @see UnifiedShelfSceneCodeEnum
     */
    private String sceneCode;

    /**
     * 平台 {@link com.dianping.vc.enums.VCClientTypeEnum}
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    private int platform;

    /**
     * 客户端类型：ios | android | harmony | 空字符串
     */
    private String client = "";

    /**
     * 版本号，客户端APP版本
     */
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    private String deviceId;

    /**
     * unionid
     */
    private String unionId;

    /**
     * 下挂商品Id 【新】
     * Ex：{"deal":"1,2,3","spu":"4,5,6"}
     * deal - 团单， spu - 泛商品
     * 解析工具如下：
     * {@link ParamUtil#getSummaryDealIds(java.lang.String, java.lang.String)}
     */
    private String summarypids;

    /**
     * 用户选中/飘红的商品，需要强制的置顶
     */
    private String anchorgoodid;

    /**
     * 上游商品类型
     */
    private String biztype;

    /**
     * 货架模块版本，由前端维护·
     */
    private int shelfversion;

    /**
     * MTSI反爬标识，其值来源于请求头，直接透传
     */
    private String mtsiflag;

    /**
     * 页面来源标记，用于在跳转链接上加来源标识后缀,前端直接传"mtlm=xxx"这种key+value样式的信息 需让前端先encode，后端会自动解析
     */
    private String pagesource;

    /**
     * 价格一致率透传加密字符串
     */
    private String pricecipher;

    /**
     * 猜喜侧传入商品，可能是到综商品、到餐商品
     */
    private String recommendinfo;

    private String position;

    private Integer pageindex;

    /**
     * regionId，到家set化用
     */
    private String wttregionid;

    private Integer pagesize;

    /**
     * 是否分页，1表示分页
     */
    private String pagination;

    /**
     * 自定义业务字段前后端交互信息，Map<String,String>格式的JSON字符串
     * key参考
     *
     * @see com.sankuai.dzviewscene.productshelf.vu.enums.DealShelfCustomInfoKeyEnum
     * value为前后端约定的具体值
     */
    private String custominfo;

    /**
     * 选择的导航ID
     */
    private long filterbtnid;

    /**
     * 筛选条件参数，用于多条件筛选，多个参数逗号分隔
     */
    private String filterparams;

    /**
     * 刷新标志
     */
    private String refreshtag;

    /**
     * 	扩展字段
     */
    private String extra;

    private String _shelf_debug;

    /**
     * 运营配置预览标识
     * {OperatorShelfSceneConfigDTO.source}#{OperatorShelfSceneConfigDTO.strategyUnit}#{id}
     */
    private List<String> operatorPreviewConfigTags;

    /**
     * 模拟的斗斛实验结果
     */
    private List<String> mockDouHuResult;

    /**
     * 渠道来源
     */
    private String channelType;
}
