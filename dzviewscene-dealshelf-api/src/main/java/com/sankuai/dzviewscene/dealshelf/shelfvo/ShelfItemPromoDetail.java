package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ShelfItemPromoDetail implements Serializable {
    /**
     * 优惠条目
     */
    @MobileDo.MobileField
    private List<PromoPerItemVO> promoItems;

    /**
     * 市场价
     */
    @MobileDo.MobileField
    private String marketPrice;

    /**
     * 总优惠金额
     */
    @MobileDo.MobileField
    private String totalPromoPrice;

    /**
     * 优惠弹窗标题
     */
    @MobileDo.MobileField
    private String title;

    /**
     * 到手价
     */
    @MobileDo.MobileField
    private String salePrice;

    /**
     * 总价旁优惠标签
     */
    @MobileDo.MobileField
    private String promoTag;
}