package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ShelfMainTitleVO implements Serializable {

    /**
     * 总商品数，这里的综商品数是一个展示字段，非逻辑判断字段。
     * 存在货架有数据但是总商品为零的情况，因为该场景可能要求强制不展示商品数量
     */
    @MobileDo.MobileField
    private int totalProductQty;

    @MobileDo.MobileField
    private String title;

    @MobileDo.MobileField
    private String icon;

    @MobileDo.MobileField
    private List<IconRichLabelModel> tags;

    @MobileDo.MobileField
    private SupernatantVO supernatantVO;
}
