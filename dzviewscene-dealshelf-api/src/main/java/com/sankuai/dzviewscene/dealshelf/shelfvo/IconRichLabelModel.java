/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : IconRichLabelModel.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class IconRichLabelModel implements Serializable {
    /**
     * 富文本
     */
    @MobileDo.MobileField
    private RichLabelModel text;

    /**
     * 图片
     */
    @MobileDo.MobileField
    private PictureModel icon;

    /**
     * 类型，0-文本，1-图片，2-icon+文本
     */
    @MobileDo.MobileField
    private int type;

}
