/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ShelfTagVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfTagVO implements Serializable {
    /**
     * 标签文案
     */
    @MobileDo.MobileField
    private RichLabelModel text;

    /**
     * 前置文案
     */
    @MobileDo.MobileField
    private RichLabelModel preText;

    /**
     * 埋点信息
     */
    @MobileDo.MobileField
    private String labs;

    /**
     * 前缀图片
     */
    @MobileDo.MobileField
    private PictureModel prePic;

    /**
     * 后缀图片
     */
    @MobileDo.MobileField
    private PictureModel afterPic;

    /**
     * 优惠明细
     */
    @MobileDo.MobileField
    private ShelfItemPromoDetail promoDetail;

    /**
     * 标签名
     */
    @MobileDo.MobileField
    private String name;

}
