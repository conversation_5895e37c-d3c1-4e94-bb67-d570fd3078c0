/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : UnifiedShelfResponse.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class UnifiedShelfResponse implements Serializable {
    /**
     * 筛选商品区
     */
    @MobileDo.MobileField
    private List<ShelfFilterProductAreaVO> filterIdAndProductAreas;

    /**
     * 货架导航
     */
    @MobileDo.MobileField
    private ShelfFilterVO filter;

    /**
     * 主标题
     */
    @MobileDo.MobileField
    private ShelfMainTitleVO mainTitle;

    /**
     * 货架场景code
     */
    @MobileDo.MobileField
    private String sceneCode;

    /**
     * 货架样式
     */
    @MobileDo.MobileField
    private int showType;

    /**
     * 上报埋点信息
     */
    @MobileDo.MobileField
    private ShelfOceanVO ocean;

    // 保留字段，业务请勿使用，只在结果返回的时候进行处理，处理一下 traceId 信息。
    private String traceId;

    // 保留字段，业务请勿使用，只在结果返回的时候进行处理
    private String retainMsg;

}
