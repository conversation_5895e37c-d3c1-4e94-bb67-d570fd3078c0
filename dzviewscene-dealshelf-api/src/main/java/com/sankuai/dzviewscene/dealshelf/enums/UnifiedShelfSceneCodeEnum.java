package com.sankuai.dzviewscene.dealshelf.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 主要管理POI页商户非通用货架的其他货架场景，如穿戴甲、零元单、代金券、秒杀等堆头类货架
 */
public enum UnifiedShelfSceneCodeEnum {

    LE_ZERO_RESERVE_UNIFIED_SHELF("le_zero_reserve_unified_shelf", "LE零元单货架"),
    BEAUTY_PRESSONNAILS_UNIFIED_SHELF("beauty_pressonnails_unified_shelf", "丽人穿戴甲"),
    VOUCHER_UNIFIED_SHELF("voucher_unified_shelf", "代金券货架"),

    BEAUTY_DEAL_RESERVE_UNIFIED_SHELF("beauty_deal_reserve_unified_shelf","丽人养发团购预订货架"),

    LIFE_WASH_RESERVE_UNIFIED_SHELF("life_wash_reserve_unified_shelf","生活服务洗涤家政预订货架"),
    RECYCLE_MINIPROGRAM_UNIFIED_SHELF("recycle_miniprogram_unified_shelf","回收小程序货架")
    ;

    private final String code;
    private final String desc;

    UnifiedShelfSceneCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static List<String> getScenes() {
        return Arrays.stream(UnifiedShelfSceneCodeEnum.values())
                .map(UnifiedShelfSceneCodeEnum::getCode)
                .collect(Collectors.toList());
    }
}
