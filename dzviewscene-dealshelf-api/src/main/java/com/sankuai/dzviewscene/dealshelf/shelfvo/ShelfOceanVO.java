/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ShelfOceanVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfOceanVO implements Serializable {
    /**
     * 商品标签打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO productItemTag;

    /**
     * 模块内的页面打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO pageView;

    /**
     * 活动优惠弹窗按钮打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO activityPromoPopViewButton;

    /**
     * 活动优惠弹窗关闭打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO activityPromoPopViewClose;

    /**
     * 活动优惠弹窗的打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO activityPromoPopView;

    /**
     * 优惠弹窗打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO promoPopView;

    /**
     * 商品打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO productItem;

    /**
     * 购买按钮打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO buyBtn;

    /**
     * 一级筛选打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO filterBar;

    /**
     * 二级筛选打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO childrenFilterBar;

    /**
     * 更多数据打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO more;

    /**
     * 整个货架打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO wholeShelf;

    /**
     * 子项展开收起打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO childrenCollapse;

    /**
     * spu卡片打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO spuItem;
    /**
     * 预订货架心智条打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO reserveMindBar;

    /**
     * 预订货架心智条浮层打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO reserveMindSupernatant;

    /**
     * 套餐卡片打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO skuItem;
    /**
     * 子项更多打点
     */
    @MobileDo.MobileField
    private ShelfOceanEntryVO childrenMore;
}
